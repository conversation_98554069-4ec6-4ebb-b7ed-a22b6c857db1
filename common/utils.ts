// 添加一个格式化毫秒时间的函数
// 示例：[00:00.00]
// 注意：这里传入的ms是毫秒，需要转换为秒
const formatMsToTime = (ms: number) => {
  // 四舍五入到最接近的10毫秒，避免精度问题
  const roundedMs = Math.round(ms / 10) * 10;
  const totalSeconds = Math.floor(roundedMs / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  const milliseconds = Math.floor((roundedMs % 1000) / 10); // 取前两位
  return `[${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}]`;
};

// 格式化秒为时间格式 [00:00.00] 分:秒.秒的小数
const formatSecondsToTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  // 处理整数和小数部分，避免浮点数精度问题
  const integerPart = Math.floor(remainingSeconds);
  const decimalPart = Math.round((remainingSeconds - integerPart) * 100);

  return `[${minutes.toString().padStart(2, '0')}:${integerPart.toString().padStart(2, '0')}.${decimalPart.toString().padStart(2, '0')}]`;
};

import defaultImg from './lens_default.png';

export { formatMsToTime, defaultImg, formatSecondsToTime };
