import axios from 'axios';

// 全局事件派发器，用于在拦截器中触发登录弹窗
type LoginEventListener = () => void;
const loginEventListeners: LoginEventListener[] = [];

// 添加登录事件监听器
export const addLoginEventListener = (listener: LoginEventListener) => {
  loginEventListeners.push(listener);
};

// 移除登录事件监听器
export const removeLoginEventListener = (listener: LoginEventListener) => {
  const index = loginEventListeners.indexOf(listener);
  if (index !== -1) {
    loginEventListeners.splice(index, 1);
  }
};

// 触发登录事件
const triggerLoginEvent = () => {
  loginEventListeners.forEach(listener => listener());
};

// 登录函数
const login = (phone: string, code: string) => {
  return axios
    .post('/lens/phone_login', {
      phone,
      code,
    })
    .then(res => {
      console.log('登录', res);
      return res;
    });
};

// 获取验证码
const getCode = (phone: string) => {
  return axios
    .post('/lens/send_sms_code', {
      phone,
    })
    .then(res => {
      console.log(res);
      return res;
    });
};

const req = axios.create({
  baseURL: '/api',
  withCredentials: true,
  timeout: 15000,
});

let isLoginRetrying = false; // 防止重复登录
const MAX_RETRY_TIMES = 1; // 最大重试次数
let retryCount = 0;

// 添加响应拦截器
req.interceptors.response.use(
  response => response,
  async error => {
    // 判断是否是登录态失效
    const isTMEOAUnauthorized = error.response?.status === 401 || error.response?.data?.error_code === 401;
    const isPhoneUnauthorized = error.response?.status === 403 || error.response?.data?.error_code === 403;
    if (isTMEOAUnauthorized) {
      location.reload();
    }

    if (isPhoneUnauthorized) {
      isLoginRetrying = true;
      retryCount++;

      try {
        // 执行登录流程
        // 这里需要实现一个自动重新登录的逻辑，可能需要提示用户输入验证码
        // 触发登录弹窗
        triggerLoginEvent();

        // 返回一个永不resolve的Promise，这样既不会触发then，也不会触发catch
        // 请求被"挂起"，等待用户登录后再次发起请求
        console.log('登录弹窗已触发，请求被挂起');
        return new Promise(() => {
          // 这个Promise永远不会resolve或reject
          // 所以不会触发后续的then或catch回调
        });
      } catch (loginError) {
        isLoginRetrying = false;
        console.error('重新登录失败', loginError);
        // 可以在这里添加提示用户的逻辑
        return Promise.reject(loginError);
      }
    }

    return Promise.reject(error);
  }
);

// 导出实例供其他模块使用
export default req;

// 导出登录相关函数
export { login, getCode };
