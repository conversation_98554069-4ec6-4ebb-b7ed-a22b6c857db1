// 颜色变量
@primary-color: #00FAFF;
@text-color: #fff;
@bg-color: #0A0F17;
@green-bg-color: #16282C;
@green-border-color: #29666B;
@green-slt-color: #00FF9A;
@green-text-color: #6AFFA3;
@gray-color: #3C485B;
@gray-color-2: #29313E;

// Mantine 主题变量
@mantine-body-bg: @bg-color; // Mantine body 背景色


// 间距变量
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;

// 字体大小
@font-size-sm: 12px;
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-xl: 20px;

// 断点
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;


:root {
  --mantine-color-dark-5: #4E5E77;
  --mantine-color-dark-6: @bg-color;
  --mantine-color-dark-7: @gray-color-2;
  --mantine-color-dark-8: @gray-color;
  --mantine-color-blue-8: #3C6F5C;
  --green-text-color: @green-text-color;
  --mantine-primary-color-filled: rgba(255, 255, 255, 0.8);
  // --checkbox-color: @green-slt-color;
  // --mantine-color-blue-filled: @green-slt-color !important;

}