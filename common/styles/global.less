@import './variables.less';
@import './mixins.less';

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif;
  font-size: @font-size-base;
  line-height: 1.5;
  color: @text-color;
  background-color: @bg-color;

}

ul {
  list-style: none;
}

button {
  border: none;
  outline: none;
  background-color: transparent;
}

// 通用样式类
.text-center {
  text-align: center;
}

.full-width {
  width: 100%;
}

.cursor-pointer {
  cursor: pointer;
}

// 间距工具类
.mt-sm {
  margin-top: @spacing-sm;
}

.mt-md {
  margin-top: @spacing-md;
}

.mt-lg {
  margin-top: @spacing-lg;
}

.mb-sm {
  margin-bottom: @spacing-sm;
}

.mb-md {
  margin-bottom: @spacing-md;
}

.mb-lg {
  margin-bottom: @spacing-lg;
}

:global {

  .mantine-TextInput-label {
    margin-bottom: 5px;
  }

  .mantine-AppShell-header {
    background: @bg-color;
    border-bottom: none;
  }

  .mantine-SegmentedControl-root {
    background: none;
    border: none;
    gap: 16px;
  }

  .mantine-SegmentedControl-control {
    background-color: #3C485B;

    border-radius: 8px;
  }


  .mantine-SegmentedControl-label {
    font-weight: 500;
    background-color: #0A0F17;
    height: 48px;
    padding: 13px 32px;
    // line-height: 48px;

    display: flex;
    align-items: center;
    justify-content: center;
    color: #FCFEFF;

    &[data-active="true"] {
      background: #3C485B;
    }
  }

  .mantine-SegmentedControl-innerLabel {
    line-height: 100%;
  }



}