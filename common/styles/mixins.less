// 弹性布局
.flex-center() {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between() {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

// 文本省略
.text-ellipsis() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// 多行文本省略
.multi-line-ellipsis(@line: 2) {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: @line;
    overflow: hidden;
}

// 响应式媒体查询
.responsive(@min-width) {
    @media screen and (min-width: @min-width) {
        @content();
    }
}