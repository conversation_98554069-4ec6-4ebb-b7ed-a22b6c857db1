const { execSync } = require("child_process");
const hint =
  "\n\u001b[31m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[32m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[33m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[94m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[37m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[36m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[90m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[35m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[31m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[32m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[33m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[94m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[37m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[36m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[90m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n\u001b[35m请使用 yarn, 不再支持 npm/tnpm install。\u001b[39m\n";

if (process.env.npm_execpath && process.env.npm_execpath.indexOf("yarn") === -1) {
  console.log("npm_execpath:" + process.env.npm_execpath + `(${typeof process.env.npm_execpath})`);
  console.log(hint);
  process.exit(1);
}

execSync("sh ./scripts/preinstall.sh", {
  stdio: "inherit",
});