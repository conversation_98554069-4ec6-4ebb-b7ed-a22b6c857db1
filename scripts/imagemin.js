import imagemin from "imagemin";
import imageminMozjpeg from "imagemin-mozjpeg";
import imageminPngquant from "imagemin-pngquant";
import imageminWebp from "imagemin-webp";
import path from "path";

const projects = ["lens-editor", "main-page"]; // 所有项目列表

(async () => {
  for (const project of projects) {
    console.log(`\nProcessing images for ${project}...`);

    const files = await imagemin([`projects/${project}/assets/images/**/*.{jpg,png}`], {
      destination: `projects/${project}/assets/images/compressed`,
      plugins: [
        // JPEG 压缩配置
        imageminMozjpeg({
          quality: 75, // 压缩质量
          progressive: true, // 渐进式加载
        }),
        // PNG 压缩配置
        imageminPngquant({
          quality: [0.6, 0.8], // 压缩质量范围
          speed: 4, // 压缩速度（1-11，越小质量越好，速度越慢）
        }),
        // WebP 转换配置
        imageminWebp({
          quality: 75, // 压缩质量
          method: 6, // 压缩方法（0-6，越大压缩率越高，速度越慢）
        }),
      ],
    });

    if (files.length > 0) {
      console.log(`Images optimized for ${project}:`, files.length);
      files.forEach((file) => {
        console.log("Compressed:", path.relative(process.cwd(), file.destinationPath));
      });
    } else {
      console.log(`No images found in ${project}`);
    }
  }
})().catch((error) => {
  console.error("Error occurred:", error);
  process.exit(1);
});
