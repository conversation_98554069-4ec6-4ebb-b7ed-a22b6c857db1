module.exports = {
  react: ['https://y.qq.com/lib/commercial/h5/react-18.2.0.min.js?max_age=2592000', 'React'],
  'react-dom': ['https://y.qq.com/lib/commercial/h5/react-dom-18.2.0.min.js?max_age=2592000', 'ReactDOM'],
  'pixi.js': ['https://game.kg.qq.com/tmegames/common/pixi/umd@6.4.2/pixi.production.min.js?tbundle=dll_pixi', 'PIXI'],
  'pixi-spine': [
    'https://game.kg.qq.com/tmegames/common/pixi-spine/umd@3.1.0/pixi-spine.min.js?tbundle=dll_pixi_spine',
    'PIXI.spine',
  ],
  babylonjs: [
    'https://game.kg.qq.com/tmegames/common/babylonjs/umd@5.9.0/babylon.production.min.js?tbundle=dll_babylon',
    'BABYLON',
  ],
  'babylonjs-loaders': [
    'https://game.kg.qq.com/tmegames/common/babylonjs/umd@5.9.0/babylon.glTF2FileLoader.min.js?tbundle=dll_babylon',
    'LOADERS',
  ],
  '@babylonjs/core': [
    'https://game.kg.qq.com/tmegames/common/babylonjs/umd@5.9.0/babylon.production.min.js?tbundle=dll_babylon',
    'BABYLON',
  ],
  '@babylonjs/loaders': [
    'https://game.kg.qq.com/tmegames/common/babylonjs/umd@5.9.0/babylon.glTF2FileLoader.min.js?tbundle=dll_babylon',
    'LOADERS',
  ],
  '@babylonjs/gui': [
    'https://game.kg.qq.com/tmegames/common/babylonjs/umd@5.9.0/babylon.gui.min.js?tbundle=dll_babylon_gui',
    'BABYLON.GUI',
  ],
  '@tencent/tme-interaction-common': [
    'https://y.qq.com/lib/interaction/h5/interaction-common-1.5.min.js?max_age=604800',
    'tmeInteractionCommon',
  ],
  '@tencent/tme-interaction-component': [
    'https://y.qq.com/lib/interaction/h5/interaction-component-1.4.min.js?max_age=604800',
    'tmeInteractionComponent',
  ],
  '@tencent/tme-interaction-music-client': [
    '//y.qq.com/lib/interaction/h5/tme-bridge-1.4.min.js?max_age=604800',
    'qqmusicBridge',
  ],
  MuxFrameAnimation: [
    'https://y.gtimg.cn/mediastyle//mod/mobile/js/muxFrameAnimation-1.0.5.min.js?max_age=604800',
    'MuxFrameAnimation',
  ],
};
