4月8日，二期改版：

1. 左边栏功能开发，支持跟时间轴进行双向联动. 1d
2. 时间轴修改，样式调整，增加支持拖动调序，自动智能排列，空置填充区域. 1.5d

3. 上传图片，风格解析. 0.5d
4. 故事灵感增加风格选项. 0.5d
5. 移除场景步骤及相关部分字段，修改相关关联逻辑. 0.5d
10. 步骤流程逻辑修改，相关状态管理修改。 1d
6. 角色管理改到主题创建环节. 0.5d
7. 所有涉及文本的地方，增加自动生成功能. 0.5d
8. 角色修改，关联图片重绘逻辑. 0.5d
9. 素材库模块开发。1d
10. 视频编辑页面整体逻辑串联和调整。 1d
11. 场景弹框，分镜弹框，视频生成弹框 相关调整。 0.5d
12. 右边栏调整 0.5d

---

角色：
1. 步骤一中，角色列表展示全部角色get_script_info：vecRoleInfo，如果字段iUse为true，则勾选，否则不勾选。后续步骤中，分镜中编辑角色只会展示已勾选的角色，未勾选的角色，不会出现在分镜的角色可选择列表中。

2. 步骤一中，新建角色弹框，展示角色相关填写字段，用户点击生成角色EM_EDIT_ROLE_TYPE_AI_GEN，则生成预览图，如果用户不满意，可以重复点生成EM_EDIT_ROLE_TYPE_AI_GEN，直到满意，满意后，用户可点击 确认创建，调用后台接口EM_EDIT_ROLE_TYPE_CONFIRM，创建角色。

3. 步骤一中，点击修改角色，弹框展示角色相关字段，用户可修改，修改后，需要用户重新点击生成EM_EDIT_ROLE_TYPE_AI_GEN，生成预览图。如果角色的iUseStoryboardCnt字段大于0，则需要展示"该角色涉及iUseStoryboardCnt个分镜需要重绘"，如果重新生成角色图，则需要展示 “一键应用” 按钮，点击后，调用 EM_EDIT_ROLE_TYPE_CHANGE_ROLE 接口，传入旧角色id和新角色id，进行替换。如果iUseStoryboardCnt等于0，则展示 “确认修改” 按钮，点击后调用EM_EDIT_ROLE_TYPE_CONFIRM。
如果用户没有点重新生成，则后续按钮不展示。

4. 步骤二中，分镜中角色模块，点击添加角色，弹框展示新建角色弹框，用户点击生成EM_EDIT_ROLE_TYPE_AI_GEN，生成角色图片，用户点击确认创建EM_EDIT_ROLE_TYPE_CONFIRM，则添加该角色到当前分镜EM_EDIT_STORYBOARD_TYPE_ROLEID，同时脚本角色列表中增加该角色。这个地方也会展示 角色列表tab， 用户可以从角色列表直接选择多个角色添加进该分镜EM_EDIT_STORYBOARD_TYPE_ROLEID。角色列表是当前脚本中被选中的角色的列表，不包含未被选中的角色。

5. 步骤二中，分镜中角色模块，点击修改角色，弹框展示修改角色弹框，用户点击生成EM_EDIT_ROLE_TYPE_AI_GEN，生成角色图片，用户点击确认修改EM_EDIT_ROLE_TYPE_CHANGE_ROLE，则修改当前分镜中的角色，同时脚本角色列表中修改该角色。这个地方不会展示角色列表tab，只能修改当前角色信息。

6. 调用生成角色预览图后EM_EDIT_ROLE_TYPE_AI_GEN，之前是调用get_script_info轮询接口获取角色生成状态，现在不再使用get_script_info，而是改成调用get_role_info接口轮询，该接口传入脚本id和EM_EDIT_ROLE_TYPE_AI_GEN返回的角色id，轮询接口返回角色信息。

7. 角色勾选/取消勾选的API接口是edit_role，然后参数中的枚举值是 emEditType 中的 EM_EDIT_ROLE_TYPE_CHOOSE_ROLE_LIST  = 66,   // 选取当然脚本使用哪些角色，不管是勾选还是取消，每次操作后就把当前最终勾选的传过去即可实现勾选和取消的功能。
        EM_EDIT_ROLE_TYPE_DELETE_ROLE  = 67,   // 删除角色
        所有api接口协议参见jce文件，
        iUse字段不能修改，是只读的，设置后，后台会返回。
        修改角色时，不需要传递和修改iUseStoryboardCnt字段，获取的话，可以从角色信息RoleInfo结构体中获取这个字段。


        轮询接口的协议：
            // 获取角色信息
    const string CMD_GET_ROLE_INFO = "interact.lens_script.svr.get_role_info";

            // CMD_GET_ROLE_INFO
    struct GetRoleInfoReq{
        0 optional string strScriptId;                              // 脚本ID
        1 optional string strRoleId;                                    // 角色 ID
    };
    struct GetRoleInfoRsp{
        0 optional proto_lens_script_comm::RoleInfo stRoleInfo;         // 角色信息
    };

    再次提醒：所有api接口协议都可以在jce文件中找到，你需要仔细阅读，不要忽略我附加的文件中的任何内容。


    AI文案：
    接口和field：
    角色：edit_role
	field =         EM_EDIT_ROLE_TYPE_GEN_ROLE_TIPS     = 63,   // 生成角色描述
场景标题：edit_scene
	field=EM_EDIT_SCEN_TYPE_GEN_TITLE          = 30,  // 生成场景标题-同步接口
分镜文案： edit_storyboard
	filed= EM_EDIT_STORYBOARD_TYPE_GEN_STORY          = 57,  // 生成分镜故事-同步接口