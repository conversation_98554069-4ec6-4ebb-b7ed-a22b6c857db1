module.exports = {
  plugins: {
    "postcss-px-to-viewport": {
      // 视窗的宽度，对应的是我们设计稿的宽度
      viewportWidth: 1920,
      // 视窗的高度，对应的是我们设计稿的高度（可以不配置）
      viewportHeight: 1080,
      // 指定`px`转换为视窗单位值的小数位数（很多时候无法整除）
      unitPrecision: 5,
      // 指定需要转换成的视窗单位，建议使用vw
      viewportUnit: "vw",
      // 指定不转换为视窗单位的类名
      selectorBlackList: [".ignore", ".hairlines"],
      // 小于或等于`1px`不转换为视窗单位
      minPixelValue: 1,
      // 允许在媒体查询中转换`px`
      mediaQuery: false,
      // 排除node_modules目录下的文件
      exclude: [/node_modules/],
    },
  },
};
