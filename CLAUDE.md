# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 常用开发命令

### 开发环境
- `yarn dev` - 启动开发服务器 (端口 30000)
- `yarn dev:proxy` - 启动带代理的开发环境
- `yarn dev:us` - 使用universal-build启动开发环境

### 构建和部署
- `yarn build` - 构建项目生产版本
- `yarn build:analyze` - 构建并生成bundle分析报告
- `yarn build:us` - 使用universal-build构建

### 代码质量检查
- `yarn tscheck` - TypeScript类型检查
- `yarn lint` - ESLint代码检查 (projects目录下的ts/tsx文件)
- `yarn lint:fix` - 自动修复ESLint错误
- `yarn stylelint` - CSS/Less样式检查并自动修复

### JCE相关 (后端接口协议)
- `yarn jce` - 更新JCE接口定义
- `yarn interact-jce-cli` - 更新interact相关JCE
- `yarn across-jce-cli` - 更新across相关JCE
- `yarn ark-jce-cli` - 更新ark相关JCE

## 项目架构

### 项目结构
- `projects/lens-editor/` - 主要的React应用 (棱镜视频编辑器)
- `projects/main-page/` - 主页面项目
- `common/` - 公共工具和样式
- `jce/` - JCE接口协议定义文件
- `scripts/` - 构建和工具脚本

### 技术栈
- **前端框架**: React 18 + TypeScript
- **状态管理**: Zustand
- **UI组件库**: Mantine + Antd
- **路由**: React Router DOM v6
- **样式**: Less + CSS Modules
- **构建工具**: TME Build (基于Webpack)
- **视频处理**: @webav/av-canvas, @webav/av-cliper
- **时间轴编辑**: @xzdarcy/react-timeline-editor
- **拖拽**: @hello-pangea/dnd

### 核心组件架构

#### 状态管理 (projects/lens-editor/store/)
- `editorStore.ts` - 主编辑器状态管理，包含步骤流程、脚本信息、轮询逻辑
- `timelineStore.ts` - 时间轴相关状态
- `homeStore.ts` - 首页状态
- `userInfoStore.ts` - 用户信息状态
- `apiActions.ts` - API调用封装

#### 主要页面组件
- `pages/Create/` - 视频创作主页面
- `pages/Home/` - 首页
- `pages/UserInfo/` - 用户信息页

#### 核心功能组件
- `components/VideoEditor/` - 视频编辑器核心组件
  - `VideoEditorIndex.tsx` - 主编辑器入口
  - `TimelineEditor.tsx` - 时间轴编辑器
  - `StoryboardTab.tsx` - 分镜头编辑
  - `MaterialLibraryModal.tsx` - 素材库
  - `RoleManager.tsx` - 角色管理
- `components/Subject/` - 主题相关组件
- `components/VideoEditor/processors/` - 视频处理逻辑

### 业务流程
1. **主题创建**: 选择风格、配置基础信息
2. **角色管理**: 创建/编辑/选择角色，支持AI生成
3. **分镜编辑**: 编辑分镜故事、配置角色和场景
4. **时间轴编辑**: 精确控制视频时间线
5. **视频生成**: 生成最终视频产品

### JCE接口系统
- 所有后端API通过JCE协议定义
- 接口协议文件位于 `jce/interact/aigc/` 目录
- 使用 `emEditType` 枚举定义不同的编辑操作类型
- 支持轮询机制获取生成状态

### 开发注意事项
- 使用Zustand进行状态管理，避免prop drilling
- 组件采用函数式组件 + Hooks模式
- 异步操作使用轮询机制检查生成状态
- 视频处理采用客户端处理 + 服务端生成结合
- 所有API接口都有对应的JCE协议定义，修改接口时需要同步更新JCE文件

### 路径别名配置
- `projects/` - 项目目录
- `common/` - 公共工具
- `jce/*.jce?emit=module` - JCE模块导入