import { useEffect, useRef, useCallback } from 'react';
import { useTimelineStore } from '../store/timelineStore';
import { useEditorStore } from '../store/editorStore';

/**
 * 智能时间轴更新Hook
 * 只在真正有变化时才进行必要的更新，避免全量重新渲染
 */
export function useSmartTimelineUpdate() {
  const scriptInfo = useEditorStore(state => state.scriptInfo);
  const scriptId = useEditorStore(state => state.scriptId); // 获取scriptId
  const avCanvas = useTimelineStore(state => state.avCanvas);
  const avCanvasRef = useRef(avCanvas);
  const scriptInfoRef = useRef(scriptInfo);
  const prevScriptIdRef = useRef<string | null>(null); // 保存上一次的scriptId
  const {
    loadTimelineData,
    updateVideoTrack,
    updateAudioTrack,
    updateSubtitleTrack,
    setPrevScriptInfo,
    resetTimelineStore,
  } = useTimelineStore();

  // 用于跟踪上次处理的状态
  const lastProcessedRef = useRef<{
    storyboardsHash: string;
    audioHash: string;
    subtitleHash: string;
    hasInitialized: boolean;
  }>({
    storyboardsHash: '',
    audioHash: '',
    subtitleHash: '',
    hasInitialized: false,
  });

  // 生成数据的哈希值，用于快速比较
  // 合并后的函数，同时支持普通数据和分镜数据
  const generateHash = useCallback((data: any, isStoryboard: boolean = false): string => {
    if (!data) return '';

    if (isStoryboard && Array.isArray(data)) {
      return data
        .map(sb => ({
          id: sb.strId,
          videoUrl: sb.stCurVideo?.strVideo,
          videoStatus: sb.stCurVideo?.eStatus,
          pictureUrl: sb.stCurPic?.strPicUrl,
          pictureStatus: sb.stCurPic?.eStatus,
          startTime: sb.iStartTime,
          timeRange: sb.iTimeRange,
          startTs: sb.stCurVideo?.iStartTs,
          endTs: sb.stCurVideo?.iEndTs,
        }))
        .map(item => JSON.stringify(item))
        .join('|');
    }

    // 对于字幕数据，进行特殊处理
    if (data && data.vecContent && Array.isArray(data.vecContent)) {
      // 这是字幕数据，提取关键信息生成精确的哈希
      return data.vecContent
        .map((track: any) => ({
          trackId: track.strId,
          trackType: track.eType,
          contents:
            track.vecContent?.map((item: any) => ({
              id: item.strId,
              content: item.strContent,
              startTime: item.iStartTimeMs,
              stopTime: item.iStopTimeMs,
            })) || [],
        }))
        .map((track: any) => JSON.stringify(track))
        .join('|');
    }

    // 对于其他数据，使用完整的JSON字符串而不是截取
    return JSON.stringify(data);
  }, []);

  // 监听scriptId变化，当scriptId变化时，重置时间轴状态
  useEffect(() => {
    if (prevScriptIdRef.current && prevScriptIdRef.current !== scriptId) {
      console.log(`[scriptId变化] 从 ${prevScriptIdRef.current} 到 ${scriptId}，重置时间轴状态`);

      // 重置时间轴存储
      resetTimelineStore();

      // 重置处理状态
      lastProcessedRef.current = {
        storyboardsHash: '',
        audioHash: '',
        subtitleHash: '',
        hasInitialized: false,
      };
    }

    // 更新上一次的scriptId
    prevScriptIdRef.current = scriptId;
  }, [scriptId, resetTimelineStore]);

  // 更新ref值
  useEffect(() => {
    scriptInfoRef.current = scriptInfo;
  }, [scriptInfo]);

  // 智能更新逻辑
  const smartUpdate = useCallback(async () => {
    if (!scriptInfo || !avCanvasRef.current) return;

    const currentState = lastProcessedRef.current;

    // 生成当前数据的哈希值
    const currentStoryboardHash = generateHash(scriptInfo.VecNewStoryboard || [], true);
    const currentAudioHash = generateHash(scriptInfo.stAudio);
    const currentSubtitleHash = generateHash(scriptInfo.stSubtitle);

    console.log('[SMART UPDATE DEBUG] 检查数据变化:', {
      storyboard: currentStoryboardHash !== currentState.storyboardsHash,
      audio: currentAudioHash !== currentState.audioHash,
      subtitle: currentSubtitleHash !== currentState.subtitleHash,
      hasInitialized: currentState.hasInitialized,
      分镜数量: scriptInfo.VecNewStoryboard?.length || 0,
      时间戳: new Date().toISOString(),
    });

    // 如果是首次初始化，进行全量加载
    if (!currentState.hasInitialized && scriptInfo.VecNewStoryboard?.length) {
      console.log('[SMART UPDATE DEBUG] 首次初始化，进行全量加载');
      await loadTimelineData();

      // 更新状态
      lastProcessedRef.current = {
        storyboardsHash: currentStoryboardHash,
        audioHash: currentAudioHash,
        subtitleHash: currentSubtitleHash,
        hasInitialized: true,
      };
      return;
    }

    // 如果已经初始化，进行精细化更新
    let hasChanges = false;

    // 检查视频轨道变化
    if (currentStoryboardHash !== currentState.storyboardsHash) {
      console.log('[SMART UPDATE DEBUG] 分镜数据发生变化，更新视频轨道');
      console.log('[SMART UPDATE DEBUG] 哈希变化:', {
        旧哈希: currentState.storyboardsHash.substring(0, 50) + '...',
        新哈希: currentStoryboardHash.substring(0, 50) + '...',
      });

      // 检查是否是首次有分镜数据（从无到有）
      if (!currentState.storyboardsHash && scriptInfo.VecNewStoryboard?.length) {
        // 首次有分镜数据，需要初始化整个时间轴
        console.log('[SMART UPDATE DEBUG] 首次有分镜数据，初始化整个时间轴');
        await loadTimelineData();
      } else if (scriptInfo.VecNewStoryboard?.length) {
        // 精细化更新视频轨道
        console.log('[SMART UPDATE DEBUG] 精细化更新视频轨道');
        await updateVideoTrack(scriptInfo.VecNewStoryboard);
      }

      hasChanges = true;
    }

    // 检查音频轨道变化
    if (currentAudioHash !== currentState.audioHash && scriptInfo.stAudio) {
      console.log('[SMART UPDATE DEBUG] 音频数据发生变化，更新音频轨道');
      await updateAudioTrack(scriptInfo.stAudio);
      hasChanges = true;
    }

    // 检查字幕轨道变化
    if (currentSubtitleHash !== currentState.subtitleHash && scriptInfo.stSubtitle) {
      console.log('[SMART UPDATE DEBUG] 字幕数据发生变化，更新字幕轨道');
      await updateSubtitleTrack(scriptInfo.stSubtitle);
      hasChanges = true;
    }

    // 如果有变化，更新缓存的状态
    if (hasChanges) {
      console.log('[SMART UPDATE DEBUG] 有变化，更新缓存状态');
      lastProcessedRef.current = {
        storyboardsHash: currentStoryboardHash,
        audioHash: currentAudioHash,
        subtitleHash: currentSubtitleHash,
        hasInitialized: true,
      };

      // 更新 prevScriptInfo 以供下次比较
      setPrevScriptInfo(JSON.parse(JSON.stringify(scriptInfo)));
    } else {
      console.log('[SMART UPDATE DEBUG] 数据无实质变化，跳过更新');
    }
  }, [
    scriptInfo,
    loadTimelineData,
    updateVideoTrack,
    updateAudioTrack,
    updateSubtitleTrack,
    generateHash,
    setPrevScriptInfo,
  ]);

  // 监听关键数据变化
  useEffect(() => {
    smartUpdate();
  }, [smartUpdate]);
  useEffect(() => {
    avCanvasRef.current = avCanvas;
  }, [avCanvas]);
  // 提供手动触发更新的方法
  const forceUpdateTimeline = useCallback(async () => {
    console.log('[FORCE UPDATE DEBUG] 开始强制更新时间轴', scriptInfoRef.current ? '有scriptInfo' : '无scriptInfo');

    // 保存当前的scriptId，用于防止竞态条件
    const currentScriptId = scriptId;

    // 如果没有avCanvas，等待它初始化
    if (!avCanvasRef.current) {
      console.log('[FORCE UPDATE DEBUG] 等待avCanvas初始化');
      // 最多等待3秒
      for (let i = 0; i < 6; i++) {
        await new Promise(resolve => setTimeout(resolve, 500));
        if (avCanvasRef.current) break;

        // 检查scriptId是否变化，如果变化则取消操作
        if (currentScriptId !== useEditorStore.getState().scriptId) {
          console.log('[FORCE UPDATE DEBUG] scriptId已变化，取消更新');
          return;
        }
      }

      // 如果还是没有avCanvas，则放弃更新
      if (!avCanvasRef.current) {
        console.log('[FORCE UPDATE DEBUG] avCanvas初始化超时，放弃更新');
        return;
      }
    }

    // 获取最新的scriptInfo
    const latestScriptInfo = useEditorStore.getState().scriptInfo;
    if (!latestScriptInfo || !latestScriptInfo.VecNewStoryboard?.length) {
      console.log('[FORCE UPDATE DEBUG] 没有有效的scriptInfo或分镜数据，放弃更新');
      return;
    }

    // 更新scriptInfoRef
    scriptInfoRef.current = latestScriptInfo;

    console.log('[FORCE UPDATE DEBUG] 强制更新时间轴，分镜数量:', latestScriptInfo.VecNewStoryboard.length);
    lastProcessedRef.current.hasInitialized = false;

    console.log('[FORCE UPDATE DEBUG] 开始全量加载时间轴数据');
    try {
      await loadTimelineData();

      // 再次检查scriptId是否变化，如果变化则取消后续操作
      if (currentScriptId !== useEditorStore.getState().scriptId) {
        console.log('[FORCE UPDATE DEBUG] 加载数据后scriptId已变化，取消更新状态');
        return;
      }

      // 更新状态
      const currentStoryboardHash = generateHash(scriptInfoRef.current.VecNewStoryboard || [], true);
      const currentAudioHash = generateHash(scriptInfoRef.current.stAudio);
      const currentSubtitleHash = generateHash(scriptInfoRef.current.stSubtitle);

      lastProcessedRef.current = {
        storyboardsHash: currentStoryboardHash,
        audioHash: currentAudioHash,
        subtitleHash: currentSubtitleHash,
        hasInitialized: true,
      };

      // 更新 prevScriptInfo 以供下次比较
      setPrevScriptInfo(JSON.parse(JSON.stringify(scriptInfoRef.current)));

      console.log('[FORCE UPDATE DEBUG] 时间轴更新完成');
    } catch (error) {
      console.error('[FORCE UPDATE DEBUG] 更新时间轴出错:', error);
    }
  }, [loadTimelineData, generateHash, setPrevScriptInfo, scriptId]);

  // 提供单独更新特定分镜的方法
  const updateSingleStoryboard = useCallback(
    async (storyboardId: string) => {
      if (!scriptInfo?.VecNewStoryboard || !avCanvasRef.current) return;

      const storyboard = scriptInfo.VecNewStoryboard.find((sb: any) => sb.strId === storyboardId);
      if (!storyboard) return;

      console.log('[SmartTimelineUpdate] 更新单个分镜:', storyboardId);

      const { updateSingleVideoAction } = useTimelineStore.getState();
      await updateSingleVideoAction(storyboardId, storyboard);

      // 更新哈希值以防止重复更新
      const currentStoryboardHash = generateHash(scriptInfo.VecNewStoryboard, true);
      lastProcessedRef.current.storyboardsHash = currentStoryboardHash;
    },
    [scriptInfo, generateHash]
  );

  return {
    forceUpdateTimeline,
    updateSingleStoryboard,
    hasInitialized: lastProcessedRef.current.hasInitialized,
  };
}
