import { useCallback, useEffect, useMemo } from 'react';
import { $StoryboardInfo, emStatus } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { SelectionState } from '../components/VideoEditor/types';

interface UseStoryboardNavigationProps {
  allStoryboards: $StoryboardInfo[];
  selectionState: SelectionState;
  updateSelectionState: (updates: Partial<SelectionState>) => void;
  setExpandedScenes: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  storyboardRefs: React.MutableRefObject<Record<string, HTMLDivElement | null>>;
}

export function useStoryboardNavigation({
  allStoryboards,
  selectionState,
  updateSelectionState,
  setExpandedScenes,
  storyboardRefs,
}: UseStoryboardNavigationProps) {
  // 检查所有分镜是否都已生成完毕
  const areAllStoryboardsReady = useMemo(() => {
    if (!allStoryboards.length) return false;

    return allStoryboards.every(storyboard => {
      // 检查分镜整体状态
      const storyboardReady = storyboard.eStatus === emStatus.EM_STATUS_SUCC;

      // 检查图片是否生成完毕
      const pictureReady = storyboard.stCurPic?.eStatus === emStatus.EM_STATUS_SUCC;

      // 分镜必须整体成功且有图片
      return storyboardReady && pictureReady;
    });
  }, [allStoryboards]);

  // 切换到上一个分镜
  const selectPreviousStoryboard = useCallback(() => {
    if (!allStoryboards.length) return;

    const currentIndex = selectionState.selectedStoryboard
      ? allStoryboards.findIndex(sb => sb.strId === selectionState.selectedStoryboard?.strId)
      : -1;

    const previousIndex = currentIndex <= 0 ? allStoryboards.length - 1 : currentIndex - 1;
    const previousStoryboard = allStoryboards[previousIndex];

    if (previousStoryboard) {
      updateSelectionState({
        selectedStoryboard: previousStoryboard,
      });

      // 展开对应的场景
      if (previousStoryboard.strSceneId) {
        setExpandedScenes(prev => ({
          ...prev,
          [previousStoryboard.strSceneId]: true,
        }));
      }

      // 滚动到对应的分镜
      setTimeout(() => {
        const storyboardElement = storyboardRefs.current[previousStoryboard.strId];
        if (storyboardElement) {
          storyboardElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }, 100);
    }
  }, [allStoryboards, selectionState.selectedStoryboard, updateSelectionState, setExpandedScenes, storyboardRefs]);

  // 切换到下一个分镜
  const selectNextStoryboard = useCallback(() => {
    if (!allStoryboards.length) return;

    const currentIndex = selectionState.selectedStoryboard
      ? allStoryboards.findIndex(sb => sb.strId === selectionState.selectedStoryboard?.strId)
      : -1;

    const nextIndex = currentIndex >= allStoryboards.length - 1 ? 0 : currentIndex + 1;
    const nextStoryboard = allStoryboards[nextIndex];

    if (nextStoryboard) {
      updateSelectionState({
        selectedStoryboard: nextStoryboard,
      });

      // 展开对应的场景
      if (nextStoryboard.strSceneId) {
        setExpandedScenes(prev => ({
          ...prev,
          [nextStoryboard.strSceneId]: true,
        }));
      }

      // 滚动到对应的分镜
      setTimeout(() => {
        const storyboardElement = storyboardRefs.current[nextStoryboard.strId];
        if (storyboardElement) {
          storyboardElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }, 100);
    }
  }, [allStoryboards, selectionState.selectedStoryboard, updateSelectionState, setExpandedScenes, storyboardRefs]);

  // 键盘事件处理函数
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      //  只有当所有分镜都生成完毕时才允许键盘导航
      if (!areAllStoryboardsReady) {
        console.log('[Navigation] 键盘导航被禁用：还有分镜未生成完毕');
        return;
      }

      // 检查是否有输入框或其他表单元素处于焦点状态
      const activeElement = document.activeElement;
      const isInputFocused =
        activeElement &&
        (activeElement.tagName === 'INPUT' ||
          activeElement.tagName === 'TEXTAREA' ||
          (activeElement as HTMLElement).contentEditable === 'true' ||
          activeElement.getAttribute('role') === 'textbox');

      // 如果有输入框处于焦点状态，不处理箭头键
      if (isInputFocused) return;

      switch (event.key) {
        case 'ArrowUp':
        case 'ArrowLeft':
          event.preventDefault();
          selectPreviousStoryboard();
          break;
        case 'ArrowDown':
        case 'ArrowRight':
          event.preventDefault();
          selectNextStoryboard();
          break;
      }
    },
    [areAllStoryboardsReady, selectPreviousStoryboard, selectNextStoryboard]
  );

  // 添加键盘事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    selectPreviousStoryboard,
    selectNextStoryboard,
    handleKeyDown,
    areAllStoryboardsReady, // 暴露状态供其他组件使用
  };
}
