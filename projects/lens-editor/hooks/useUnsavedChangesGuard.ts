import { useEffect, useCallback } from 'react';
import { useNavigate, useLocation, NavigateFunction } from 'react-router-dom';
import { useEditorStore } from '../store/editorStore';
import { modals } from '@mantine/modals';

/**
 * 自定义钩子，用于在存在未保存的更改时拦截导航
 */
export function useUnsavedChangesGuard() {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPendingChanges, saveAllChanges } = useEditorStore();

  // 处理浏览器刷新/关闭
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasPendingChanges()) {
        // 标准方式，显示浏览器的确认对话框
        e.preventDefault();
        e.returnValue = '有未保存的更改，确定要离开吗？';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasPendingChanges]);

  // 创建拦截导航的函数
  const guardedNavigate = useCallback(
    (to: string, options?: any) => {
      if (hasPendingChanges()) {
        modals.openConfirmModal({
          title: '未保存的更改',
          children: '你有未保存的更改，离开页面将丢失这些更改。是否保存更改后再离开？',
          labels: { confirm: '保存并离开', cancel: '放弃更改' },
          onCancel: () => {
            // 用户选择放弃更改，直接导航
            navigate(to, options);
          },
          onConfirm: async () => {
            try {
              // 保存所有更改
              await saveAllChanges();
              // 保存成功后导航
              navigate(to, options);
            } catch (error) {
              console.error('保存更改失败:', error);
              // 可以显示错误通知
            }
          },
        });
      } else {
        // 没有未保存的更改，直接导航
        navigate(to, options);
      }
    },
    [navigate, hasPendingChanges, saveAllChanges]
  );

  return guardedNavigate;
}
