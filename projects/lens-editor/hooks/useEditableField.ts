import { useState, useRef, useCallback, useEffect } from 'react';

/**
 * 可编辑字段的选项接口
 */
interface UseEditableFieldOptions<T> {
  /** 字段的初始值 */
  initialValue: T;
  /** 当值发生变化需要保存时调用的函数 */
  onSave: (newValue: T) => void;
  /** 自定义比较函数，用于判断值是否变化，默认使用全等比较 */
  equalityFn?: (prev: T, next: T) => boolean;
  /** 是否启用防抖，默认为false */
  debounce?: boolean;
  /** 防抖时间（毫秒），默认为300ms */
  debounceTime?: number;
}

/**
 * 用于处理可编辑字段（如input, textarea等）的自定义Hook
 * 提供状态管理、值变更检测，并在必要时调用保存函数
 */
export function useEditableField<T>({
  initialValue,
  onSave,
  equalityFn = (a, b) => a === b,
  debounce = false,
  debounceTime = 300,
}: UseEditableFieldOptions<T>) {
  // 当前字段值状态
  const [value, setValue] = useState<T>(initialValue);

  // 使用两个独立的ref：一个存储真正的原始值，一个用于比较
  const trueOriginalValueRef = useRef<T>(initialValue); // 真正的初始值，永远不变
  const focusTimeValueRef = useRef<T>(initialValue); // 获取焦点时的值，用于比较

  // 防抖定时器引用
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 是否正在编辑的标记
  const isEditingRef = useRef<boolean>(false);

  // 当外部initialValue变化时更新内部状态
  useEffect(() => {
    setValue(initialValue);

    // 如果没有处于编辑状态，才更新原始值
    if (!isEditingRef.current) {
      console.log('[useEditableField][initialValue变化] 更新原始值:', initialValue);
      trueOriginalValueRef.current = initialValue;
      focusTimeValueRef.current = initialValue;
    }
  }, [initialValue]);

  // 当字段获取焦点时记录当前值
  const handleFocus = useCallback(() => {
    // 标记开始编辑
    isEditingRef.current = true;

    // 记录获取焦点时的值，用于后续比较
    focusTimeValueRef.current = value;
    console.log('[useEditableField][handleFocus] 记录焦点时的值:', value);
    console.log('[useEditableField][handleFocus] 真正的原始值:', trueOriginalValueRef.current);
  }, [value]);

  // 处理值的变化
  const handleChange = useCallback((newValue: T) => {
    setValue(newValue);
  }, []);

  // 保存值的函数，如果启用防抖，则会延迟调用
  const saveValue = useCallback(
    (newValue: T) => {
      // 如果是字符串类型且为空，不触发保存
      if (typeof newValue === 'string' && newValue.trim() === '') {
        console.log('[useEditableField][saveValue] 空值，跳过保存');
        return;
      }

      // 与焦点时的值进行比较，看是否需要保存
      const hasChangedFromFocus = !equalityFn(focusTimeValueRef.current, newValue);

      // 与真正原始值比较，用于日志记录和调试
      const hasChangedFromOriginal = !equalityFn(trueOriginalValueRef.current, newValue);

      console.log(
        '[useEditableField][saveValue] 检查值是否变化:',
        '真正的原始值:',
        trueOriginalValueRef.current,
        '焦点时的值:',
        focusTimeValueRef.current,
        '当前值:',
        newValue,
        '与焦点时比较是否变化:',
        hasChangedFromFocus,
        '与原始值比较是否变化:',
        hasChangedFromOriginal
      );

      if (hasChangedFromFocus) {
        console.log('[useEditableField][saveValue] 值已变化，准备调用onSave');
        if (debounce) {
          // 清除之前的定时器
          if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
          }

          // 设置新的定时器
          debounceTimerRef.current = setTimeout(() => {
            console.log('[useEditableField][saveValue] 防抖结束，调用onSave');
            onSave(newValue);
            debounceTimerRef.current = null;
          }, debounceTime);
        } else {
          console.log('[useEditableField][saveValue] 立即调用onSave');
          onSave(newValue);
        }
      } else {
        console.log('[useEditableField][saveValue] 值未变化，跳过onSave调用');
      }
    },
    [onSave, equalityFn, debounce, debounceTime]
  );

  // 当字段失去焦点时检查是否需要保存
  const handleBlur = useCallback(() => {
    console.log('[useEditableField][handleBlur] 触发:', value);

    // 调用保存逻辑
    saveValue(value);

    // 重置编辑状态
    isEditingRef.current = false;
  }, [value, saveValue]);

  // 重置为初始值
  const reset = useCallback(() => {
    setValue(initialValue);
    trueOriginalValueRef.current = initialValue;
    focusTimeValueRef.current = initialValue;
    isEditingRef.current = false;

    // 清除可能存在的防抖定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }
  }, [initialValue]);

  // 更新原始值参考点，用于在保存后重置脏状态
  const updateOriginalValue = useCallback((newValue: T) => {
    console.log('[useEditableField][updateOriginalValue] 更新原始值:', newValue);
    focusTimeValueRef.current = newValue;
  }, []);

  // 清理函数，在组件卸载时清除防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return {
    value,
    setValue,
    handleFocus,
    handleChange,
    handleBlur,
    reset,
    updateOriginalValue,
    // 提供给外部使用的检查值是否变化的方法
    isDirty: !equalityFn(focusTimeValueRef.current, value),
    // 暴露原始值引用，以便外部组件可以访问
    originalValueRef: focusTimeValueRef,
  };
}
