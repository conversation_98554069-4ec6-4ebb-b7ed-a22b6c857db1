import { useState, useCallback } from 'react';
import { notifications } from '@mantine/notifications';
import {
  $StoryboardInfo,
  $VideoInfo,
  emStatus,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { emEditType, emScriptGetMask } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { VideoGenerationConfig } from '../components/VideoEditor/VideoGenerationModal';
import { SelectionState } from '../components/VideoEditor/types';
import req from 'common/request';

interface UseVideoGenerationProps {
  scriptId: string;
  scriptInfo: any;
  selectionState: SelectionState;
  selectedMedia: $VideoInfo | null;
  videoTipsInput: string;
  updateSelectionState: (updates: Partial<SelectionState>) => void;
  updateSingleStoryboard: (storyboardId: string) => Promise<void>;
  regenerateVideo: (storyboard: $StoryboardInfo, video: $VideoInfo, videoTips: string, callback?: () => void) => void;
  startPolling: (
    eGetType: emScriptGetMask,
    onSuccess?: () => void,
    maxAttempts?: number,
    checkType?: 'video' | 'picture' | 'all-videos' | 'topic' | 'other'
  ) => void;
  scriptInfoRef: React.MutableRefObject<any>;
}

export function useVideoGeneration({
  scriptId,
  scriptInfo,
  selectionState,
  selectedMedia,
  videoTipsInput,
  updateSelectionState,
  updateSingleStoryboard,
  regenerateVideo,
  startPolling,
  scriptInfoRef,
}: UseVideoGenerationProps) {
  // 视频生成配置弹框状态
  const [isVideoGenerationModalOpen, setIsVideoGenerationModalOpen] = useState(false);
  const [videoGenerationMode, setVideoGenerationMode] = useState<'single' | 'batch'>('single');
  const [pendingVideoGeneration, setPendingVideoGeneration] = useState<{
    type: 'single' | 'batch';
    storyboard?: $StoryboardInfo;
    originalVideoTips?: string;
  } | null>(null);

  // 直接生成视频（不弹配置框）
  const regenerateVideoHandler = useCallback(() => {
    console.log('[regenerateVideo selectionState]:', selectionState, selectedMedia);

    // 安全检查
    if (!selectionState.selectedStoryboard || !selectedMedia) {
      notifications.show({
        title: '提示',
        message: '请先选择一个分镜',
        color: 'orange',
      });
      return;
    }

    // 使用hook处理视频生成
    regenerateVideo(selectionState.selectedStoryboard, selectedMedia, videoTipsInput, () => {
      // 轮询完成后检查最新状态并更新UI
      console.log('视频生成轮询完成，更新UI');

      // 获取最新的分镜信息
      const updatedScriptInfo = scriptInfoRef.current;
      if (updatedScriptInfo) {
        const latestStoryboard = updatedScriptInfo.VecNewStoryboard?.find(
          (sb: $StoryboardInfo) => sb.strId === selectionState.selectedStoryboard?.strId
        );

        if (latestStoryboard && latestStoryboard.stCurVideo) {
          console.log('找到最新分镜状态:', latestStoryboard);
          // 更新选择状态，确保UI正确反映最新状态
          updateSelectionState({
            selectedStoryboard: latestStoryboard,
          });
        }
      }
    });
  }, [selectionState, selectedMedia, videoTipsInput, regenerateVideo, scriptInfoRef, updateSelectionState]);

  // 打开视频生成配置弹框
  const regenerateVideoDialog = useCallback(() => {
    console.log('[regenerateVideo selectionState]:', selectionState, selectedMedia);

    // 安全检查
    if (!selectionState.selectedStoryboard || !selectedMedia) {
      notifications.show({
        title: '提示',
        message: '请先选择一个分镜',
        color: 'orange',
      });
      return;
    }

    // 设置待生成的视频信息并打开配置弹框
    setPendingVideoGeneration({
      type: 'single',
      storyboard: selectionState.selectedStoryboard,
      originalVideoTips: videoTipsInput,
    });
    setVideoGenerationMode('single');
    setIsVideoGenerationModalOpen(true);
  }, [selectionState, selectedMedia, videoTipsInput]);

  // 处理带配置的批量视频生成
  const generateAllVideosWithConfig = useCallback(
    (config: VideoGenerationConfig) => {
      if (!scriptInfo) {
        notifications.show({
          title: '错误',
          message: '脚本信息不存在',
          color: 'red',
        });
        return;
      }

      // 获取需要生成视频的分镜，并应用配置
      const storyboardsToGenerate = scriptInfo.VecNewStoryboard?.filter(
        (storyboard: $StoryboardInfo) =>
          !storyboard.stCurVideo ||
          !storyboard.stCurVideo.strVideo ||
          storyboard.stCurVideo.eStatus !== emStatus.EM_STATUS_SUCC
      );

      if (!storyboardsToGenerate || storyboardsToGenerate.length === 0) {
        notifications.show({
          title: '提示',
          message: '所有分镜视频已生成完成或无需生成。',
          color: 'green',
        });
        return;
      }

      // 为每个分镜应用配置
      const updatedStoryboards = storyboardsToGenerate.map((storyboard: $StoryboardInfo) => ({
        ...storyboard,
        stCurVideo: {
          ...(storyboard.stCurVideo || {}),
          strVideoModelId: config.modelId,
          strMovementId: config.movementId || '',
          strVideoTips: config.videoTips || '',
        } as $VideoInfo,
      }));

      // 调用批量生成API
      req
        .post('/lens_script/edit_storyboard', {
          strScriptId: scriptId,
          vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_VIDEO],
          stBoardInfo: updatedStoryboards,
        })
        .then((res: any) => {
          if (res.data.error_code !== 0 || res.data.data.iRes !== 0) {
            notifications.show({
              title: '批量生成视频任务提交失败',
              message: res.data.error_msg || '请稍后重试',
              color: 'red',
              autoClose: 5000,
            });
          } else {
            notifications.show({
              title: '正在批量生成视频',
              message: res.data.data.strTips || '视频正在生成中，请耐心等待',
              loading: true,
              autoClose: 5000,
              withCloseButton: false,
              id: 'all-videos-generation',
            });

            startPolling(
              emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL,
              () => {
                console.log('所有视频生成轮询完成，更新UI');
                notifications.update({
                  id: 'all-videos-generation',
                  title: '所有视频生成状态已更新',
                  message: '请查看各分镜的视频状态',
                  color: 'green',
                  loading: false,
                  autoClose: 5000,
                });
              },
              undefined,
              'all-videos'
            );
          }
        })
        .catch(error => {
          console.error('生成所有视频失败:', error);
          notifications.show({
            title: '生成所有视频失败',
            message: '请稍后重试',
            color: 'red',
            autoClose: 5000,
          });
        });
    },
    [scriptInfo, scriptId, startPolling]
  );

  // 处理视频生成配置确认
  const handleVideoGenerationConfirm = useCallback(
    (config: VideoGenerationConfig) => {
      if (!pendingVideoGeneration) return;

      if (pendingVideoGeneration.type === 'single') {
        // 单个视频生成
        if (!selectedMedia || !selectionState.selectedStoryboard) {
          notifications.show({
            title: '提示',
            message: '请先选择一个分镜',
            color: 'orange',
          });
          setIsVideoGenerationModalOpen(false);
          setPendingVideoGeneration(null);
          return;
        }

        // 使用配置信息更新视频参数
        const updatedVideo = {
          ...selectedMedia,
          strVideoModelId: config.modelId,
          strMovementId: config.movementId || '',
          strVideoTips: config.videoTips || '',
        };

        // 使用hook处理视频生成
        regenerateVideo(selectionState.selectedStoryboard, updatedVideo, config.videoTips || '', () => {
          // 轮询完成后检查最新状态并更新UI
          console.log('视频生成轮询完成，更新UI');

          // 获取最新的分镜信息
          const updatedScriptInfo = scriptInfoRef.current;
          if (updatedScriptInfo) {
            const latestStoryboard = updatedScriptInfo.VecNewStoryboard?.find(
              (sb: $StoryboardInfo) => sb.strId === selectionState.selectedStoryboard?.strId
            );

            if (latestStoryboard && latestStoryboard.stCurVideo) {
              console.log('找到最新分镜状态:', latestStoryboard);

              // 只更新变化的单个分镜，而不是整个时间轴
              updateSingleStoryboard(latestStoryboard.strId);

              // 更新选择状态，确保UI正确反映最新状态
              updateSelectionState({
                selectedStoryboard: latestStoryboard,
              });
            }
          }
        });
      } else {
        // 批量视频生成
        generateAllVideosWithConfig(config);
      }

      // 关闭弹框
      setIsVideoGenerationModalOpen(false);
      setPendingVideoGeneration(null);
    },
    [
      pendingVideoGeneration,
      selectedMedia,
      selectionState.selectedStoryboard,
      regenerateVideo,
      scriptInfoRef,
      updateSingleStoryboard,
      updateSelectionState,
      generateAllVideosWithConfig,
    ]
  );

  // 处理视频生成配置弹框关闭
  const handleVideoGenerationModalClose = useCallback(() => {
    setIsVideoGenerationModalOpen(false);
    setPendingVideoGeneration(null);
  }, []);

  return {
    // 状态
    isVideoGenerationModalOpen,
    videoGenerationMode,
    pendingVideoGeneration,

    // 方法
    regenerateVideoHandler,
    regenerateVideoDialog,
    handleVideoGenerationConfirm,
    handleVideoGenerationModalClose,
    generateAllVideosWithConfig,
  };
}
