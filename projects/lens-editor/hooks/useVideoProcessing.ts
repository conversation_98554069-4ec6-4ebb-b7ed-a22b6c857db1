import { useState, useCallback, useRef } from 'react';
import { MP4Clip, VisibleSprite } from '@webav/av-cliper';
import { $VideoInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { emStatus } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { emScriptGetMask, emEditType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { notifications } from '@mantine/notifications';
import req from 'common/request';
import { $WebScriptInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { useEditorStore } from '../store/editorStore';
import { useTimelineStore } from '../store/timelineStore';
import { TLActionWithName } from '../components/VideoEditor/types';
import { processVideoClip } from '../utils/videoUtils';

export function useVideoProcessing() {
  const {
    scriptInfo,
    scriptId,
    setScriptInfo,
    startPolling,
    isUpdatingClientSideClip,
    setIsUpdatingClientSideClip,
    hasVideoGenerating,
  } = useEditorStore();

  const { avCanvas, tlState, addToSpriteMap, getSpriteFromMap, getVideoClip } = useTimelineStore();

  // 状态管理
  const lastVideoUpdateRef = useRef<{
    videoId: string;
    startTs: number;
    endTs: number;
  } | null>(null);

  /**
   * 处理视频片段裁剪
   */
  const updateVideoSegment = useCallback(
    async (sprite: VisibleSprite, oldVideo: $VideoInfo, newVideo: $VideoInfo, action: TLActionWithName) => {
      if (isUpdatingClientSideClip) {
        console.log('正在处理客户端视频片段，稍候重试...');
        setTimeout(() => {
          updateVideoSegment(sprite, oldVideo, newVideo, action);
        }, 200);
        return;
      }

      setIsUpdatingClientSideClip(true);

      try {
        const currentClip = sprite.getClip() as MP4Clip;
        if (!currentClip) {
          setIsUpdatingClientSideClip(false);
          return;
        }

        const videoUrl = newVideo.strVideo.replace('http://', 'https://');

        // 使用统一缓存获取视频
        let videoClip: MP4Clip;
        try {
          videoClip = await getVideoClip(videoUrl);
        } catch (error) {
          console.error('获取视频流失败:', error);
          setIsUpdatingClientSideClip(false);
          return;
        }

        const readyPromise = videoClip.ready;
        const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('视频准备超时')), 5000));

        try {
          await Promise.race([readyPromise, timeoutPromise]);
        } catch (error) {
          console.error('视频准备失败:', error);
          setIsUpdatingClientSideClip(false);
          return;
        }

        const finalVideoClip = await processVideoClip(
          videoClip,
          newVideo.iStartTs,
          newVideo.iEndTs,
          newVideo.iTimeRange
        );

        await finalVideoClip.ready;

        const newSprite = new VisibleSprite(finalVideoClip);
        newSprite.time.offset = action.start * 1e6;
        newSprite.time.duration = (action.end - action.start) * 1e6;

        if (avCanvas) {
          let wasPlaying = false;
          try {
            wasPlaying = avCanvas.playing === true;
          } catch (e) {
            console.log('无法获取播放状态:', e);
          }

          if (wasPlaying) {
            avCanvas.pause();
          }

          avCanvas.removeSprite(sprite);

          await new Promise(resolve => setTimeout(resolve, 50));

          await avCanvas.addSprite(newSprite);

          addToSpriteMap(action, newSprite);

          // 更新action的原始时间戳，确保下次比较时能正确检测变化
          action.originalStartTs = newVideo.iStartTs;
          action.originalEndTs = newVideo.iEndTs;

          console.log('[更新视频片段]:', newVideo.iStartTs, newVideo.iEndTs);

          if (wasPlaying) {
            await new Promise(resolve => setTimeout(resolve, 50));

            if (tlState?.current) {
              const currentTime = tlState.current.getTime() * 1e6;
              avCanvas.play({ start: currentTime });
            } else {
              avCanvas.play();
            }
          }

          setTimeout(() => {
            try {
              sprite.destroy();
            } catch (e) {
              console.error('销毁旧sprite失败:', e);
            }
          }, 300);
        }

        updateScriptInfoWithNewVideo(oldVideo, newVideo);
      } catch (error: any) {
        console.error('更新视频片段失败:', error);
        notifications.show({
          title: '视频更新失败',
          message: error?.message || '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      } finally {
        setIsUpdatingClientSideClip(false);
      }
    },
    [isUpdatingClientSideClip, getVideoClip, avCanvas, tlState, addToSpriteMap]
  );

  /**
   * 更新视频时间点但不重新加载视频
   */
  const updateVideoTimePoints = useCallback(
    async (action: TLActionWithName) => {
      if (!scriptInfo) return;

      const storyboard = scriptInfo.VecNewStoryboard?.find(sb => sb.strId === action.id);
      if (!storyboard || !storyboard.stCurVideo) return;

      const originalStartTs = action.originalStartTs || 0;
      const newStartTs = action.newStartTs || 0;

      // 不再限制newEndTs在originalDuration范围内，允许使用我们计算的时间范围
      const newEndTs = action.newEndTs || 0;

      if (
        lastVideoUpdateRef.current &&
        lastVideoUpdateRef.current.videoId === storyboard.strId &&
        lastVideoUpdateRef.current.startTs === newStartTs &&
        lastVideoUpdateRef.current.endTs === newEndTs
      ) {
        console.log('[跳过重复更新]:', storyboard.strId, newStartTs, newEndTs);
        return;
      }

      lastVideoUpdateRef.current = {
        videoId: storyboard.strId,
        startTs: Math.round(newStartTs),
        endTs: Math.round(newEndTs),
      };

      const updatedVideo = {
        ...storyboard.stCurVideo,
        iStartTs: Math.round(newStartTs),
        iEndTs: Math.round(newEndTs),
      };

      const updatedStoryboard = {
        ...storyboard,
        stCurVideo: updatedVideo,
      };

      req
        .post('/lens_script/edit_storyboard', {
          strScriptId: scriptId,
          vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_EDIT_VIDEO_LENGTH],
          stBoardInfo: [updatedStoryboard],
        })
        .then(res => {
          console.log('[更新视频时间点结果]:', res);
        })
        .catch(error => {
          console.error('更新视频时间点失败:', error);
        });

      const sprite = getSpriteFromMap(action);
      if (sprite && (newStartTs !== originalStartTs || newEndTs !== (action.originalEndTs || 0))) {
        await updateVideoSegment(sprite, storyboard.stCurVideo, updatedVideo, action);
      }
    },
    [scriptInfo, scriptId, updateVideoSegment, getSpriteFromMap]
  );

  /**
   * 生成单个视频
   */
  const regenerateVideo = useCallback(
    (selectedStoryboard: any, selectedMedia: any, videoTipsInput: string, onSuccess?: () => void) => {
      if (!selectedMedia || !selectedStoryboard) {
        notifications.show({
          title: '提示',
          message: '请先选择一个分镜',
          color: 'orange',
        });
        return;
      }

      // 检查该分镜视频是否正在生成中
      if (selectedStoryboard.stCurVideo?.eStatus === emStatus.EM_STATUS_RUNNING) {
        notifications.show({
          title: '提示',
          message: '该分镜视频正在生成中，请稍候...',
          color: 'blue',
        });
        return;
      }

      const updatedSelectedVideo = {
        ...(selectedMedia as $VideoInfo),
        strVideoTips: videoTipsInput,
      };

      const updatedStoryboard = {
        ...selectedStoryboard,
        stCurVideo: updatedSelectedVideo,
      };

      req
        .post('/lens_script/edit_storyboard', {
          strScriptId: scriptId,
          vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_REGEN_VIDEO],
          stBoardInfo: [updatedStoryboard],
        })
        .then((res: any) => {
          if (res.data.error_code !== 0 || res.data.data.iRes !== 0) {
            notifications.show({
              title: '视频生成失败',
              message: res.data.error_msg || '请稍后重试',
              color: 'red',
            });
            // 状态由视频的eStatus自动管理，无需手动移除
          } else {
            notifications.show({
              title: '正在生成视频',
              message: res.data.data.strTips,
              loading: true,
              withCloseButton: false,
              id: 'video-generation',
            });

            const updatedStoryboardWithRunningStatus = {
              ...selectedStoryboard,
              stCurVideo: {
                ...updatedSelectedVideo,
                eStatus: emStatus.EM_STATUS_RUNNING,
              },
            };

            const updatedStoryboards = scriptInfo?.VecNewStoryboard.map(sb =>
              sb.strId === selectedStoryboard.strId ? updatedStoryboardWithRunningStatus : sb
            );

            setScriptInfo({
              ...scriptInfo,
              VecNewStoryboard: updatedStoryboards,
            } as $WebScriptInfo);

            startPolling(
              emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL,
              () => {
                console.log('视频生成轮询完成，更新UI');
                if (onSuccess) onSuccess();
              },
              undefined,
              'video'
            );
          }
        })
        .catch(error => {
          console.error('生成视频失败:', error);

          notifications.update({
            id: 'video-generation',
            title: '视频生成失败',
            message: '请稍后重试',
            color: 'red',
            loading: false,
            autoClose: 5000,
          });
          // 状态由视频的eStatus自动管理，无需手动移除
        });
    },
    [scriptId, scriptInfo, setScriptInfo, startPolling]
  );

  /**
   * 生成剩余全部视频
   */
  const generateAllVideos = useCallback(() => {
    // 检查是否有视频正在生成中
    if (hasVideoGenerating()) {
      notifications.show({
        title: '提示',
        message: '有视频正在生成中，请稍候...',
        color: 'blue',
      });
      return;
    }

    // 立即设置生成状态，提供即时的用户反馈
    const { setGeneratingAllVideos } = useEditorStore.getState();
    setGeneratingAllVideos(true);

    if (!scriptInfo) {
      setGeneratingAllVideos(false);
      notifications.show({
        title: '错误',
        message: '脚本信息不存在',
        color: 'red',
      });
      return;
    }

    // 筛选需要生成视频的分镜（未成功且未在生成中）
    const storyboardsToPotentiallyGenerate = scriptInfo.VecNewStoryboard?.filter(
      storyboard =>
        (!storyboard.stCurVideo ||
          !storyboard.stCurVideo.strVideo ||
          storyboard.stCurVideo.eStatus !== emStatus.EM_STATUS_SUCC) &&
        storyboard.stCurVideo?.eStatus !== emStatus.EM_STATUS_RUNNING
    );

    if (!storyboardsToPotentiallyGenerate || storyboardsToPotentiallyGenerate.length === 0) {
      setGeneratingAllVideos(false);
      notifications.show({
        title: '提示',
        message: '所有分镜视频已生成完成或无需生成。',
        color: 'green',
      });
      return;
    }

    req
      .post('/lens_script/edit_storyboard', {
        strScriptId: scriptId,
        vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_VIDEO],
        stBoardInfo: storyboardsToPotentiallyGenerate || [],
      })
      .then((res: any) => {
        if (res.data.error_code !== 0 || res.data.data.iRes !== 0) {
          // API 失败时重置状态
          setGeneratingAllVideos(false);
          notifications.show({
            title: '批量生成视频任务提交失败',
            message: res.data.error_msg || '请稍后重试',
            color: 'red',
            autoClose: 5000,
          });
        } else {
          notifications.show({
            title: '正在批量生成视频',
            message: res.data.data.strTips || '视频正在生成中，请耐心等待',
            loading: true,
            autoClose: 5000,
            withCloseButton: false,
            id: 'all-videos-generation',
          });

          startPolling(
            emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL,
            () => {
              console.log('所有视频生成轮询完成，更新UI');
              // 轮询完成时重置状态
              setGeneratingAllVideos(false);
              notifications.update({
                id: 'all-videos-generation',
                title: '所有视频生成状态已更新',
                message: '请查看各分镜的视频状态',
                color: 'green',
                loading: false,
                autoClose: 5000,
              });

              // 如果生产视频的分镜数量大于60，则在完成后刷新页面
              if (storyboardsToPotentiallyGenerate.length > 60) {
                window.location.reload();
              }
            },
            undefined,
            'all-videos'
          );
        }
      })
      .catch(error => {
        console.error('生成所有视频失败:', error);
        // 网络错误或异常时重置状态
        setGeneratingAllVideos(false);
        notifications.update({
          id: 'all-videos-generation',
          title: '生成所有视频失败',
          message: '请稍后重试',
          color: 'red',
          loading: false,
          autoClose: 5000,
        });
      });
  }, [hasVideoGenerating, scriptInfo, scriptId, startPolling]);

  /**
   * 更新scriptInfo中的视频信息
   */
  const updateScriptInfoWithNewVideo = useCallback(
    (oldVideo: $VideoInfo, newVideo: $VideoInfo) => {
      if (!scriptInfo) return;

      const storyboard = scriptInfo.VecNewStoryboard?.find(sb => {
        return sb.stCurVideo && sb.stCurVideo.strId === oldVideo.strId;
      });

      if (storyboard) {
        const updatedStoryboard = {
          ...storyboard,
          stCurVideo: newVideo,
        };

        const updatedStoryboards = scriptInfo.VecNewStoryboard.map(sb =>
          sb.strId === storyboard.strId ? updatedStoryboard : sb
        );

        setScriptInfo({
          ...scriptInfo,
          VecNewStoryboard: updatedStoryboards,
        } as $WebScriptInfo);
      }
    },
    [scriptInfo, setScriptInfo]
  );

  return {
    isUpdatingClientSideClip,
    updateVideoSegment,
    updateVideoTimePoints,
    regenerateVideo,
    generateAllVideos,
    hasVideoGenerating, // 检查是否有视频正在生成中
  };
}
