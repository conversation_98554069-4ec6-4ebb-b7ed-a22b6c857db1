import { useEffect, useState, useRef } from 'react';
import { useEditorStore, SaveableComponent } from '../store/editorStore';
import { nanoid } from 'nanoid'; // 您需要安装这个包或使用其他唯一ID生成方式

interface UseSaveableComponentOptions<T> {
  initialValue: T;
  onSave: (value: T) => Promise<void>;
  componentType: string;
  equalityFn?: (a: T, b: T) => boolean;
  id?: string; // 可选的自定义ID
}

export function useSaveableComponent<T>({
  initialValue,
  onSave,
  componentType,
  equalityFn = (a, b) => a === b,
  id: customId,
}: UseSaveableComponentOptions<T>) {
  const [value, setValue] = useState<T>(initialValue);
  const originalValueRef = useRef<T>(initialValue);
  const isDirtyRef = useRef<boolean>(false);
  const componentId = useRef<string>(customId || `${componentType}-${nanoid()}`);

  const { registerSaveableComponent, unregisterSaveableComponent } = useEditorStore();

  // 检查是否有未保存的更改
  const checkDirty = (newValue: T = value) => {
    const dirty = !equalityFn(originalValueRef.current, newValue);
    isDirtyRef.current = dirty;
    return dirty;
  };

  // 保存当前值
  const saveValue = async () => {
    if (!checkDirty()) return;

    try {
      console.log(`[${componentType}] 保存值:`, value);
      await onSave(value);
      originalValueRef.current = value;
      isDirtyRef.current = false;
    } catch (error) {
      console.error(`[${componentType}] 保存失败:`, error);
      throw error;
    }
  };

  // 注册到可保存组件系统
  useEffect(() => {
    const component: SaveableComponent = {
      id: componentId.current,
      save: saveValue,
      isDirty: () => checkDirty(),
      componentType,
    };

    registerSaveableComponent(component);

    return () => {
      // 如果组件卸载时有未保存的更改，尝试保存
      if (checkDirty()) {
        console.log(`[${componentType}] 组件卸载时保存未保存的更改`);
        saveValue().catch(err => console.error(`[${componentType}] 组件卸载时保存失败:`, err));
      }
      unregisterSaveableComponent(componentId.current);
    };
  }, [value]); // 依赖value确保组件的save方法总是使用最新值

  // 值变更时更新状态
  useEffect(() => {
    originalValueRef.current = initialValue;
    setValue(initialValue);
  }, [initialValue]);

  return {
    value,
    setValue,
    isDirty: checkDirty,
    reset: () => {
      setValue(originalValueRef.current);
      isDirtyRef.current = false;
    },
    save: saveValue,
    componentId: componentId.current,
  };
}
