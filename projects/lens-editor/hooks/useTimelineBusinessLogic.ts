import { useCallback } from 'react';
import { TLActionWithName } from '../components/VideoEditor/types';
import { useVideoProcessing } from './useVideoProcessing';
import { useTimelineStore } from '../store/timelineStore';
import { useEditorStore } from '../store/editorStore';
import { $SubtitleItem } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import req from 'common/request';

export function useTimelineBusinessLogic() {
  const { updateVideoTimePoints } = useVideoProcessing();
  const { getSpriteFromMap } = useTimelineStore();
  const { scriptInfo, scriptId } = useEditorStore();

  // 处理视频时间点更新
  const handleVideoTimePointsUpdate = useCallback(
    (action: TLActionWithName) => {
      // 只调用updateVideoTimePoints，它内部已经包含了API请求逻辑
      updateVideoTimePoints(action);
    },
    [updateVideoTimePoints]
  );

  // 处理字幕移动
  const handleSubtitleMoving = useCallback(
    async (action: TLActionWithName) => {
      if (!scriptInfo?.stSubtitle?.vecContent || !scriptId) return;

      // 根据action.id,从scriptInfo?.stSubtitle?.vecContent遍历，找到对应轨道下面包含该歌词的数据
      const subtitleTrack = scriptInfo.stSubtitle.vecContent.find((track: any) =>
        track.vecContent?.find((item: any) => item.strId === action.id)
      );

      if (!subtitleTrack) return;

      // 找到轨道内的具体字幕项
      const subtitleItem = subtitleTrack.vecContent?.find((item: any) => item.strId === action.id);

      if (!subtitleItem) return;

      const updatedSubtitle: $SubtitleItem = {
        ...subtitleItem,
        iStartTimeMs: Math.floor(action.start * 1000),
        iStopTimeMs: Math.floor(action.end * 1000),
      };

      try {
        const response = await req.post('/lens_script/edit_text', {
          strScriptId: scriptId,
          strTrackId: subtitleTrack.strId,
          stInfo: updatedSubtitle,
        });

        if (response.data.error_code === 0) {
          console.log('字幕时间更新成功');
        } else {
          console.error('字幕时间更新失败:', response.data.error_msg);
        }
      } catch (error) {
        console.error('字幕时间更新请求失败:', error);
      }
    },
    [scriptInfo, scriptId]
  );

  // 处理时间轴Action位置变化
  // action在时间轴上的位置发生变化之后回调，歌词和视频轨道都会触发
  const handleTimelineOffsetChange = useCallback(
    (action: TLActionWithName) => {
      const spr = getSpriteFromMap(action);
      console.log('[onOffsetChange]:', action, spr);
      if (spr == null) return;
      spr.time.offset = action.start * 1e6;

      // 检查是否为视频轨道的action
      if (action.type === 'video') {
        // 更新视频时间点
        handleVideoTimePointsUpdate(action);
      } else if (action.type === 'subtitle') {
        // 更新歌词时间点
        handleSubtitleMoving(action);
      }
    },
    [getSpriteFromMap, handleVideoTimePointsUpdate, handleSubtitleMoving]
  );

  // 处理时间轴Action时长变化
  const handleTimelineDurationChange = useCallback(
    ({ action, start, end }: { action: TLActionWithName; start: number; end: number }) => {
      // action在时间轴上的时长发生变化之后回调
      const spr = getSpriteFromMap(action);
      if (spr == null) return false;
      const duration = (end - start) * 1e6;
      if (duration <= 0) return false;

      spr.time.duration = duration;
      console.log('[onDurationChange]:', action, start, end, duration);

      if (action.type === 'video') {
        // 更新视频时间点
        handleVideoTimePointsUpdate(action);
      } else if (action.type === 'subtitle') {
        // 更新歌词时间点
        handleSubtitleMoving(action);

        // 更新歌词淡入淡出动画设置
        if (spr) {
          const totalDuration = (end - start) * 1000000; // 微秒
          const crossfadeDuration = 0.2; // 默认值，可以从字幕参数中获取

          // 计算淡入淡出所占百分比
          const fadeInPercent = ((crossfadeDuration * 1000000) / totalDuration) * 100;
          const fadeOutPercent = 100 - fadeInPercent;

          // 重新设置动画
          spr.setAnimation(
            {
              '0%': { opacity: 0 },
              [`${fadeInPercent}%`]: { opacity: 1 },
              [`${fadeOutPercent}%`]: { opacity: 1 },
              '100%': { opacity: 0 },
            },
            {
              duration: totalDuration,
              iterCount: 1,
            }
          );
        }
      }
      return true;
    },
    [getSpriteFromMap, handleVideoTimePointsUpdate, handleSubtitleMoving]
  );

  return {
    handleTimelineOffsetChange,
    handleTimelineDurationChange,
  };
}
