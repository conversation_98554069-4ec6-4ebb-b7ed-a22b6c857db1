import { useCallback } from 'react';
import { TimelineAction } from '@xzdarcy/react-timeline-editor';
import { TLActionWithName, CustomTimelineRow } from '../components/VideoEditor/types';
import { checkOverlap, fixFloatPrecision } from 'projects/lens-editor/utils/index';

import { useTimelineStore } from '../store/timelineStore';

interface TimelineHandlersProps {
  onPreviewTime: (time: number) => void;
  onOffsetChange: (action: TLActionWithName) => void;
  onDurationChange: (params: { action: TLActionWithName; start: number; end: number }) => void;
  setActiveAction: (action: TLActionWithName) => void;
  actionSpriteMap: WeakMap<TimelineAction, any>;
  onOpenLyricEditor: (action: TLActionWithName) => void;
}

/**
 * 提供时间线相关的事件处理函数
 */
export function useTimelineHandlers({
  onPreviewTime,
  onOffsetChange,
  onDurationChange,
  setActiveAction,
  actionSpriteMap,
  onOpenLyricEditor,
}: TimelineHandlersProps) {
  // 获取当前活跃的动作
  const { activeAction } = useTimelineStore();
  // 处理Action移动的回调, 目前只有歌词轨道运行移动
  const handleActionMoving = useCallback(
    ({ action, row, start, end }: { action: TimelineAction; row: CustomTimelineRow; start: number; end: number }) => {
      console.log('[handleActionMoving]:', action, row, start, end);
      // 检查当前移动的Action是否为歌词轨道
      if (row.id.includes('subtitle')) {
        // 检查是否有重叠
        const hasOverlap = checkOverlap(action, row, start, end);

        // 如果有重叠，阻止移动
        if (hasOverlap) {
          return false;
        }
      }
      return true;
    },
    []
  );

  // 处理Action调整大小的回调
  const handleActionResizing = useCallback(
    ({
      action,
      row,
      start,
      end,
      dir,
    }: {
      action: TLActionWithName;
      row: CustomTimelineRow;
      start: number;
      end: number;
      dir: string;
    }) => {
      console.log('[🎬 RESIZE DEBUG] handleActionResizing开始:', {
        actionId: action.id,
        start,
        end,
        duration: end - start,
        originalStartTs: action.originalStartTs,
        originalEndTs: action.originalEndTs,
        newStartTs: action.newStartTs,
        newEndTs: action.newEndTs,
        dir,
      });

      // 检查当前调整的Action是否为歌词轨道
      if (row.id.includes('subtitle')) {
        // 检查是否有重叠
        const hasOverlap = checkOverlap(action, row, start, end);

        // 如果有重叠，阻止调整大小
        if (hasOverlap) {
          return false;
        }
      } else if (row.id.includes('video')) {
        if (action.videoStatus !== 2 || activeAction?.id !== action.id) {
          return false;
        }
        const allActions = row.actions; // 这里allActions里面的数据没有按照时间顺序排序，需要先进行排序
        allActions.sort((a, b) => a.start - b.start);

        // 找到当前片段的索引
        const currentIndex = allActions.findIndex(a => a.id === action.id);
        if (currentIndex === -1) return true; // 如果找不到，使用默认行为

        // 如果视频是从第0秒开始的，并且向左拖动，则阻止拖动
        // 检查调整后的视频片段时长
        const newDurationSec = fixFloatPrecision(end - start);
        const minDurationSec = 1; // 最小1秒
        const maxDurationSec = 5; // 最大5秒

        // 如果时长小于最小限制或大于最大限制，则阻止调整
        if (newDurationSec < minDurationSec || newDurationSec > maxDurationSec) {
          return false;
        }

        if (dir === 'left') {
          if (action.originalStartTs === 0 && start < action.start) {
            return false;
          }
          // 计算产生的空隙大小, 正数表述往右拖，负数表述往左拖
          const gapSize = fixFloatPrecision(start - (currentIndex > 0 ? allActions[currentIndex - 1].end : 0)); // 秒

          // console.log('[action.duration]:', gapSize, action.playDuration, action.originalStartTs);

          // 往左拖动
          if (gapSize < 0) {
            if (action.originalStartTs && action.originalStartTs > 0) {
              // 如果视频不是从0开始播放的，检查是否超出视频开始边界,超出边界则阻止继续拖动
              if (-gapSize > action.originalStartTs / 1000) {
                return false;
              }
            } else {
              return false;
            }
          }
        }
        // 如果是往右拖动，并且结束时间减去开始时间大于等于视频的originalDuration，则阻止拖动
        // 这个检查仍然有效，但最大5秒的限制会优先于它生效如果originalDuration超过5秒
        if (dir === 'right' && newDurationSec >= (action.originalDuration || 0) / 1000) {
          return false;
        }
      }

      return true;
    },
    [activeAction]
  );

  const handleActionResizeEnd = ({
    action,
    row,
    start,
    end,
    dir,
  }: {
    action: TLActionWithName;
    row: CustomTimelineRow;
    start: number;
    end: number;
    dir: string;
  }) => {
    console.log('[🎬 RESIZE DEBUG] handleActionResizeEnd开始:', {
      actionId: action.id,
      start,
      end,
      duration: end - start,
      originalStartTs: action.originalStartTs,
      originalEndTs: action.originalEndTs,
      newStartTs: action.newStartTs,
      newEndTs: action.newEndTs,
      dir,
      isVideoTrack: row.id.includes('video'),
    });

    // 处理视频轨道的特殊逻辑
    if (row.id.includes('video')) {
      const allActions = row.actions; // 这里allActions里面的数据没有按照时间顺序排序，需要先进行排序
      allActions.sort((a, b) => a.start - b.start);

      // 找到当前片段的索引
      const currentIndex = allActions.findIndex(a => a.id === action.id);
      if (currentIndex === -1) return true; // 如果找不到，使用默认行为

      // 处理左侧调整的情况
      if (dir === 'left') {
        // console.log('[action.originalStartTs]:', start, end, action, currentIndex);

        // 如果是第一个片段且start小于0，直接阻止
        if (currentIndex === 0 && start < 0) {
          action.start = 0;
          return false;
        }

        // 计算产生的空隙大小, 正数表述往右拖，负数表述往左拖
        const gapSize = fixFloatPrecision(start - (currentIndex > 0 ? allActions[currentIndex - 1].end : 0)); // 秒

        // console.log('[action.duration]:', gapSize, action.playDuration, action.originalStartTs);

        // 检查调整后的视频时长是否合理
        const newDuration = fixFloatPrecision(end - start);
        if (newDuration <= 0) {
          console.log('[视频时长不能为0或负数]');
          return false;
        }

        // console.log('[gapSize]:', gapSize);

        // 计算新的视频开始时间点（毫秒）
        // 左侧拖动改变的是视频的起始播放时间
        const newStartTs = parseInt(Math.max(0, (action.originalStartTs || 0) + gapSize * 1000).toFixed(0));
        // 不要立即更新originalStartTs，保持原始值以便updateVideoSegment能检测到变化
        // action.originalStartTs = newStartTs; // 注释掉这行，让updateVideoSegment处理
        action.newStartTs = newStartTs;

        // 把右侧所有片段往左移动gapSize（gapSize可能为负数也可能为正数）以填补空隙
        for (let i = currentIndex; i < allActions.length; i++) {
          const rightAction = allActions[i];

          rightAction.start = fixFloatPrecision(rightAction.start - gapSize);
          rightAction.end = fixFloatPrecision(rightAction.end - gapSize);

          // 只有当前action的newStartTs已更新，其他action不需要更新时间点
          if (i === currentIndex) {
            rightAction.newStartTs = newStartTs;
            rightAction.newEndTs = rightAction.originalEndTs || 0;
          }

          const sprite = actionSpriteMap.get(rightAction);
          if (!sprite) continue;
          sprite.time.offset = rightAction.start * 1e6;
          sprite.time.duration = fixFloatPrecision(rightAction.end - rightAction.start) * 1e6;
        }
        // console.log('[action.onOffsetChange]:', action);
        onOffsetChange(action);
      }

      // // 处理右侧handle拖动
      if (dir === 'right') {
        // 计算偏移量,正数表示往右拖了，负数表示往左拖了。
        const moveOffset = fixFloatPrecision(
          end - start - ((action.originalEndTs || 0) - (action.originalStartTs || 0)) / 1000
        );

        // 计算新的视频结束时间点（毫秒）
        // 右侧拖动改变的是视频的结束播放时间
        const newEndTs = parseInt(
          Math.min(action.originalDuration || 0, (action.originalEndTs || 0) + moveOffset * 1000).toFixed(0)
        );

        // 设置新的结束时间戳，但不立即更新originalEndTs
        // 这样可以让updateVideoTimePoints正确检测到变化
        action.newEndTs = newEndTs;

        // 为后续分镜计算提供正确的时间戳（临时存储）
        (action as any)._computedEndTs = newEndTs;
        // console.log(
        //   '[handleActionResizeEnd moveOffset]:',
        //   moveOffset,
        //   'action原始end:',
        //   action.end,
        //   '新end:',
        //   end,
        //   newEndTs,
        //   currentIndex
        // );
        // 缩放后，如果当时视频的右侧已经是结束位置，那么这个时候往右拖动，只能把视频的start时间往前移动，才能满足。
        if (newEndTs - (action.newStartTs || 0) !== fixFloatPrecision(action.end - action.start) * 1000) {
          action.newStartTs = parseInt(
            Math.max(0, newEndTs - fixFloatPrecision(action.end - action.start) * 1000).toFixed(0)
          );

          // 为后续分镜计算提供正确的时间戳（临时存储）
          (action as any)._computedStartTs = action.newStartTs;
        }
        // 更新当前action的结束时间
        action.end = end;

        console.log(
          `[当前分镜更新] ${action.id}: start=${action.start}, end=${action.end}, newEndTs=${action.newEndTs}`
        );

        // 如果不是最后一个片段，需要移动后续所有片段以保持无缝衔接
        if (currentIndex < allActions.length - 1) {
          let previousEndTime = end; // 前一个片段的结束时间

          // 依次移动后续所有片段，确保它们紧密相连
          console.log(
            '[🎬 RESIZE DEBUG] 开始更新后续分镜，当前索引:',
            currentIndex,
            '总数:',
            allActions.length,
            'previousEndTime:',
            previousEndTime
          );
          for (let i = currentIndex + 1; i < allActions.length; i++) {
            const rightAction = allActions[i];
            const actionDuration = fixFloatPrecision(rightAction.end - rightAction.start); // 保持片段时长不变
            const oldStart = rightAction.start;
            const oldEnd = rightAction.end;

            // 设置新的开始和结束时间，确保与前一个片段无缝衔接
            rightAction.start = fixFloatPrecision(previousEndTime);
            rightAction.end = fixFloatPrecision(previousEndTime + actionDuration);
            previousEndTime = rightAction.end;

            console.log('[🎬 RESIZE DEBUG] 更新后续分镜:', {
              actionId: rightAction.id,
              索引: i,
              原始位置: { start: oldStart, end: oldEnd },
              新位置: { start: rightAction.start, end: rightAction.end },
              时长: actionDuration,
              previousEndTime,
              originalStartTs: rightAction.originalStartTs,
              originalEndTs: rightAction.originalEndTs,
            });

            // 更新对应的sprite
            const sprite = actionSpriteMap.get(rightAction);
            if (sprite) {
              sprite.time.offset = rightAction.start * 1e6;
              sprite.time.duration = actionDuration * 1e6; // 确保后续片段的时长也被正确更新
            }

            // 对于后续分镜，保持其原始的视频播放范围不变，只调整在时间轴上的位置
            // 但是需要确保newStartTs和newEndTs与当前的originalStartTs和originalEndTs一致
            if (rightAction.originalStartTs !== undefined) {
              rightAction.newStartTs = rightAction.originalStartTs;
            }
            if (rightAction.originalEndTs !== undefined) {
              rightAction.newEndTs = rightAction.originalEndTs;
            }
            // console.log('handleActionResizeEnd rightAction:', rightAction);
          }
        }
        // console.log('[handleActionResizeEnd]:', action, start, end);

        // 更新视频播放时长
        action.playDuration = parseInt((fixFloatPrecision(end - start) * 1000).toFixed(0));

        // 直接更新当前分镜的sprite时长，确保播放时长正确
        const currentSprite = actionSpriteMap.get(action);
        if (currentSprite) {
          const newDuration = fixFloatPrecision(end - start) * 1e6; // 转换为微秒
          currentSprite.time.duration = newDuration;
          console.log('[直接更新当前分镜sprite时长]:', action.id, '新时长(秒):', end - start, '微秒:', newDuration);
        }

        // 更新当前action的结束时间
        onDurationChange({
          action,
          start,
          end,
        });

        // 在onDurationChange之后更新originalStartTs和originalEndTs
        // 这样既能让视频更新逻辑正常工作，又能为下次拖动提供正确的基准值
        if (action.newStartTs !== undefined && action.newStartTs !== action.originalStartTs) {
          action.originalStartTs = action.newStartTs;
        }
        if (action.newEndTs !== undefined && action.newEndTs !== action.originalEndTs) {
          action.originalEndTs = action.newEndTs;
        }

        // 清理临时计算值
        delete (action as any)._computedStartTs;
        delete (action as any)._computedEndTs;

        // 重要：将修改后的actions更新回时间轴存储，确保UI正确渲染
        const { updateTrackById } = useTimelineStore.getState();
        const videoTrack = useTimelineStore.getState().getTrackById('3-video');
        if (videoTrack) {
          console.log(
            '[🎬 RESIZE DEBUG] 准备更新时间轴存储，当前actions:',
            videoTrack.actions.map(a => ({
              id: a.id,
              start: a.start,
              end: a.end,
              originalStartTs: a.originalStartTs,
              originalEndTs: a.originalEndTs,
              newStartTs: a.newStartTs,
              newEndTs: a.newEndTs,
            }))
          );
          updateTrackById('3-video', videoTrack.actions);
          console.log('[🎬 RESIZE DEBUG] 时间轴存储已更新，包含', videoTrack.actions.length, '个actions');
        }
      }

      // 已经手动处理了所有更新，阻止默认行为
      return false;
    } else if (row.id.includes('subtitle')) {
      onDurationChange({
        action,
        start,
        end,
      });
      // 歌词轨道
      return false;
    }

    return true;
  };

  // 处理点击操作
  const handleClickAction = useCallback(
    (
      _: any,
      {
        action,
        time,
        row,
      }: {
        action: TLActionWithName;
        time: number;
        row: CustomTimelineRow;
      }
    ) => {
      setActiveAction({ ...action, name: action.id });
      console.log('[action]:', action, row);

      // 当点击视频轨道时执行预览操作
      if (action.type === 'video' || row.id === '3-video') {
        onPreviewTime(time);
      }
      // 歌词点击后，需要打开歌词编辑器
      if (row.id.includes('subtitle')) {
        onOpenLyricEditor(action);
      }
    },
    [setActiveAction, onPreviewTime, onOpenLyricEditor]
  );

  // 处理时间线点击
  const handleClickTimeArea = useCallback(
    (time: number) => {
      onPreviewTime(time);
      return true;
    },
    [onPreviewTime]
  );

  // 处理游标拖拽结束
  const handleCursorDragEnd = useCallback(
    (time: number) => {
      onPreviewTime(time);
    },
    [onPreviewTime]
  );

  // 动作移动结束
  const handleActionMoveEnd = useCallback(
    ({ action }: { action: TLActionWithName }) => {
      if (onOffsetChange) {
        onOffsetChange(action);
      }
    },
    [onOffsetChange]
  );

  return {
    handleActionMoving,
    handleActionResizing,
    handleActionResizeEnd,
    handleClickAction,
    handleClickTimeArea,
    handleCursorDragEnd,
    handleActionMoveEnd,
  };
}
