/**
 * 视频处理工具函数集合
 * 提供视频剪辑、缓存和处理的通用方法
 * 处理视频剪辑，按照开始时间和结束时间剪切视频
 * 优化实现，包含内存管理和性能优化
 *
 * @param videoClip 原始视频剪辑对象
 * @param startTs 开始时间（毫秒）
 * @param endTs 结束时间（毫秒）
 * @param totalDuration 视频总时长（毫秒）
 * @returns 处理后的视频剪辑对象
 */
export async function processVideoClip(
  videoClip: any,
  startTs: number,
  endTs: number,
  totalDuration: number
): Promise<any> {
  let finalVideoClip = videoClip;
  // 临时存储需要销毁的视频片段
  const clipsToDestroy: any[] = [];

  try {
    // 如果有指定的开始时间，使用split方法获取从指定时间开始的视频片段
    if (startTs > 0) {
      try {
        // 采用分阶段处理方式，避免连续多次split
        const splitClips = await videoClip.split(startTs * 1000); // 转换为微秒
        if (splitClips && splitClips.length === 2) {
          // 记录第一个片段，后续统一销毁
          clipsToDestroy.push(splitClips[0]);
          finalVideoClip = splitClips[1]; // 使用从iStartTs开始的部分

          // 分阶段操作之间添加短暂休息，减轻解码器压力
          await new Promise(resolve => setTimeout(resolve, 50));

          // 处理结束时间
          if (endTs < totalDuration) {
            const endClipTime = (endTs - startTs) * 1000; // 转换为微秒
            const endSplitClips = await finalVideoClip.split(endClipTime);
            if (endSplitClips && endSplitClips.length === 2) {
              // 记录第二个片段，后续统一销毁
              clipsToDestroy.push(endSplitClips[1]);
              finalVideoClip = endSplitClips[0];
            }
          }
        } else {
          // 如果分割返回的结果不符合预期，继续使用原始视频
          finalVideoClip = videoClip;
        }
      } catch (error) {
        console.error('分割视频开始部分失败:', error);
        // 如果split失败，继续使用原始视频
        finalVideoClip = videoClip;
      }
    } else if (endTs < totalDuration) {
      // 只处理结束时间
      try {
        const endClipTime = endTs * 1000; // 转换为微秒
        const endSplitClips = await videoClip.split(endClipTime);
        if (endSplitClips && endSplitClips.length === 2) {
          // 记录第二个片段，后续统一销毁
          clipsToDestroy.push(endSplitClips[1]);
          finalVideoClip = endSplitClips[0];
        } else {
          finalVideoClip = videoClip;
        }
      } catch (error) {
        console.error('分割视频结束部分失败:', error);
        finalVideoClip = videoClip;
      }
    } else {
      // 使用完整视频
      finalVideoClip = videoClip;
    }

    // 确保视频剪辑就绪
    if (finalVideoClip && finalVideoClip.ready) {
      await finalVideoClip.ready;
    }

    return finalVideoClip;
  } catch (error) {
    console.error('处理视频剪辑失败:', error);
    // 发生错误时返回原始视频剪辑
    return videoClip;
  } finally {
    // 统一销毁不需要的片段，释放内存
    if (clipsToDestroy.length > 0) {
      // 延迟销毁，避免影响主流程
      setTimeout(() => {
        clipsToDestroy.forEach(clip => {
          try {
            if (clip && clip !== finalVideoClip) {
              clip.destroy();
            }
          } catch (e) {
            console.error('销毁临时视频片段失败:', e);
          }
        });
      }, 300);
    }
  }
}

/**
 * 检查视频URL是否有效
 *
 * @param url 视频URL
 * @returns 是否有效
 */
export async function isVideoUrlValid(url: string): Promise<boolean> {
  if (!url) return false;

  try {
    const normalizedUrl = url.replace('http://', 'https://');
    const response = await fetch(normalizedUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('检查视频URL有效性失败:', error);
    return false;
  }
}
