import { TimelineAction } from '@xzdarcy/react-timeline-editor';
import { CustomTimelineRow } from '../components/VideoEditor/types';

export function stopPropagation(event: any) {
  event.stopPropagation();
}

export function isArrayEqual(a: string[], b: string[]) {
  return a.join('') === b.join('');
}
// 格式化时间 分钟:秒
export const formatTime = (timeInMs: number) => {
  const totalSeconds = Math.floor(timeInMs / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * 修复浮点数精度问题的工具函数
 * @param num 需要修复精度的数字
 * @param precision 保留的小数位数，默认为10位
 * @returns 修复精度后的数字
 */
export const fixFloatPrecision = (num: number, precision: number = 10): number => {
  return Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision);
};

// 检查是否重叠
export const checkOverlap = (
  currentAction: TimelineAction,
  row: CustomTimelineRow,
  newStart: number,
  newEnd: number
): boolean => {
  // 获取同一轨道上的其他action
  const otherActions = row.actions.filter(a => a.id !== currentAction.id);

  // 检查是否与其他action重叠
  return otherActions.some(a => {
    // 检查两个时间段是否重叠
    return newStart < a.end && newEnd > a.start;
  });
};

/**
 * 对时间轴 actions 按 start 时间排序
 * @param actions 要排序的 actions 数组
 * @returns 排序后的 actions 数组
 */
export const sortActionsByTime = <T extends { start: number; id: string }>(actions: T[]): T[] => {
  return [...actions].sort((a, b) => {
    if (a.start !== b.start) {
      return a.start - b.start;
    }
    // 如果开始时间相同，按 id 排序保证稳定性
    return a.id.localeCompare(b.id);
  });
};

/**
 * 验证 actions 是否按时间顺序排列
 * @param actions 要验证的 actions 数组
 * @param trackName 轨道名称，用于日志
 * @returns 是否已正确排序
 */
export const validateActionsOrder = <T extends { start: number; id: string }>(
  actions: T[],
  trackName = 'unknown'
): boolean => {
  for (let i = 1; i < actions.length; i++) {
    if (actions[i].start < actions[i - 1].start) {
      console.warn(`[${trackName}] Actions 顺序错误:`, {
        前一个: { id: actions[i - 1].id, start: actions[i - 1].start },
        当前: { id: actions[i].id, start: actions[i].start },
        索引: i,
      });
      return false;
    }
  }
  return true;
};

/**
 * 安全地获取排序后的 actions
 * @param actions 原始 actions 数组
 * @param trackName 轨道名称
 * @returns 确保排序的 actions 数组
 */
export const getSortedActions = <T extends { start: number; id: string }>(actions: T[], trackName = 'unknown'): T[] => {
  if (!validateActionsOrder(actions, trackName)) {
    console.log(`[${trackName}] 检测到顺序错误，正在重新排序...`);
    return sortActionsByTime(actions);
  }
  return actions;
};
