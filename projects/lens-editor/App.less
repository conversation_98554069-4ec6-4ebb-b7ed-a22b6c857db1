@import '../../common/styles/variables.less';
@import '../../common/styles/mixins.less';



.header {
  background-color: @bg-color;
  margin: 4px 15px;
}

.logo {
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 999;
}

:global {
  .mantine-Stepper-root {
    padding-top: 1px;
  }

  .mantine-Stepper-steps {
    width: 500px;
    margin: 10px auto;
    color: rgba(255, 255, 255, 0.3);
  }

  .mantine-Stepper-step {
    width: 114px;
    padding: 5px 15px 5px 7px;
    border-radius: 50px;
  }

  .mantine-Stepper-step:where([data-progress]) {
    color: rgba(255, 255, 255, 1) !important;
    background-color: rgba(255, 255, 255, 0.1);

  }

  // .mantine-Stepper-stepLoader::after {
  //   border-color: var(--loader-color) var(--loader-color)var(--loader-color)transparent;
  // }

  .mantine-Stepper-stepIcon {
    width: 7px;
    height: 7px;
    min-width: 7px;
    min-height: 7px;
    overflow: hidden;
    text-indent: -9999px;
    font-weight: normal;

    border: none;

    svg {
      display: none;
    }
  }

  .mantine-Stepper-separator {
    height: 0;
  }

  .mantine-Stepper-stepIcon:where([data-progress]) {
    background: linear-gradient(270deg, #215DFF 0%, #69FFA3 100%);
    color: #fff;
    border: none;
    min-width: 22px;
    min-height: 22px;
    text-indent: 0;
  }

  .mantine-Stepper-stepIcon:where([data-completed]) {
    background: @gray-color;
    border-color: var(--mantine-color-dark-5);
  }

  .mantine-SegmentedControl-control {
    flex: none
  }

  .mantine-SegmentedControl-control::before {
    display: none;
  }

  .mantine-SegmentedControl-root {
    gap: 14px;
    flex-wrap: wrap;
  }

  .mantine-SegmentedControl-label {
    height: auto;
    padding: 8px 28px;
    border: 1px solid transparent
  }

  .mantine-SegmentedControl-label[data-active=true] {
    color: @green-text-color;
    border-color: @green-text-color;
  }

  .mantine-SegmentedControl-label[data-disabled=true] {
    color: rgba(255, 255, 255, 0.3);
    border-color: transparent;
    background-color: #222;
  }

  .mantine-Button-root {
    padding: 0 20px;
  }

  .mantine-Tabs-list {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #2A313E
  }
}

.doneStep {
  color: rgba(255, 255, 255, 0.8);

  :global {
    .mantine-Stepper-stepIcon {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }
}


.appContainer {

  margin: 0 auto;
  width: 100%;
  min-height: 100vh;
  background-color: @bg-color;
  color: @text-color;

  // 使用mixin示例
  .flex-center();
}

.themeCard {
  width: 240px;
  height: 120px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  background: #16282C;
  border-color: #29666B;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.active {
    border-color: @primary-color;
  }
}

.checkIcon {
  position: absolute;
  top: 12px;
  right: 15px;
  background: rgba(0, 0, 0, 0.50);
  border-radius: 5px;
}

.prevBtn,
.nextBtns {
  position: fixed;
  top: 14px;
  z-index: 100;


}

.prevBtn {
  left: 140px;
}

.nextBtns {

  display: flex;
  justify-content: flex-end;
  align-items: center;

  right: 20px;



}

.nextBtns button:not(:first-child) {
  margin-left: 20px;
}

button[data-variant="proccess"] {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(3px);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  color: #fff;
  border: none;
}

button[data-variant="lite"] {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  backdrop-filter: blur(3px);
  border-radius: 12px;
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
  text-align: center;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}



.nextBtns button {
  padding: 10px 32px;
}

.prevBtn {
  backdrop-filter: blur(20px);
  padding: 10px 26px;
}

.selectedItem {
  border: 1px solid @green-text-color;
}

// 文字按钮
.txtBtn {
  color: @green-text-color;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}



.aiTxtLoadIcon {
  width: 16px;
  height: 16px;
  background: url('projects/lens-editor/assets/images/pages/ai_txt_load.png') no-repeat center center;
  background-size: 14px 14px;
}

.aiTxtLoadIconIng {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.aiTxtLoadBtn {
  position: absolute;
  right: 6px;
  bottom: 6px;
}

.aiTxtLoadCount {
  position: absolute;
  right: 80px;
  bottom: 10px;
}


.aiTxtInput[data-error]~.aiTxtLoadBtn {
  bottom: 24px;
}

.aiTxtInput[data-error]~.aiTxtLoadCount {
  bottom: 28px;
}

.mytab {
  flex: 1;



  ::before {
    border-bottom: 1px solid #333842;
  }

  button[role="tab"] {
    font-size: 16px;
    border-bottom-width: 1px;
    padding: 10px 12px;
  }

  button[role="tab"][data-active] {

    border-bottom: 1px solid @green-slt-color !important;
  }
}