import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Create from '../pages/Create';
import Home from '../pages/Home';
import UserInfo from '../pages/UserInfo';

const AppRoutes: React.FC = () => {
  console.log('Current pathname:', window.location.hash);
  return (
    <Routes>
      <Route path="/" element={<Navigate to="/home" replace />} />
      <Route path="/home" element={<Home />} />
      <Route path="/userinfo" element={<UserInfo />} />
      <Route path="/editor/:scriptId" element={<Create />} />
      <Route path="/editor/new" element={<Create />} />
      <Route path="*" element={<Navigate to="/home" replace />} />
    </Routes>
  );
};

export default AppRoutes;
