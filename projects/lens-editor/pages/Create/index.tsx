import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Container, Button, Group, LoadingOverlay, Modal, Box, Text } from '@mantine/core';
import { useParams } from 'react-router-dom';
import { useDisclosure } from '@mantine/hooks';
import Subject from '../../components/Subject';

import VideoEditor from 'projects/lens-editor/components/VideoEditor/VideoEditorIndex';
import Preview from 'projects/lens-editor/components/Preview/preview';
import { useEditorStore } from '../../store/editorStore';

import {
  $VideoModel,
  emFlow,
  emStatus,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import {
  $DoMainProcessRsp,
  emScriptGetMask,
  emEditType,
  $BasicInfoRsp,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { notifications } from '@mantine/notifications';
import req from 'common/request';
// import QrCodeModal from 'projects/lens-editor/components/Preview/qrCodeModal';
import appcss from 'projects/lens-editor/App.less';

// 新的步骤映射，将UI步骤(0,1,2)映射到后端流程步骤(1,3,5)
const STEP_TO_FLOW_MAP = [emFlow.EM_FLOW_DETERMIN_TOPIC, emFlow.EM_FLOW_STORYBOARD, emFlow.EM_FLOW_PREVIEW];
// 反向映射，将后端流程步骤映射到UI步骤，修复类型问题
const FLOW_TO_STEP_MAP: Record<number, number> = {
  [emFlow.EM_FLOW_DETERMIN_TOPIC]: 0,
  [emFlow.EM_FLOW_STORYBOARD]: 1,
  [emFlow.EM_FLOW_PREVIEW]: 2,
};

export default function Create() {
  const { scriptId: routeScriptId } = useParams();
  const [isLoading, setIsLoading] = useState(false);
  const [flowStatus, setFlowStatus] = useState<Record<emFlow, number>>();
  console.log('[routeScriptId]:', routeScriptId);

  const {
    currentStep, // 从0开始的，
    setCurrentStep,
    scriptId,

    setScriptId,
    setScriptInfo,

    scriptInfo,
    startPolling,
    getBasicInfo,
    showBasicConfig,
    isButtonLoading,
    fetchScriptInfoOnce,
    setBasicConfig,
    setButtonLoading,
  } = useEditorStore();

  // 添加确认弹框状态
  const [confirmModalOpened, { open: openConfirmModal, close: closeConfirmModal }] = useDisclosure(false);
  const [pendingVideoModel, setPendingVideoModel] = useState<$VideoModel | undefined>(undefined);

  const [share, setShare] = useState('');

  const fetchStoryboardProcess = () => {
    req
      .post('/lens_script/do_main_process', {
        strScriptId: scriptId,
        eType: emFlow.EM_FLOW_STORYBOARD,
      })
      .then((res: { data: { data: $DoMainProcessRsp; error_code: number; error_msg: string } }) => {
        console.log('[do_main_process EM_FLOW_STORYBOARD res]:', res);
        if (res.data.error_code === 0 && res.data.data.iRes === 0) {
          // 确保轮询已启动，获取最新数据
          startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, undefined, undefined, 'picture');
          setCurrentStep(1);
        } else {
          notifications.show({
            title: '提示',
            message: res.data.error_msg,
            color: 'yellow',
            autoClose: 5000,
            position: 'top-center',
          });
          setButtonLoading(false);
        }
      })
      .catch(error => {
        console.error('分镜生成失败:', error);
        notifications.show({
          title: '服务器异常',
          message: error.status,
          color: 'red',
          autoClose: 5000,
          position: 'top-center',
        });
        setButtonLoading(false);
      });
  };

  const handleNextStep = () => {
    console.log('[handleNextStep]:', currentStep);

    // 使用通用表单验证方法验证当前步骤
    const isFormValid = useEditorStore.getState().validateCurrentStepForm();
    if (!isFormValid) {
      return; // 如果表单验证失败，则不执行后续步骤
    }

    setButtonLoading(true);

    // 获取当前步骤对应的后端流程ID
    // const currentFlow = STEP_TO_FLOW_MAP[currentStep ?? 0];
    // 获取下一个步骤对应的后端流程ID
    const nextFlow = STEP_TO_FLOW_MAP[(currentStep ?? 0) + 1];

    // 如果点击的按钮是"下一步"，并且nextFlow的mapFlowStatus为2（已完成），则直接设置currentStep为当前步骤+1
    if (
      typeof currentStep === 'number' &&
      getNextButtonText() === '下一步' &&
      (scriptInfo?.mapFlowStatus as Record<string, number>)?.[nextFlow.toString()] === 2
    ) {
      // 延迟500ms，确保当前步骤的值已经触发blur导致更新
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        setButtonLoading(false);
      }, 500);
      return;
    }

    switch (currentStep) {
      case 0:
        console.log('[currentStep 0]:', scriptInfo);

        // 已经确定主题 需要获取场景脚本
        setTimeout(() => {
          fetchStoryboardProcess();
        }, 600);
        break;

      case 1:
        // 已经获取分镜 需要合成视频
        req
          .post('/lens_script/do_main_process', {
            strScriptId: scriptId,
            eType: emFlow.EM_FLOW_PREVIEW,
          })
          .then(res => {
            console.log('[合成视频 res]:', res);
            if (res.data.error_code === 0 && res.data.data.iRes === 0) {
              // 确保轮询已启动，获取最新数据
              startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_MERGE);
              setCurrentStep((currentStep ?? 0) + 1);
            } else {
              notifications.show({
                title: '合成视频失败',
                message: res.data.error_msg,
                color: 'red',
                autoClose: 5000,
              });
              setButtonLoading(false);
              // fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_MERGE, () => {
              //   setCurrentStep((currentStep ?? 0) + 1);
              // });
            }
          })
          .catch(error => {
            console.error('合成视频失败:', error);
            notifications.show({
              title: '服务器异常',
              message: error.status,
              color: 'red',
              autoClose: 5000,
            });
            setButtonLoading(false);
          });
        break;

      case 2:
        // 当前在预览界面，分享视频
        if (scriptInfo?.strVideoUrl) {
          setShare(scriptInfo?.strVideoUrl);
        } else {
          notifications.show({
            title: '分享视频失败',
            message: '加载中，请稍后重试',
            color: 'red',
            autoClose: 5000,
          });
        }
        setButtonLoading(false);
        break;

      default:
        setCurrentStep((currentStep ?? 0) + 1);
    }
  };

  const prevStep = () =>
    setCurrentStep(currentStep !== undefined && currentStep > 0 ? currentStep - 1 : (currentStep ?? 0));

  const getNextButtonText = () => {
    // 获取下一个步骤对应的后端流程ID
    const nextFlow = STEP_TO_FLOW_MAP[(currentStep ?? 0) + 1];

    // 如果下一个流程的状态为2（已完成），则显示"下一步"
    if (
      currentStep !== undefined &&
      currentStep < 2 &&
      scriptInfo?.mapFlowStatus &&
      (scriptInfo.mapFlowStatus as Record<string, number>)[nextFlow.toString()] === 2
    ) {
      return '下一步';
    }

    switch (currentStep) {
      case 0:
        return '生成分镜';
      case 1:
        // 如果 mapStatus[5] = 0 的时候展示合成视频，否则展示下一步
        if (scriptInfo?.mapFlowStatus && (scriptInfo.mapFlowStatus as Record<string, number>)['5'] === 0) {
          return '合成视频';
        } else {
          return '下一步';
        }
      case 2:
        return '';
      default:
        return '下一步';
    }
  };

  // 修改handleAutoGenAll函数
  const handleAutoGenAll = async (stVideoModel?: $VideoModel) => {
    // 使用通用表单验证方法验证当前步骤
    const isFormValid = useEditorStore.getState().validateCurrentStepForm();
    if (!isFormValid) {
      return; // 如果表单验证失败，则不执行后续步骤
    }

    // 获取下一个步骤对应的后端流程ID
    const nextFlow = STEP_TO_FLOW_MAP[(currentStep ?? 0) + 1];

    // 如果下一步的内容状态为2，则弹确认弹框
    if ((scriptInfo?.mapFlowStatus as Record<string, number>)?.[nextFlow.toString()] === 2) {
      setPendingVideoModel(stVideoModel);
      openConfirmModal();
      return;
    }
    // 如果角色生成状态不为2，则弹确认弹框
    if (scriptInfo?.vecRoleInfo?.length) {
      // 遍历角色，如果任意一个角色的eStatus不为2，则弹确认弹框
      const hasRoleNotReady = scriptInfo.vecRoleInfo.some(role => role.eStatus !== 2);
      if (hasRoleNotReady) {
        notifications.show({
          title: '提示',
          message: '请等待角色生成完毕',
          color: 'yellow',
          autoClose: 5000,
        });
        return;
      }
    }

    // 直接执行生成流程
    handleAutoGenAllProcess(stVideoModel);
  };

  // 抽取实际处理逻辑到单独函数
  const handleAutoGenAllProcess = async (stVideoModel?: $VideoModel) => {
    // 如果视频模型不为空，则修改视频模型
    if (stVideoModel) {
      const { modifyTopic } = useEditorStore.getState();
      try {
        await modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_VIDMODEL, {
          stVideoModel,
        });
      } catch (error: any) {
        notifications.show({
          title: '一键成片失败',
          message: '视频模型修改失败' + error,
          color: 'red',
          autoClose: 5000,
        });
        return;
      }
    }

    // 获取当前步骤对应的后端流程ID
    const currentFlow = STEP_TO_FLOW_MAP[currentStep ?? 0];

    const requestData: any = {
      eType: currentFlow,
      strScriptId: scriptId,
    };

    req
      .post('/lens_script/auto_gen_all', requestData)
      .then(res => {
        console.log('[一键成片 res]:', res);
        if (res.data.error_code === 0 && res.data.data.iRes === 0) {
          startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL);
          notifications.show({
            title: '一键成片成功',
            message: res.data.data.strTips,
          });
        } else {
          notifications.show({
            title: '一键成片失败',
            message: res.data.error_msg || res.data.data.strTips,
            color: 'red',
            autoClose: 5000,
          });
        }
      })
      .catch(error => {
        console.error('一键成片失败:', error);
        notifications.show({
          title: '一键成片失败',
          message: '服务器繁忙，请稍后重试' + error.status,
          color: 'red',
          autoClose: 5000,
        });
      });
  };

  const handleStepClick = (step: number) => {
    if (step === currentStep) {
      return;
    }
    setCurrentStep(step);
  };

  const isAutoIng = () => {
    // 是否正在一键成片， 如果视频链接不存在，或者当前流程不是最后一步，则认为正在一键成片
    return scriptInfo?.iIsAuto === 1 && (!scriptInfo?.strVideoUrl || scriptInfo?.eCurFlow < emFlow.EM_FLOW_PREVIEW);
  };

  const isStepAllowed = (step: number) => {
    // 获取步骤对应的后端流程ID
    const flowId = STEP_TO_FLOW_MAP[step];

    // 如果是一键成片类型且当前步骤还未执行的情况下，不允许点击，反之则允许点击
    switch (step) {
      case 0:
        return !isAutoIng();
      case 1:
        return !isAutoIng() && (scriptInfo?.mapFlowStatus as Record<string, number>)?.[flowId] > 0;
      case 2:
        return (
          !isAutoIng() &&
          ((scriptInfo?.mapFlowStatus as Record<string, number>)?.[flowId] > 0 || !!scriptInfo?.strVideoUrl)
        );
      default:
        return true;
    }
  };

  useEffect(() => {
    console.log('[scriptInfo]:', scriptInfo, routeScriptId);

    // 当路由变化时，立即清理状态
    const shouldReset =
      routeScriptId !== scriptInfo?.strScriptId || ((!routeScriptId || routeScriptId === 'new') && scriptInfo);

    if (shouldReset) {
      // 先停止轮询，防止轮询继续更新状态
      const { stopPolling, resetState } = useEditorStore.getState();
      stopPolling('路由变化，清理状态');

      // 使用resetState完全重置状态
      resetState();
    }

    // 添加延迟确保状态清理完成

    const initializeEditor = () => {
      // 如果是新建MV
      if (!routeScriptId || routeScriptId === 'new') {
        setScriptId('');
        setScriptInfo(null);
        setCurrentStep(0);
        console.log('[新建]:');
      } else {
        console.log('[编辑]:', routeScriptId, scriptId);
        // URL参数中有scriptId，则认为是编辑已有MV
        setScriptId(routeScriptId);
        setIsLoading(true);
        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, () => {
          const currentScriptInfo = useEditorStore.getState().scriptInfo;
          console.log('[fetchScriptInfoOnce scriptInfo]:', currentScriptInfo);

          if (currentScriptInfo) {
            // 设置当前步骤，根据eCurFlow映射到UI步骤
            const uiStep = FLOW_TO_STEP_MAP[currentScriptInfo.eCurFlow] || 0;
            setCurrentStep(uiStep);
          }
          setIsLoading(false);
        });
      }
    };

    initializeEditor();

    // 获取基础配置信息
    getBasicInfo(routeScriptId)
      .then((config: $BasicInfoRsp) => {
        setBasicConfig(config);
      })
      .catch(error => {
        console.error('获取基础配置失败:', error);
      });
  }, [routeScriptId]);

  useEffect(() => {
    console.log('[mapFlowStatus useEffect]:', scriptInfo?.mapFlowStatus);

    setFlowStatus(scriptInfo?.mapFlowStatus as Record<emFlow, number>);
  }, [scriptInfo?.mapFlowStatus]);

  // // 如果当前步骤的mapFlowStatus为1，则执行轮询
  // useEffect(() => {
  //   console.log('[currentStep useEffect]:', currentStep, scriptInfo?.mapFlowStatus);

  //   if (
  //     currentStep &&
  //     scriptInfo?.mapFlowStatus &&
  //     (scriptInfo.mapFlowStatus as Record<string, number>)[currentStep] === 1
  //   ) {
  //     startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL);
  //     console.log('[currentStep useEffect]:', currentStep, scriptInfo?.mapFlowStatus);
  //   }
  // }, [currentStep, scriptInfo?.mapFlowStatus]);

  console.log('[scriptInfo create]:', isAutoIng(), scriptInfo, currentStep, isLoading, isButtonLoading, flowStatus);
  return (
    <Container size={'100%'} p={0}>
      <LoadingOverlay visible={isLoading} />
      {!isLoading && currentStep !== undefined && (
        <Stepper active={currentStep} size="sm" onStepClick={handleStepClick}>
          <Stepper.Step
            label="主题确定"
            className={flowStatus?.[emFlow.EM_FLOW_DETERMIN_TOPIC] === emStatus.EM_STATUS_SUCC ? appcss.doneStep : ''}
            allowStepSelect={isStepAllowed(0)}
            loading={flowStatus?.[emFlow.EM_FLOW_DETERMIN_TOPIC] === 1}>
            {currentStep === 0 && <Subject />}
          </Stepper.Step>
          <Stepper.Step
            className={flowStatus?.[emFlow.EM_FLOW_STORYBOARD] === emStatus.EM_STATUS_SUCC ? appcss.doneStep : ''}
            allowStepSelect={isStepAllowed(1)}
            label="分镜编辑"
            loading={flowStatus?.[emFlow.EM_FLOW_STORYBOARD] === 1}>
            {currentStep === 1 && <VideoEditor fetchStoryboardProcess={fetchStoryboardProcess} />}
          </Stepper.Step>
          <Stepper.Step
            className={flowStatus?.[emFlow.EM_FLOW_PREVIEW] === emStatus.EM_STATUS_SUCC ? appcss.doneStep : ''}
            allowStepSelect={isStepAllowed(2)}
            label="合成作品"
            loading={flowStatus?.[emFlow.EM_FLOW_PREVIEW] === 1}>
            {currentStep === 2 && <Preview />}
          </Stepper.Step>
        </Stepper>
      )}

      {/* 确认弹框 */}
      <Modal
        opened={confirmModalOpened}
        onClose={closeConfirmModal}
        title="提示"
        centered
        overlayProps={{
          backgroundOpacity: 0.55,
          blur: 8,
        }}>
        <Box>
          <Text mb="xl">一键成片将重新生成所有视频，是否继续？</Text>
          <Group justify="right">
            <Button onClick={closeConfirmModal}>取消</Button>
            <Button
              color="red"
              onClick={() => {
                closeConfirmModal();
                handleAutoGenAllProcess(pendingVideoModel);
              }}>
              确认
            </Button>
          </Group>
        </Box>
      </Modal>

      {/* 按钮条 */}
      {scriptId && currentStep !== undefined && currentStep > 0 && !scriptInfo?.iIsAuto && (
        <Button variant="proccess" className={appcss.prevBtn} onClick={prevStep}>
          上一步
        </Button>
      )}

      {/* 如果当前步骤不是0，则显示底部按钮条， 如果当前步骤是0，但是用户选择了重新选歌（showBasicConfig为false），那么虽然有scriptId，但是也不能显示下一步按钮 */}
      {scriptId && (showBasicConfig || currentStep !== 0) && getNextButtonText() ? (
        <Box className={appcss.nextBtns}>
          <Button variant="proccess" onClick={() => void handleNextStep()} loading={isButtonLoading || isAutoIng()}>
            {getNextButtonText()}
          </Button>

          {/* 只有第一步(currentStep=0)才显示"一键成片"按钮 */}
          {currentStep === 0 && (
            <Button
              variant="awsome"
              loading={scriptInfo?.iIsAuto === 1 && scriptInfo?.eCurFlow < 5 && isButtonLoading}
              onClick={() => void handleAutoGenAll()}>
              一键成片
            </Button>
          )}
        </Box>
      ) : null}

      {/* {share ? <QrCodeModal link={share} onClose={() => setShare('')} /> : null} */}
    </Container>
  );
}
