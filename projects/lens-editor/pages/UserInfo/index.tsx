import React, { useEffect } from 'react';
import { Container, Loader, Center } from '@mantine/core';
import { useUserInfoStore } from '../../store/userInfoStore';
// import MyRole from '../../components/Mine/MyRole';
// import MyEnv from '../../components/Mine/MyEnv';
import MyMv from '../../components/Mine/MyMv';

const UserInfo: React.FC = () => {
  const { isLoading, fetchData } = useUserInfoStore();

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <Container size={970} px={0}>
      {isLoading ? (
        <Center>
          <Loader size="xl" />
        </Center>
      ) : (
        <>
          <MyMv/>
          {/* <MyRole/> */}
          {/* <MyEnv/> */}
          
        </>
      )}
    </Container>
  );
};

export default UserInfo;
