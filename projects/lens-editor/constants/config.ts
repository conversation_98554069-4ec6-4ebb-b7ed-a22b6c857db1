import {
  $PicInfo,
  $StoryboardInfo,
  $VideoInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import fallbackAvatar from '../assets/images/pages/fallbackAvatar.png';
import placeholder from '../assets/images/pages/placeholder.png';
export const FALLBACK_IMAGE = placeholder;

export const FALLBACK_AVATAR = fallbackAvatar;
// 新的空分镜模板
export const newEmptyStoryboard: $StoryboardInfo = {
  strId: '',
  strSceneId: '',
  iIndex: 1, // 第一个分镜
  strStory: '',
  eStatus: 0,
  stCurPic: {} as $PicInfo,
  vecPic: [],
  stCurVideo: {} as $VideoInfo,
  vecVideo: [],
  vecRoleId: [],
  strShotType: '',
  iStartTime: 0,
  iTimeRange: 0,
  strEnvId: '',
  iEndTime: 0,
};
