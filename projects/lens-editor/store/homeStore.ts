import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { $WebVideoInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import req from 'common/request';
import { $GetFrontPageRsp } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { notifications } from '@mantine/notifications';

interface HomeState {
  recommends: $WebVideoInfo[];
  iPageIndex: number;
  total: number;
  isLoad: boolean;
  strTitleDetail: string;
  strDocsUrl: string;
  iCanAccess: number;
  strRefuseTips: string;
  isFirstLoad: boolean;
}

interface HomeActions {
  getList: (isReset?: boolean) => Promise<$GetFrontPageRsp | undefined>;
}

const initialState: HomeState = {
  recommends: [],
  iPageIndex: 1,
  total: Infinity,
  isLoad: false,
  strTitleDetail: '',
  strDocsUrl: '',
  iCanAccess: 0,
  strRefuseTips: '',
  isFirstLoad: true,
};

export const useHomeStore = create<HomeState & HomeActions>()(
  devtools((set, get) => ({
    ...initialState,
    getList: async (isReset = false) => {
      if (get().isLoad) {
        return;
      }

      if (isReset) {
        set({
          iPageIndex: 1,
          recommends: [],
        });
      }

      set({
        isLoad: true,
      });

      try {
        const res = await req.post('/lens_script/get_front_page', { iPageIndex: get().iPageIndex, iPageCnt: 20 });
        if (res.data.error_code === 0) {
          const isFirstLoad = get().isFirstLoad;

          set({
            recommends:
              isFirstLoad || isReset ? res.data.data.vecList : [...get().recommends, ...res.data.data.vecList],
            total: res.data.data.iTotal,
            iPageIndex: get().iPageIndex + 1,
            strTitleDetail: res.data.data.strTitleDetail,
            strDocsUrl: res.data.data.strDocsUrl,
            iCanAccess: res.data.data.iCanAccess,
            strRefuseTips: res.data.data.strRefuseTips,
            isFirstLoad: false,
          });
          return res.data.data;
        } else {
          notifications.show({
            title: '获取首页推荐列表失败',
            message: res.data.error_msg,
            color: 'red',
            autoClose: 5000,
          });
        }
      } catch (error) {
        console.error('获取首页推荐列表失败:', error);
        notifications.show({
          title: '获取首页推荐列表失败',
          message: '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      } finally {
        set({
          isLoad: false,
        });
      }
    },
  }))
);
