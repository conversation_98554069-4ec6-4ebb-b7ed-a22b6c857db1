import req from 'common/request';
import { notifications } from '@mantine/notifications';
import {
  emEditType,
  emScriptGetMask,
  $WebScriptInfo,
  $SongList,
  $AudioUrlRsp,
  $BasicInfoRsp,
  emBasicMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { StoreApi } from 'zustand';
import {
  $RoleInfo,
  $StoryboardInfo,
  $TopicInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { TopicRequest } from './types';

// 定义Store的部分类型，只包含我们需要的函数和状态
interface StoreState {
  scriptId: string;
  scriptInfo: $WebScriptInfo | null;
  fetchScriptInfoOnce: (eGetType: emScriptGetMask, onSuccess?: () => void) => void;
  // 请求排队相关状态
  isTopicRequestProcessing: boolean;
  topicRequestQueue: TopicRequest[];
}

// API动作集合
export const createApiActions = (set: StoreApi<StoreState>['setState'], get: StoreApi<StoreState>['getState']) => ({
  searchSongList: async (keyword: string) => {
    try {
      const res = await req.post('/lens_script/search_song_list', { strSongName: keyword });
      if (res.data.error_code === 0) {
        return res.data.data;
      }
      throw new Error(res.data.error_msg || '搜索歌曲失败');
    } catch (error) {
      console.error('搜索歌曲失败:', error);
      throw error;
    }
  },

  getSongInfo: async (song: $SongList) => {
    try {
      const res = await req.post('/lens_script/song_info', { strSongId: song.strSongId, strMid: song.strMid });
      if (res.data.error_code === 0) {
        return res.data.data;
      }
      throw new Error(res.data.error_msg || '获取歌曲信息失败');
    } catch (error) {
      console.error('获取歌曲信息失败:', error);
      throw error;
    }
  },

  // 获取音频文件
  getAudioUrl: async (songId: string): Promise<$AudioUrlRsp> => {
    try {
      const res = await req.post('/lens_script/get_audio_url', { strSongId: songId });
      if (res.data.error_code === 0) {
        return res.data.data as $AudioUrlRsp;
      }
      throw new Error(res.data.error_msg || '获取音频文件失败');
    } catch (error) {
      console.error('获取音频文件失败:', error);
      throw error;
    }
  },

  determineTopic: async (params: {
    strScriptId: string;
    vectorEditField: emEditType[];
    stTopic: { stSongInfo: any };
  }) => {
    try {
      const res = await req.post('/lens_script/determine_topic', params);
      if (res.data.error_code === 0) {
        return res.data.data;
      }
      throw new Error(res.data.error_msg || '确定主题失败');
    } catch (error) {
      console.error('确定主题失败:', error);
      throw error;
    }
  },

  /**
   * 修改主题相关信息
   * @param editType 修改类型
   * @param topicData 修改的主题数据
   * @param onSuccess 成功回调
   */
  modifyTopic: async (editType: emEditType, topicData: $TopicInfo, onSuccess?: () => void) => {
    const { scriptId, scriptInfo, fetchScriptInfoOnce, isTopicRequestProcessing, topicRequestQueue } = get();

    // 创建当前请求
    const currentRequest: TopicRequest = {
      editType,
      topicData,
      onSuccess,
    };

    // 如果有请求正在处理，则将当前请求加入队列并返回
    if (isTopicRequestProcessing) {
      console.log('主题修改请求已排队等待处理', currentRequest);
      set(state => ({
        topicRequestQueue: [...state.topicRequestQueue, currentRequest],
      }));
      return;
    }

    // 标记当前有请求正在处理
    set({ isTopicRequestProcessing: true });

    // 处理请求的函数
    const processRequest = async (request: TopicRequest) => {
      try {
        const res = await req.post('/lens_script/determine_topic', {
          strScriptId: scriptId,
          vectorEditField: [request.editType],
          stTopic: {
            ...scriptInfo?.stTopic,
            ...request.topicData,
          },
        });

        if (res.data.error_code === 0) {
          fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_TOPIC, () => {
            if (request.onSuccess) request.onSuccess();
          });
          return res.data.data;
        } else {
          throw new Error(res.data.error_msg || '主题修改失败');
        }
      } catch (error: any) {
        console.error('主题修改失败:', error);
        // 重新抛出错误，以便调用方可以捕获并处理UI通知
        throw error;
      } finally {
        const { topicRequestQueue } = get();
        if (topicRequestQueue.length > 0) {
          const nextRequest = topicRequestQueue[0];
          set(state => ({
            topicRequestQueue: state.topicRequestQueue.slice(1),
          }));
          await processRequest(nextRequest);
        } else {
          set({ isTopicRequestProcessing: false });
        }
      }
    };

    return processRequest(currentRequest);
  },

  /**
   * 更新分镜角色绑定关系(添加或删除角色)
   * EM_EDIT_STORYBOARD_TYPE_ROLEID = 41
   * @param stBoardInfo 分镜信息
   * @param role 角色信息，可以是单个角色或角色数组
   * @param isAdd 是否是添加操作，true为添加，false为删除
   */
  updateStoryboardRoles: (stBoardInfo: $StoryboardInfo, role: $RoleInfo | $RoleInfo[], isAdd: boolean) => {
    const { scriptId } = get();

    // 如果role数组为空且isAdd为true，则直接使用storyboard中的vecRoleId
    if (Array.isArray(role) && role.length === 0 && isAdd) {
      // 直接使用storyboard中已更新的vecRoleId
      console.log('[updateStoryboardRoles] 使用已更新的vecRoleId:', stBoardInfo.vecRoleId);
    } else {
      // 将单个角色转换为数组处理
      const roles = Array.isArray(role) ? role : [role];

      // 根据操作类型处理角色ID
      if (isAdd) {
        // 添加角色 - 确保不重复添加
        const roleIdsToAdd = roles.map(r => r.strId).filter(id => !stBoardInfo.vecRoleId.includes(id));
        if (roleIdsToAdd.length > 0) {
          stBoardInfo.vecRoleId = [...stBoardInfo.vecRoleId, ...roleIdsToAdd];
        }
      } else {
        // 删除角色
        const roleIdsToRemove = roles.map(r => r.strId);
        stBoardInfo.vecRoleId = stBoardInfo.vecRoleId.filter(id => !roleIdsToRemove.includes(id));
      }
    }

    return req
      .post('/lens_script/edit_storyboard', {
        strScriptId: scriptId,
        vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_ROLEID],
        stBoardInfo: [stBoardInfo],
      })
      .then(res => {
        if (res.data.error_code === 0 && res.data.data.iRes === 0) {
          return res.data;
          // 更新scriptInfo
          // notifications.show({
          //   title: isAdd ? '添加角色成功' : '删除角色成功',
          //   message: isAdd ? '角色已添加到当前分镜' : '角色已从当前分镜中移除',
          //   color: 'green',
          //   autoClose: 5000,
          // });
        } else {
          throw new Error(res.data.error_msg);
          // notifications.show({
          //   title: isAdd ? '添加角色失败' : '删除角色失败',
          //   message: res.data.error_msg,
          //   color: 'red',
          //   autoClose: 5000,
          // });
        }
      })
      .catch(error => {
        console.error(isAdd ? '添加角色失败:' : '删除角色失败:', error);
        notifications.show({
          title: '服务器异常',
          message: '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
        throw error;
      });
  },

  /**
   * 获取基础配置信息
   * @param scriptId 脚本ID，可选
   * @returns Promise<$BasicInfoRsp>
   */
  getBasicInfo: async (scriptId?: string): Promise<$BasicInfoRsp> => {
    try {
      const res = await req.post('/lens_script/basic_info', {
        strScriptId: scriptId || '',
        eMask: emBasicMask.EM_BASIC_MASK_ALL,
      });

      if (res.data.error_code === 0) {
        console.log('[getBasicInfo] 获取基础配置成功:', res.data.data);
        return res.data.data as $BasicInfoRsp;
      }

      throw new Error(res.data.error_msg || '获取基础配置失败');
    } catch (error) {
      console.error('获取基础配置失败:', error);
      throw error;
    }
  },

  /**
   * 切换分镜历史图片
   * @param storyboard 分镜信息
   * @returns Promise<any>
   */
  changeStoryboardHistoryPicture: async (storyboard: $StoryboardInfo) => {
    const { scriptId } = get();

    if (!scriptId) {
      throw new Error('脚本ID不存在');
    }

    console.log('[changeStoryboardHistoryPicture] 开始切换历史图片:', storyboard.strId, storyboard.stCurPic?.strId);

    try {
      const res = await req.post('/lens_script/edit_storyboard', {
        strScriptId: scriptId,
        vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_PIC],
        stBoardInfo: [storyboard],
      });

      if (res.data.error_code === 0 && res.data.data.iRes === 0) {
        console.log('[changeStoryboardHistoryPicture] API调用成功');
        notifications.show({
          message: '图片切换成功',
          color: 'green',
          autoClose: 2000,
        });
        return res.data;
      } else {
        throw new Error(res.data.error_msg || res.data.data.strTips || '切换图片失败');
      }
    } catch (error) {
      console.error('[changeStoryboardHistoryPicture] API调用失败:', error);
      notifications.show({
        title: '切换图片失败',
        message: error instanceof Error ? error.message : '网络错误，请稍后重试',
        color: 'red',
      });
      throw error;
    }
  },
});
