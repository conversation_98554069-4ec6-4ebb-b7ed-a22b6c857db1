import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  $BasicInfoRsp,
  $SongList,
  $WebScriptInfo,
  $GetScriptInfoRsp,
  emScriptGetMask,
  emEditType,
  $SearchSongListRsp,
  $SongInfoRsp,
  $DetermineTopicRsp,
  $AudioUrlRsp,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import {
  emFlow,
  emStatus,
  $StoryboardInfo,
  $RoleInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { notifications } from '@mantine/notifications';

import req from 'common/request';
import { createApiActions } from './apiActions';
import { TopicRequest } from './types'; // 导入TopicRequest类型

// 歌词选择范围类型定义
// ！注意！重要：因为歌词都是一句一句的，所以选取歌曲片段时长的时候，需要根据歌词的索引进行计算，从歌词的开始和结束时间为起止时间，不能用任意某个时间点作为片段的起止时间。
// 比如startTime应该取startIndex对应的歌词的开始时间iStartMs，endTime应该取endIndex对应的歌词的结束时间iEndMs。
// 时间轴拖动的时候，如果当前鼠标停在某一行歌词上，则开头取当前行歌词的开始时间，结尾取结尾行歌词的结束时间，也就是说要覆盖到所在行的歌词，不能舍弃。
export interface LyricSelection {
  startIndex: number; // 开始行的歌词索引 从0开始
  endIndex: number; // 结束行的歌词索引 从0开始
  startTime: number; // 开始行的歌词的开始时间 秒
  endTime: number; // 结束行的歌词的结束时间 秒
}

// 编辑器状态类型
interface EditorState {
  // 步骤状态
  currentStep: number | undefined;
  // 主题选择状态
  activeSubject: string;
  // 歌曲搜索和选择状态
  searchKeyword: string;
  searchList: $SongList[];
  selectedSong: $SongList | null;
  selectedStoryboardId: string; // 添加全局选中的分镜ID
  inputSummary: string; // 当前输入框里面的灵感
  lyricSelection: LyricSelection;
  // 基础配置状态
  showBasicConfig: boolean;
  basicConfig: $BasicInfoRsp;
  // 脚本相关状态
  scriptId: string;
  scriptInfo: $WebScriptInfo | null;
  scriptTips: string;
  isPolling: boolean;
  isFailed: boolean; // 当前大流程步骤生成失败了
  pollingInterval: ReturnType<typeof setTimeout> | null;
  isButtonLoading: boolean; // 大流程的主按钮加载状态 右下角步骤按钮，需要等当前步骤全部内容生成完毕后才能ready
  regenAllVideosLoading: boolean; // 重新生成所有视频加载状态
  isGeneratingAllVideos: boolean; // 全部生成视频按钮的立即加载状态
  isFormValid: boolean; // 表单验证状态
  // 主题请求排队相关状态
  isTopicRequestProcessing: boolean; // 是否有主题修改请求正在处理
  topicRequestQueue: TopicRequest[]; // 主题修改请求队列
  // 视频处理状态管理
  isUpdatingClientSideClip: boolean; // 是否正在更新客户端视频片段
  // 添加图片解析相关的state类型
  imageTags: string[]; // 图片解析出的标签
}

interface EditorActions {
  setInputSummary: (summary: string) => void;
  setCurrentStep: (step: number) => void;
  setActiveSubject: (subject: string) => void;
  setSearchKeyword: (keyword: string) => void;
  setSearchList: (list: $SongList[]) => void;
  setSelectedSong: (song: $SongList | null) => void;

  setLyricSelection: (selection: LyricSelection) => void;
  setShowBasicConfig: (show: boolean) => void;
  setBasicConfig: (config: $BasicInfoRsp) => void;
  setScriptId: (id: string) => void;
  setScriptInfo: (info: $WebScriptInfo | null) => void;
  setScriptTips: (tips: string) => void;
  resetState: () => void;
  setSelectedStoryboardId: (id: string) => void;
  // 添加统一的 scriptInfo 更新方法
  updateScriptInfo: (data: Partial<$WebScriptInfo>, source?: string) => void;
  mergeScriptInfoByType: (data: $WebScriptInfo, eGetType: emScriptGetMask, source?: string) => void;
  handlePolling: (
    eGetType: emScriptGetMask,
    onSuccess?: () => void,
    remainingAttempts?: number,
    checkType?: 'video' | 'picture' | 'all-videos' | 'topic' | 'other'
  ) => void;
  startPolling: (
    eGetType: emScriptGetMask,
    onSuccess?: () => void,
    maxAttempts?: number,
    checkType?: 'video' | 'picture' | 'all-videos' | 'topic' | 'other'
  ) => void;
  stopPolling: (from?: string) => void;
  setButtonLoading: (loading: boolean) => void;
  setGeneratingAllVideos: (isGenerating: boolean) => void;
  fetchScriptInfoOnce: (eGetType: emScriptGetMask, onSuccess?: () => void) => void;

  validateSubjectForm: () => boolean;
  validateStoryboardForm: () => boolean;
  validateCurrentStepForm: () => boolean;
  setImageTags: (tags: string[]) => void; // 设置图片解析标签
  // 视频处理状态管理方法
  setIsUpdatingClientSideClip: (isUpdating: boolean) => void;
  hasVideoGenerating: () => boolean; // 检查是否有视频正在生成中

  // 从apiActions中添加的方法
  modifyTopic: (editType: emEditType, topicData: any, onSuccess?: () => void) => Promise<any>;
  updateStoryboardRoles: (stBoardInfo: $StoryboardInfo, role: $RoleInfo | $RoleInfo[], isAdd: boolean) => Promise<any>;
  searchSongList: (keyword: string) => Promise<$SearchSongListRsp>;
  getSongInfo: (song: $SongList) => Promise<$SongInfoRsp>;
  determineTopic: (params: any) => Promise<$DetermineTopicRsp>;
  getAudioUrl: (songId: string) => Promise<$AudioUrlRsp>; // 获取音频文件
  getBasicInfo: (scriptId?: string) => Promise<$BasicInfoRsp>; // 获取基础配置信息
  changeStoryboardHistoryPicture: (storyboard: $StoryboardInfo) => Promise<any>; // 切换分镜历史图片
}

type EditorStore = EditorState & EditorActions;

// 初始状态
const initialState: EditorState = {
  currentStep: undefined,
  inputSummary: '',
  isFailed: false,
  activeSubject: 'MV',
  searchKeyword: '',
  searchList: [],
  selectedSong: null,
  selectedStoryboardId: '', // 添加全局选中的分镜ID

  lyricSelection: {
    startIndex: 1,
    endIndex: 5,
    startTime: 0,
    endTime: 30,
  },
  showBasicConfig: false,
  basicConfig: {
    vecPicMod: [],
    vecViMod: [],
    vecVideoSize: [],
    vecMovement: [],
    vecCompose: [],
    vecRegion: [],
  },
  scriptId: '',
  scriptInfo: null,
  scriptTips: '',
  isPolling: false,
  pollingInterval: null,
  isButtonLoading: false, // 初始化为false
  regenAllVideosLoading: false, // 重新生成所有视频
  isGeneratingAllVideos: false, // 初始化为false
  isFormValid: true, // 初始化为true
  // 主题请求排队相关状态初始化
  isTopicRequestProcessing: false,
  topicRequestQueue: [],
  // 视频处理状态初始化
  isUpdatingClientSideClip: false,
  imageTags: [], // 初始化图片解析标签为空数组
};

// 添加步骤与流程的映射关系，与index.tsx中的保持一致
const STEP_TO_FLOW_MAP = [emFlow.EM_FLOW_DETERMIN_TOPIC, emFlow.EM_FLOW_STORYBOARD, emFlow.EM_FLOW_PREVIEW];
const FLOW_TO_STEP_MAP: Record<number, number> = {
  [emFlow.EM_FLOW_DETERMIN_TOPIC]: 0,
  [emFlow.EM_FLOW_STORYBOARD]: 1,
  [emFlow.EM_FLOW_PREVIEW]: 2,
};

export const useEditorStore = create<EditorStore>()(
  devtools((set, get) => ({
    ...initialState,

    setCurrentStep: step => {
      const { scriptInfo } = get();

      // 如果没有scriptInfo，则仅设置步骤
      if (!scriptInfo) {
        set({ currentStep: step });
        return;
      }

      // 获取当前步骤对应的流程ID
      const currentFlow = STEP_TO_FLOW_MAP[step];

      if (currentFlow && scriptInfo.mapFlowStatus) {
        // 检查当前流程的状态
        const flowStatus = scriptInfo.mapFlowStatus[currentFlow as keyof typeof scriptInfo.mapFlowStatus];
        const isLoading = typeof flowStatus === 'number' && flowStatus <= emStatus.EM_STATUS_RUNNING;
        const isFailed = flowStatus === emStatus.EM_STATUS_FAIL;

        // 创建Error对象以获取堆栈跟踪
        // const stackError = new Error();
        console.log('setCurrentStep', step, currentFlow, isLoading, isFailed);
        // console.log('调用堆栈:', stackError.stack);

        // 更新状态
        set({
          currentStep: step,
          isFailed: isFailed,
        });
      } else {
        // 如果找不到对应的流程或状态信息，只更新步骤
        set({ currentStep: step });
      }
    },
    setActiveSubject: subject => set({ activeSubject: subject }),
    setSearchKeyword: keyword => set({ searchKeyword: keyword }),
    setSearchList: list => set({ searchList: list }),
    setSelectedSong: song => set({ selectedSong: song }),

    setInputSummary: summary => set({ inputSummary: summary }),
    setImageTags: tags => set({ imageTags: tags }), // 添加设置图片解析标签的方法

    // 视频处理状态管理方法实现
    setIsUpdatingClientSideClip: (isUpdating: boolean) => {
      set({ isUpdatingClientSideClip: isUpdating });
    },
    hasVideoGenerating: () => {
      const { scriptInfo, isGeneratingAllVideos } = get();

      // 优先检查立即生效的全部生成状态
      if (isGeneratingAllVideos) return true;

      if (!scriptInfo?.VecNewStoryboard) return false;

      // 检查是否有任意视频的状态为正在生成中
      return scriptInfo.VecNewStoryboard.some(
        storyboard => storyboard.stCurVideo?.eStatus === emStatus.EM_STATUS_RUNNING
      );
    },

    setLyricSelection: selection => set({ lyricSelection: selection }),
    setShowBasicConfig: show => set({ showBasicConfig: show }),
    setBasicConfig: config => set({ basicConfig: config }),
    setScriptId: id => set({ scriptId: id }),
    setScriptInfo: info => set({ scriptInfo: info }),
    setScriptTips: tips => set({ scriptTips: tips }),
    resetState: () => {
      // 清理轮询定时器，防止内存泄漏
      const { pollingInterval } = get();
      if (pollingInterval) {
        clearTimeout(pollingInterval);
        console.log('[resetState] 已清理轮询定时器');
      }

      set({
        ...initialState,
        isGeneratingAllVideos: false, // 确保重置状态包含新的字段
      });
    },
    setSelectedStoryboardId: id => set({ selectedStoryboardId: id }),
    setButtonLoading: loading => set({ isButtonLoading: loading }),
    setGeneratingAllVideos: isGenerating => set({ isGeneratingAllVideos: isGenerating }),

    // 统一的 scriptInfo 更新方法 - 防止竞态条件
    updateScriptInfo: (data: Partial<$WebScriptInfo>, source = 'unknown') => {
      console.log(`[updateScriptInfo] 来源: ${source}`, data);

      // 使用函数式更新确保基于最新状态
      set(state => ({
        scriptInfo: state.scriptInfo
          ? ({
              ...state.scriptInfo,
              ...data,
              // 确保关键字段不被意外覆盖
              strName: data.strName || state.scriptInfo.strName,
            } as $WebScriptInfo)
          : (data as $WebScriptInfo),
      }));
    },

    // 根据 eGetType 智能合并数据 - 避免数据丢失
    mergeScriptInfoByType: (data: $WebScriptInfo, eGetType: emScriptGetMask, source = 'unknown') => {
      console.log(`[mergeScriptInfoByType] 来源: ${source}, 类型: ${eGetType}`, data);

      set(state => {
        // 如果没有现有数据，直接设置
        if (!state.scriptInfo) {
          return { scriptInfo: data };
        }

        // 基于当前状态创建更新
        let updatedScriptInfo = {
          ...state.scriptInfo,
          // 始终更新的通用字段
          mapFlowStatus: data.mapFlowStatus || state.scriptInfo.mapFlowStatus,
          iIsAuto: data.iIsAuto !== undefined ? data.iIsAuto : state.scriptInfo.iIsAuto,
          eCurFlow: data.eCurFlow !== undefined ? data.eCurFlow : state.scriptInfo.eCurFlow,
          strVideoUrl: data.strVideoUrl || state.scriptInfo.strVideoUrl,
          strCoverImg: data.strCoverImg || state.scriptInfo.strCoverImg,
          iTimeRange: data.iTimeRange || state.scriptInfo.iTimeRange,
        };

        // 根据 eGetType 选择性更新特定字段
        switch (eGetType) {
          case emScriptGetMask.EM_SCRIPT_GET_TYPE_TOPIC:
            if (data.stTopic) updatedScriptInfo.stTopic = data.stTopic;
            if (data.vecRoleInfo) updatedScriptInfo.vecRoleInfo = data.vecRoleInfo;
            if (data.vecEnvInfo) updatedScriptInfo.vecEnvInfo = data.vecEnvInfo;
            break;

          case emScriptGetMask.EM_SCRIPT_GET_TYPE_STORYBOARD:
            if (data.VecNewStoryboard) updatedScriptInfo.VecNewStoryboard = data.VecNewStoryboard;
            if (data.stSubtitle) updatedScriptInfo.stSubtitle = data.stSubtitle;
            if (data.stAudio) updatedScriptInfo.stAudio = data.stAudio;
            break;

          case emScriptGetMask.EM_SCRIPT_GET_TYPE_MERGE:
            if (data.strVideoUrl) updatedScriptInfo.strVideoUrl = data.strVideoUrl;
            if (data.strCoverImg) updatedScriptInfo.strCoverImg = data.strCoverImg;
            break;

          case emScriptGetMask.EM_SCRIPT_GET_TYPE_ROLE:
            if (data.vecRoleInfo) updatedScriptInfo.vecRoleInfo = data.vecRoleInfo;
            break;

          case emScriptGetMask.EM_SCRIPT_GET_TYPE_ENV:
            if (data.vecEnvInfo) updatedScriptInfo.vecEnvInfo = data.vecEnvInfo;
            break;

          case emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL:
          default:
            // 全量更新，但保留重要字段
            updatedScriptInfo = {
              ...data,
              // 确保关键字段不丢失
              strName: data.strName || state.scriptInfo.strName,
            };
            break;
        }

        return { scriptInfo: updatedScriptInfo as $WebScriptInfo };
      });
    },

    // 大流程点击下一步之前，验证当前步骤的表单
    validateCurrentStepForm: () => {
      const { currentStep } = get();
      console.log('验证当前步骤表单:', currentStep);

      // 根据当前步骤调用对应的验证函数
      switch (currentStep) {
        case 0: // 主题确定
          return get().validateSubjectForm();
        case 1: // 分镜编辑步骤→合成视频
          return get().validateStoryboardForm();
        default:
          return true; // 默认返回true，不阻止流程
      }
    },

    // 验证Subject组件的表单
    validateSubjectForm: () => {
      const { scriptInfo, showBasicConfig, inputSummary } = get();

      // 不是主题确定阶段或者还没有显示基础配置，则不需要验证
      if (!showBasicConfig || !scriptInfo?.stTopic) {
        return true;
      }
      console.log('[validateSubjectForm]:', scriptInfo, inputSummary);
      // 检查主题灵感是否已填写
      if (!scriptInfo.stTopic.strInspirationSummary?.trim() || !inputSummary?.trim()) {
        notifications.show({
          title: '请填写故事主题和情绪',
          message: '故事主题和情绪不能为空',
          color: 'red',
          autoClose: 5000,
        });
        return false;
      }

      // mv品质
      if (!scriptInfo?.stTopic?.stPicModel?.vecInfo[0]?.strId) {
        notifications.show({
          title: '请选择MV品质',
          message: 'MV品质不能为空',
          color: 'red',
          autoClose: 5000,
        });
        return false;
      }
      // 检查是否选择了画面风格
      if (!scriptInfo.stTopic.stPicModel?.strId) {
        notifications.show({
          title: '请选择画面风格',
          message: '画面风格不能为空',
          color: 'red',
          autoClose: 5000,
        });
        return false;
      }

      // 检查是否选择了MV尺寸
      if (!scriptInfo.stTopic.stSize?.sizeName) {
        notifications.show({
          title: '请选择MV尺寸',
          message: 'MV尺寸不能为空',
          color: 'red',
          autoClose: 5000,
        });
        return false;
      }

      // 检查是否选择了MV类型
      if (!scriptInfo.stTopic.stPicModel?.strId) {
        notifications.show({
          title: '请选择MV类型',
          message: 'MV类型不能为空',
          color: 'red',
          autoClose: 5000,
        });
        return false;
      }

      return true;
    },

    // 验证Storyboard组件的表单
    validateStoryboardForm: () => {
      const { scriptInfo } = get();

      // 检查是否有分镜数据
      if (!scriptInfo?.VecNewStoryboard || scriptInfo.VecNewStoryboard.length === 0) {
        notifications.show({
          title: '请等待分镜生成完成',
          message: '分镜不能为空',
          color: 'red',
          autoClose: 5000,
        });
        return false;
      }

      // 检查每个分镜是否都已生成视频
      for (let i = 0; i < scriptInfo.VecNewStoryboard.length; i++) {
        const storyboard = scriptInfo.VecNewStoryboard[i];

        if (!storyboard.stCurVideo?.strVideo) {
          notifications.show({
            title: '请先确保所有分镜已生成视频',
            message: `分镜${i + 1}的视频尚未生成`,
            color: 'red',
            autoClose: 5000,
          });
          return false;
        }
      }

      return true;
    },

    startPolling: (
      eGetType: emScriptGetMask,
      onSuccess?: () => void,
      maxAttempts?: number,
      checkType?: 'video' | 'picture' | 'all-videos' | 'topic' | 'other'
    ) => {
      const { scriptId, isPolling } = get();
      const state = get();

      if (!scriptId) {
        console.error('轮询失败：scriptId不存在');
        return;
      }

      // 如果已经在轮询，先停止之前的轮询
      if (isPolling) {
        state.stopPolling();
      }

      // 设置轮询状态
      set({ isPolling: true });

      // 开始轮询
      state.handlePolling(eGetType, onSuccess, maxAttempts, checkType);
    },
    stopPolling: (from?: string) => {
      console.log('[stopPolling] 停止轮询，来源:', from);
      const { pollingInterval } = get();

      // 清理定时器，防止内存泄漏
      if (pollingInterval) {
        clearTimeout(pollingInterval);
        console.log('[stopPolling] 已清理定时器');
      }

      set({
        pollingInterval: null,
        isPolling: false,
        isButtonLoading: false, // 停止轮询时取消按钮加载状态
        isGeneratingAllVideos: false, // 停止轮询时重置全部生成视频状态
      });
    },

    // 轮询获取视频指定数据
    handlePolling: (
      eGetType: emScriptGetMask,
      onSuccess?: () => void,
      remainingAttempts: number = 1000,
      checkType: 'video' | 'picture' | 'all-videos' | 'topic' | 'other' = 'other'
    ) => {
      const { scriptId, isPolling, pollingInterval, stopPolling } = get();

      if (!scriptId || !isPolling) {
        return;
      }
      // 先清理已存在的定时器
      if (pollingInterval) {
        clearTimeout(pollingInterval);
      }
      if (remainingAttempts <= 0) {
        stopPolling('轮询达到最大尝试次数，停止轮询');
        return;
      }

      // 添加调试日志 - 轮询开始
      console.log(
        `[POLLING DEBUG] 开始轮询 - checkType: ${checkType}, 剩余次数: ${remainingAttempts}, 时间: ${new Date().toISOString()}`
      );

      req
        .post('/lens_script/get_script_info', {
          strScriptId: scriptId,
          eGetType,
        })
        .then((res: { data: { data: $GetScriptInfoRsp; error_code: number; error_msg: string } }) => {
          if (res.data.error_code !== 0) {
            notifications.show({
              title: '获取脚本信息失败',
              message: res.data.error_msg,
              color: 'red',
              autoClose: 5000,
            });

            console.error('获取脚本信息失败', res.data.error_msg);
            return;
          }

          const data = res.data.data.stData;

          // 添加调试日志 - 轮询数据
          const storyboards = data.VecNewStoryboard || [];
          const runningVideos = storyboards.filter(sb => sb.stCurVideo && sb.stCurVideo.eStatus === 1);
          const runningPictures = storyboards.filter(sb => sb.stCurPic && sb.stCurPic.eStatus === 1);
          console.log(
            `[POLLING DEBUG] 轮询数据 - 分镜总数: ${storyboards.length}, 生成中视频: ${runningVideos.length}, 生成中图片: ${runningPictures.length}`
          );

          if (runningVideos.length > 0) {
            console.log(
              `[POLLING DEBUG] 生成中的视频ID:`,
              runningVideos.map(sb => sb.strId)
            );
          }

          if (
            data.iIsAuto === 1 &&
            data.mapFlowStatus[emFlow.EM_FLOW_PREVIEW as keyof typeof data.mapFlowStatus] < emStatus.EM_STATUS_SUCC
          ) {
            // 不能用iIsAuto来判断，容易误判，需要让后台加一个判断当前是不是在一键成片的状态，
            console.log('[一键成片]:', data.eCurFlow);
            // 使用FLOW_TO_STEP_MAP更新当前步骤
            const mappedStep = FLOW_TO_STEP_MAP[data.eCurFlow];
            console.log('[mappedStep]:', mappedStep);
            if (mappedStep !== undefined) {
              get().setCurrentStep(mappedStep);
            }
          }

          // 添加调试日志 - 状态更新前
          console.log(`[POLLING DEBUG] 准备更新状态 - eGetType: ${eGetType}`);
          get().mergeScriptInfoByType(data, eGetType, 'handlePolling');

          set({ scriptTips: res.data.data.strTips });

          // 检查是否可以停止轮询
          const canStop = ((): boolean => {
            console.log('[POLLING DEBUG] 检查是否可以停止轮询 - checkType:', checkType, data.mapFlowStatus);

            // **一键成片模式特殊处理**
            if (data.iIsAuto === 1) {
              console.log('[POLLING DEBUG] 一键成片模式，检查所有流程状态');

              // 在一键成片模式下，必须等到预览流程（最后一步）完成才能停止轮询
              const previewFlowStatus = data.mapFlowStatus[
                emFlow.EM_FLOW_PREVIEW as keyof typeof data.mapFlowStatus
              ] as number | undefined;

              if (previewFlowStatus === emStatus.EM_STATUS_SUCC) {
                console.log('[POLLING DEBUG] 一键成片模式：预览流程已完成，可以停止轮询');
                return true;
              } else if (previewFlowStatus === emStatus.EM_STATUS_FAIL) {
                console.log('[POLLING DEBUG] 一键成片模式：预览流程失败，停止轮询');
                return true;
              } else {
                console.log('[POLLING DEBUG] 一键成片模式：预览流程未完成，继续轮询', previewFlowStatus);
                return false;
              }
            }

            // 通用检查逻辑 - 检查当前流程步骤的状态
            const { currentStep } = get();
            if (currentStep === undefined) return true;

            // 获取当前步骤对应的流程
            const currentFlow = STEP_TO_FLOW_MAP[currentStep];
            console.log('[currentFlow]:', currentFlow);

            if (!currentFlow || !data.mapFlowStatus) {
              return true;
            }

            // 使用类型安全的方式获取流程状态
            const flowStatus = data.mapFlowStatus[currentFlow as keyof typeof data.mapFlowStatus] as number | undefined;

            if (flowStatus === emStatus.EM_STATUS_RUNNING) {
              return false;
            }

            // 根据checkType判断是否继续轮询
            if (checkType === 'video') {
              // 检查视频是否已生成完成
              const storyboards = data.VecNewStoryboard || [];
              const selectedStoryboardId = get().selectedStoryboardId;

              console.log('轮询检查视频状态, 选中ID:', selectedStoryboardId, '分镜数量:', storyboards.length);
              if (storyboards.length === 0) {
                return false;
              }
              // 如果有特定选中的分镜，只检查该分镜的视频状态
              if (selectedStoryboardId) {
                const targetStoryboard = storyboards.find(sb => sb.strId === selectedStoryboardId);
                if (targetStoryboard && targetStoryboard.stCurVideo) {
                  console.log('检查分镜视频状态:', targetStoryboard.strId, targetStoryboard.stCurVideo.eStatus);
                  // 如果视频状态为成功或失败，停止轮询
                  const result =
                    targetStoryboard.stCurVideo.eStatus === emStatus.EM_STATUS_SUCC ||
                    targetStoryboard.stCurVideo.eStatus === emStatus.EM_STATUS_FAIL;
                  if (result) {
                    console.log(
                      '视频生成完成，状态:',
                      targetStoryboard.stCurVideo.eStatus === emStatus.EM_STATUS_SUCC ? '成功' : '失败'
                    );
                  } else {
                    console.log('视频生成中，继续轮询...');
                  }
                  return result;
                } else {
                  console.log('找不到目标分镜或分镜没有视频信息');
                  return false;
                }
              } else {
                // 否则检查所有分镜视频状态
                const allCompleted = !storyboards.some(
                  sb => sb.stCurVideo && sb.stCurVideo.eStatus === emStatus.EM_STATUS_RUNNING
                );
                console.log('检查所有视频状态:', allCompleted ? '全部完成' : '存在生成中视频');
                return allCompleted;
              }
            } else if (checkType === 'all-videos') {
              // 生成所有视频的轮询检查
              const storyboards = data.VecNewStoryboard || [];
              console.log('[POLLING DEBUG] 轮询检查所有视频状态, 分镜数量:', storyboards.length);

              if (storyboards.length === 0) {
                return false;
              }

              // 检查所有分镜是否都已完成图片生成和视频生成（成功或失败），或者存在正在生成的视频

              const allCompleted =
                !storyboards.some(sb => sb.stCurVideo && sb.stCurVideo.eStatus === emStatus.EM_STATUS_RUNNING) &&
                !storyboards.some(sb => sb.stCurPic && sb.stCurPic.eStatus === emStatus.EM_STATUS_RUNNING);

              console.log('[POLLING DEBUG] 检查所有视频生成状态:', allCompleted ? '全部完成' : '存在生成中视频');

              // 添加详细的状态分析
              if (!allCompleted) {
                const runningVideos = storyboards.filter(
                  sb => sb.stCurVideo && sb.stCurVideo.eStatus === emStatus.EM_STATUS_RUNNING
                );
                const runningPictures = storyboards.filter(
                  sb => sb.stCurPic && sb.stCurPic.eStatus === emStatus.EM_STATUS_RUNNING
                );
                console.log(
                  `[POLLING DEBUG] 还有 ${runningVideos.length} 个视频生成中, ${runningPictures.length} 个图片生成中`
                );
              } else {
                // 当全部视频生成完成时，自动刷新页面
                console.log('[POLLING DEBUG] 所有视频已生成完成，即将刷新页面');

                // 如果分镜数量大于60，则刷新页面
                if (storyboards.length > 60) {
                  window.location.reload();
                }
              }

              return allCompleted;
            } else if (checkType === 'picture') {
              // 检查分镜图片是否已生成完成
              console.log('[picture checkType]:', data.VecNewStoryboard);

              const storyboards = data.VecNewStoryboard || [];
              // 检查所有分镜的图片状态，如果没有任何一个分镜的图片处于"生成中"状态，则返回true表示可以停止轮询
              // 如果存在任何一个分镜的图片状态是"生成中"，则返回false表示需要继续轮询
              // 先判断数组长度，确保有分镜数据才进行检查
              if (storyboards.length > 0) {
                return !storyboards.some(sb => sb.stCurPic && sb.stCurPic.eStatus === emStatus.EM_STATUS_RUNNING);
              }
              // 如果没有分镜数据，则认为还处于生成中状态
              return false;
            } else if (checkType === 'topic') {
              // 确定主题阶段，检查主题是否已确定并且角色生成完成
              if (!data.stTopic.strId) {
                return false;
              }

              // 检查角色是否都已生成完成
              if (data.vecRoleInfo && data.vecRoleInfo.length > 0) {
                const hasRunningRole = data.vecRoleInfo.some(
                  role => role.eStatus && role.eStatus <= emStatus.EM_STATUS_RUNNING
                );
                if (hasRunningRole) {
                  console.log('[topic轮询] 角色还在生成中，继续轮询');
                  return false;
                }
              }

              console.log('[topic轮询] 主题和角色都已完成，停止轮询');
              return true;
            }

            return true;
          })();

          if (canStop) {
            console.log('[POLLING DEBUG] 轮询结束，状态满足停止条件', checkType);
            stopPolling('轮询结束，状态满足停止条件');

            // 更新加载状态
            const { currentStep } = get();
            if (currentStep !== undefined) {
              const currentFlow = STEP_TO_FLOW_MAP[currentStep];
              if (currentFlow && data.mapFlowStatus) {
                const flowStatus = data.mapFlowStatus[currentFlow as keyof typeof data.mapFlowStatus];
                console.log('[polling flowStatus]:', flowStatus);

                set({
                  isFailed: flowStatus === emStatus.EM_STATUS_FAIL,
                });
              }
            }

            // 调用成功回调
            if (onSuccess) {
              onSuccess();
            }
          } else {
            // 检查是否有任意一个流程状态为失败
            let anyFailed = false;
            if (data.mapFlowStatus) {
              // 使用类型安全的方式遍历mapFlowStatus
              Object.keys(data.mapFlowStatus).forEach(flowKey => {
                const flow = Number(flowKey);
                const flowStatus = data.mapFlowStatus[flow as keyof typeof data.mapFlowStatus] as number;
                if (flowStatus === emStatus.EM_STATUS_FAIL) {
                  anyFailed = true;
                }
              });
            }

            if (anyFailed) {
              console.log('[POLLING DEBUG] 轮询检测到流程状态失败，停止轮询');
              stopPolling('轮询检测到流程状态失败，停止轮询');
              set({ isFailed: true });
              if (onSuccess) {
                onSuccess();
              }
            } else {
              // 继续轮询
              console.log(`[POLLING DEBUG] 继续轮询 - 2秒后重试, 剩余次数: ${remainingAttempts - 1}`);
              const pollingInterval = setTimeout(() => {
                get().handlePolling(eGetType, onSuccess, remainingAttempts - 1, checkType);
              }, 2000);
              set({ pollingInterval });
            }
          }
        })
        .catch(error => {
          console.error('[POLLING DEBUG] 轮询请求失败:', error);

          // 检查是否仍在轮询状态（可能已被手动停止）
          const { isPolling } = get();
          if (!isPolling) {
            console.log('[handlePolling] 轮询已停止，不再重试');
            return;
          }

          // 减少重试次数，防止无限重试
          const newRemainingAttempts = remainingAttempts - 1;
          if (newRemainingAttempts <= 0) {
            console.error('[handlePolling] 达到最大重试次数，停止轮询');
            get().stopPolling('达到最大重试次数');
            return;
          }

          // 继续轮询，使用指数退避策略
          const retryDelay = Math.min(3000 * (1000 - newRemainingAttempts + 1), 10000); // 最大10秒
          console.log(`[handlePolling] ${retryDelay}ms 后重试，剩余尝试次数: ${newRemainingAttempts}`);

          const pollingInterval = setTimeout(() => {
            get().handlePolling(eGetType, onSuccess, newRemainingAttempts, checkType);
          }, retryDelay);
          set({ pollingInterval });
        });
    },

    // 只获取一次脚本信息，不轮询, 这里默认获取的是全部的数据。页面打开时初始化的时候调用
    fetchScriptInfoOnce: (eGetType: emScriptGetMask, onSuccess) => {
      const { scriptId, isPolling, isButtonLoading, currentStep } = get();
      console.log('一次性获取脚本信息', scriptId, '当前轮询状态:', isPolling, '按钮加载状态:', isButtonLoading);

      if (!scriptId) {
        console.log('获取失败: scriptId不存在');
        onSuccess?.();
        return;
      }

      set({ isButtonLoading: true }); // 设置加载状态

      req
        .post('/lens_script/get_script_info', {
          strScriptId: scriptId,
          eGetType,
        })
        .then((res: { data: { data: $GetScriptInfoRsp; error_code: number; error_msg: string } }) => {
          console.log('获取脚本信息结果:', res);

          if (res.data.error_code !== 0) {
            notifications.show({
              title: '获取脚本信息失败',
              message: res.data.error_msg,
              color: 'red',
              autoClose: 5000,
            });
            console.error('获取脚本信息失败', res.data.error_msg);
            return;
          }

          const webScriptInfoData = res.data.data.stData;

          if (
            webScriptInfoData.iIsAuto === 1 &&
            webScriptInfoData.mapFlowStatus[emFlow.EM_FLOW_PREVIEW as keyof typeof webScriptInfoData.mapFlowStatus] <
              emStatus.EM_STATUS_SUCC
          ) {
            // 不能用iIsAuto来判断，容易误判，需要让后台加一个判断当前是不是在一键成片的状态，
            console.log('[一键成片]:', webScriptInfoData.eCurFlow);
            // 使用FLOW_TO_STEP_MAP更新当前步骤
            const mappedStep = FLOW_TO_STEP_MAP[webScriptInfoData.eCurFlow];
            console.log('[mappedStep]:', mappedStep);
            if (mappedStep !== undefined) {
              get().setCurrentStep(mappedStep);
            }
          }

          // 使用统一的数据合并方法 - 防止竞态条件
          get().mergeScriptInfoByType(webScriptInfoData, eGetType, 'fetchScriptInfoOnce');

          // 更新提示信息
          set({ scriptTips: res.data.data.strTips });
          console.log(
            '[fetchScriptInfoOnce 当前流程状态]:',
            webScriptInfoData.mapFlowStatus[
              webScriptInfoData.eCurFlow as keyof typeof webScriptInfoData.mapFlowStatus
            ] <= emStatus.EM_STATUS_RUNNING
          );
          set({
            isFailed:
              webScriptInfoData.mapFlowStatus[
                webScriptInfoData.eCurFlow as keyof typeof webScriptInfoData.mapFlowStatus
              ] === emStatus.EM_STATUS_FAIL,
          });

          // 第一次拉取数据后，检查当前处于哪个步骤，如果处于分镜视频 即data.eCurFlow=3 ，这个步骤，则需要额外判断对应数据中的图片或视频是否已经全部生成，只要有一个还没生成，则开启轮询。
          const hasRunningStatus = (data: $WebScriptInfo): boolean => {
            console.log('[hasRunningStatus]:', data);
            // 检查主题状态
            if (data.stTopic?.eStatus && data.stTopic.eStatus <= emStatus.EM_STATUS_RUNNING) {
              return true;
            }

            // 检查角色状态
            if (data.vecRoleInfo) {
              for (const role of data.vecRoleInfo) {
                if (role.eStatus && role.eStatus <= emStatus.EM_STATUS_RUNNING) {
                  console.log('[hasRunningStatus] 角色生成中:', role.strName, '状态:', role.eStatus);
                  return true;
                }
              }
            }

            // 检查分镜状态
            if (data.VecNewStoryboard) {
              for (const storyboard of data.VecNewStoryboard) {
                // 检查分镜本身状态
                if (storyboard.eStatus && storyboard.eStatus <= emStatus.EM_STATUS_RUNNING) {
                  return true;
                }
                // 检查分镜图片状态
                if (storyboard.stCurPic?.eStatus && storyboard.stCurPic.eStatus <= emStatus.EM_STATUS_RUNNING) {
                  return true;
                }
                // 检查分镜视频状态
                if (storyboard.stCurVideo?.eStatus && storyboard.stCurVideo.eStatus <= emStatus.EM_STATUS_RUNNING) {
                  return true;
                }
              }
            }

            // 检查场景状态
            if (data.vecNewScene) {
              for (const scene of data.vecNewScene) {
                if (scene.eStatus && scene.eStatus <= emStatus.EM_STATUS_RUNNING) {
                  return true;
                }
              }
            }

            return false;
          };

          const currentFlow = webScriptInfoData.eCurFlow;
          let checkType: 'video' | 'picture' | 'all-videos' | 'topic' | 'other' = 'all-videos';

          // **一键成片模式特殊处理**
          if (webScriptInfoData.iIsAuto === 1) {
            console.log('[fetchScriptInfoOnce] 一键成片模式，检查是否需要轮询');

            // 检查预览流程状态，只要预览流程未完成就需要轮询
            const previewFlowStatus =
              webScriptInfoData.mapFlowStatus[emFlow.EM_FLOW_PREVIEW as keyof typeof webScriptInfoData.mapFlowStatus];
            const needPolling = typeof previewFlowStatus === 'number' && previewFlowStatus < emStatus.EM_STATUS_SUCC;

            console.log(
              '[fetchScriptInfoOnce] 一键成片模式 - 预览流程状态:',
              previewFlowStatus,
              '需要轮询:',
              needPolling
            );

            if (needPolling) {
              // 检查是否已经在轮询中，如果是，则不重新启动
              const { isPolling } = get();
              if (!isPolling) {
                console.log('[fetchScriptInfoOnce] 一键成片模式开始轮询，类型: EM_SCRIPT_GET_TYPE_ALL');
                get().startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, undefined, undefined, 'all-videos');
              } else {
                console.log('[fetchScriptInfoOnce] 一键成片模式已在轮询中，忽略此次轮询请求');
              }
            } else {
              set({ isButtonLoading: false }); // 请求完成，如果不需要轮询，则取消加载状态
            }

            if (onSuccess) onSuccess();
            return; // 一键成片模式下直接返回，不走下面的普通逻辑
          }

          // 检查当前是否处于需要轮询的步骤（普通模式）
          if (
            [emFlow.EM_FLOW_DETERMIN_TOPIC, emFlow.EM_FLOW_STORYBOARD, emFlow.EM_FLOW_PREVIEW].includes(currentFlow)
          ) {
            // 检查当前流程和前面所有流程的状态
            let needPolling =
              webScriptInfoData.mapFlowStatus[currentFlow as keyof typeof webScriptInfoData.mapFlowStatus] <=
              emStatus.EM_STATUS_RUNNING;

            // 如果当前流程不需要轮询，还需要检查前面的流程状态
            if (!needPolling) {
              // 获取当前步骤对应的流程在STEP_TO_FLOW_MAP中的索引
              const currentStepIndex = STEP_TO_FLOW_MAP.indexOf(currentFlow);

              // 检查前面所有流程的状态
              for (let i = 0; i < currentStepIndex; i++) {
                const previousFlow = STEP_TO_FLOW_MAP[i];
                const previousFlowStatus =
                  webScriptInfoData.mapFlowStatus[previousFlow as keyof typeof webScriptInfoData.mapFlowStatus];

                if (typeof previousFlowStatus === 'number' && previousFlowStatus <= emStatus.EM_STATUS_RUNNING) {
                  console.log(`前置流程 ${previousFlow} 状态为 ${previousFlowStatus}，需要轮询`);
                  needPolling = true;
                  break;
                }
              }
            }

            const needPollFlow = currentStep === undefined ? webScriptInfoData.eCurFlow : STEP_TO_FLOW_MAP[currentStep];
            console.log('[needPollFlow]:', needPollFlow);

            // 根据当前步骤判断是否需要轮询
            if (!needPolling) {
              if (needPollFlow === emFlow.EM_FLOW_DETERMIN_TOPIC || needPollFlow === emFlow.EM_FLOW_STORYBOARD) {
                console.log('[needPolling hasRunningStatus ]:', currentFlow, hasRunningStatus(webScriptInfoData));
                needPolling = hasRunningStatus(webScriptInfoData);
              } else if (needPollFlow === emFlow.EM_FLOW_PREVIEW) {
                // 检查视频是否已生成
                // 如果视频URL为空，则需要轮询
                needPolling = !webScriptInfoData.strVideoUrl;
              }
            }

            console.log('needPolling', needPolling);
            // 如果需要轮询，则开启轮询
            if (needPolling) {
              console.log(`当前在步骤${currentStep}中，检测到有内容未生成，开启轮询`);
              // 设置适当的轮询类型，根据当前步骤设置不同的轮询类型
              let pollingType = emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL;

              if (needPollFlow === emFlow.EM_FLOW_DETERMIN_TOPIC) {
                pollingType = emScriptGetMask.EM_SCRIPT_GET_TYPE_TOPIC;
                checkType = 'topic';
              } else if (needPollFlow === emFlow.EM_FLOW_STORYBOARD) {
                pollingType = emScriptGetMask.EM_SCRIPT_GET_TYPE_STORYBOARD;
              } else if (needPollFlow === emFlow.EM_FLOW_PREVIEW) {
                pollingType = emScriptGetMask.EM_SCRIPT_GET_TYPE_MERGE;
              }

              // 检查是否已经在轮询中，如果是，则不重新启动
              const { isPolling } = get();
              if (!isPolling) {
                console.log('fetchScriptInfoOnce开始轮询，类型:', pollingType, checkType);
                get().startPolling(pollingType, undefined, undefined, checkType);
              } else {
                console.log('已在轮询中，忽略此次轮询请求');
              }
            } else {
              set({ isButtonLoading: false }); // 请求完成，如果不需要轮询，则取消加载状态
            }
          } else {
            set({ isButtonLoading: false }); // 请求完成，如果不需要轮询，则取消加载状态
          }

          console.log('[set scriptInfo]:', webScriptInfoData);

          if (onSuccess) onSuccess();
        })
        .catch(error => {
          console.error('获取脚本信息失败', error);
          set({ isButtonLoading: false }); // 请求失败，取消加载状态
          onSuccess?.();
        });
    },

    // 集成API actions
    ...createApiActions(set, get),
  }))
);

// 导出响应式 selector，用于自动计算 isFlowLoading
export const selectIsFlowLoading = (state: EditorStore): boolean => {
  const { scriptInfo, currentStep } = state;

  // 如果没有scriptInfo或currentStep，返回false
  if (!scriptInfo || currentStep === undefined) {
    return false;
  }

  // 获取当前步骤对应的流程
  const currentFlow = STEP_TO_FLOW_MAP[currentStep];
  if (!currentFlow || !scriptInfo.mapFlowStatus) {
    return false;
  }

  // 检查当前流程的状态是否为运行中
  const flowStatus = scriptInfo.mapFlowStatus[currentFlow as keyof typeof scriptInfo.mapFlowStatus];
  return typeof flowStatus === 'number' && flowStatus <= emStatus.EM_STATUS_RUNNING;
};

// 添加分镜相关的选择器
export const selectAllStoryboards = (state: EditorStore) => state.scriptInfo?.VecNewStoryboard || [];
export const selectStoryboardById = (id: string) => (state: EditorStore) =>
  state.scriptInfo?.VecNewStoryboard?.find(sb => sb.strId === id) || null;
export const selectHasStoryboards = (state: EditorStore) =>
  !!state.scriptInfo?.VecNewStoryboard && state.scriptInfo.VecNewStoryboard.length > 0;
