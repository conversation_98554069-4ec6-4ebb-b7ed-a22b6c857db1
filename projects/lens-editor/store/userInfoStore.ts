// 个人中心
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

import { notifications } from '@mantine/notifications';
import {
  $GetPersonInfoRsp,
  EmGetPersonInfoMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import req from 'common/request';

interface UserInfoStore {
  userInfo: $GetPersonInfoRsp | null;
  isLoading: boolean;
  fetchData: () => Promise<void>;
  updateUserInfo: (userInfo: $GetPersonInfoRsp) => void;
}

export const useUserInfoStore = create<UserInfoStore>()(
  devtools((set, get) => ({
    userInfo: null,
    isLoading: false,
    fetchData: async () => {
      req
        .post('/lens_script/get_person_info', {
          IGetMask: EmGetPersonInfoMask.EM_GET_PERSON_INFO_TYPE_ALL,
        })
        .then(res => {
          if (res.data.error_code === 0) {
            set({ userInfo: res.data.data });
          } else {
            notifications.show({
              title: '获取个人信息失败',
              message: res.data.error_msg,
              color: 'red',
            });
          }
        })
        .catch(err => {
          notifications.show({
            title: '获取个人信息失败',
            message: err.message,
            color: 'red',
          });
        });
    },
    updateUserInfo: (userInfo: $GetPersonInfoRsp) => {
      set({ userInfo });
    },
  }))
);
