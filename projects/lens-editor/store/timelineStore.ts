import { create } from 'zustand';
import { CustomTimelineRow, TLActionWithName } from '../components/VideoEditor/types';
import { processVideoData } from '../components/VideoEditor/processors/videoProcessor';
import { processOptimizedAudioData } from '../components/VideoEditor/processors/audioProcessor';
import { processSubtitleData } from '../components/VideoEditor/processors/subtitleProcessor';
import { VisibleSprite, AudioClip, ImgClip, MP4Clip } from '@webav/av-cliper';
import { TimelineState, TimelineAction } from '@xzdarcy/react-timeline-editor';
import {
  $AudioItem,
  $SubtitleItem,
  $VideoInfo,
  emStatus,
  $StoryboardInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { HARDWARE_ACCELERATION } from '../constants';
import { useEditorStore } from '../store/editorStore';
import { $WebScriptInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { sortActionsByTime, validateActionsOrder, getSortedActions } from '../utils/index';

// 视频缓存最大大小
const MAX_VIDEO_CACHE_SIZE = 15;

// 加载时间轴数据锁
const loadTimelineDataLock = {
  isLoading: false,
};

interface TimelineStoreState {
  // 时间轴数据
  timelineData: CustomTimelineRow[];
  // 时间轴动作到Sprite的映射
  actionSpriteMap: WeakMap<TimelineAction, VisibleSprite>;
  // 当前选中的动作对应的数据
  selectedAction: $VideoInfo | $SubtitleItem | $AudioItem | undefined | null;
  // 当前活动的动作
  activeAction: TLActionWithName | null;
  // 加载时间轴数据
  loadTimelineData: () => Promise<void>;
  // 设置选中的动作
  setSelectedAction: (action: $VideoInfo | $SubtitleItem | $AudioItem | undefined | null) => void;
  // 设置活动的动作
  setActiveAction: (action: TLActionWithName | null) => void;
  // 直接设置时间轴数据
  setTimelineData: (data: CustomTimelineRow[]) => void;
  // 获取AVCanvas实例
  avCanvas: any;
  // 设置AVCanvas实例
  setAVCanvas: (avCvs: any) => void;
  // 获取TimelineState实例
  tlState: React.MutableRefObject<TimelineState | undefined> | null;
  // 设置TimelineState实例
  setTLState: (tlState: React.MutableRefObject<TimelineState | undefined>) => void;
  // 向actionSpriteMap添加映射
  addToSpriteMap: (action: TimelineAction, sprite: VisibleSprite) => void;
  // 从actionSpriteMap获取sprite
  getSpriteFromMap: (action: TimelineAction) => VisibleSprite | undefined;
  // 从actionSpriteMap删除mapping
  removeFromSpriteMap: (action: TimelineAction) => void;
  // 视频资源缓存
  videoCache: Map<string, any>;
  // 音频资源缓存
  audioCache: Map<string, AudioClip>;
  // 默认图片缓存 - 缓存已加载的图像二进制数据
  defaultImgCache: Uint8Array | null;
  // 默认图片ImgClip缓存 - 缓存创建的ImgClip对象
  defaultImgClipCache: ImgClip | null;
  // 设置默认图片ImgClip缓存
  setDefaultImgClipCache: (imgClip: ImgClip) => void;
  // 上一次加载的脚本信息，用于差异对比
  prevScriptInfo: $WebScriptInfo | null;
  // 设置上一次加载的脚本信息
  setPrevScriptInfo: (prevScriptInfo: $WebScriptInfo | null) => void;
  // 强制刷新时间轴的key
  forceUpdateKey: number;
  // 强制刷新时间轴
  forceUpdate: () => void;
  // 检查分镜图是否已更新
  checkStoryboardImagesUpdated: (scriptInfo: $WebScriptInfo | null) => boolean;
  // 添加精细化更新方法
  updateVideoTrack: (storyboards: $StoryboardInfo[]) => Promise<void>;
  updateAudioTrack: (audioData: any) => Promise<void>;
  updateSubtitleTrack: (subtitleData: any) => Promise<void>;

  // 单个视频action的更新
  updateSingleVideoAction: (storyboardId: string, storyboard: $StoryboardInfo) => Promise<void>;

  // 精细化差异检测
  getVideoTrackChanges: (
    prevStoryboards: $StoryboardInfo[],
    currentStoryboards: $StoryboardInfo[]
  ) => {
    added: $StoryboardInfo[];
    updated: $StoryboardInfo[];
    removed: string[];
    unchanged: $StoryboardInfo[];
  };

  // 获取特定轨道数据
  getTrackById: (trackId: string) => CustomTimelineRow | undefined;

  // 更新特定轨道
  updateTrackById: (trackId: string, actions: TLActionWithName[]) => void;

  // 新增：统一视频缓存管理
  // 获取视频剪辑（优先从缓存获取，没有则加载并缓存）
  getVideoClip: (videoUrl: string) => Promise<MP4Clip>;

  // 清理特定视频缓存
  clearVideoClip: (videoUrl: string) => void;

  // 清理所有视频缓存
  clearAllVideoCache: () => void;

  // 新增：重置时间轴存储
  resetTimelineStore: () => void;
}

export const useTimelineStore = create<TimelineStoreState>((set, get) => ({
  timelineData: [],
  actionSpriteMap: new WeakMap(),
  selectedAction: null,
  activeAction: null,
  avCanvas: null,
  tlState: null,
  videoCache: new Map(),
  audioCache: new Map(),
  defaultImgCache: null,
  defaultImgClipCache: null,
  prevScriptInfo: null,
  forceUpdateKey: 0,
  setPrevScriptInfo: prevScriptInfo => {
    set({ prevScriptInfo });
  },

  setAVCanvas: avCvs => {
    set({ avCanvas: avCvs });
  },

  setTLState: tlState => {
    set({ tlState });
  },

  setDefaultImgClipCache: imgClip => {
    set({ defaultImgClipCache: imgClip });
  },

  addToSpriteMap: (action, sprite) => {
    const actionSpriteMap = get().actionSpriteMap;
    actionSpriteMap.set(action, sprite);
  },

  getSpriteFromMap: action => {
    const actionSpriteMap = get().actionSpriteMap;
    return actionSpriteMap.get(action);
  },

  removeFromSpriteMap: action => {
    const actionSpriteMap = get().actionSpriteMap;
    actionSpriteMap.delete(action);
  },

  forceUpdate: () => {
    set(state => ({ forceUpdateKey: state.forceUpdateKey + 1 }));
  },

  // 重置时间轴存储方法
  resetTimelineStore: () => {
    console.log('[resetTimelineStore] 开始重置时间轴存储');

    // 清理加载锁
    loadTimelineDataLock.isLoading = false;

    // 获取当前状态
    const { videoCache, audioCache, avCanvas, timelineData, actionSpriteMap } = get();

    // 先将时间轴数据设为空，防止渲染过程中访问已被销毁的资源
    set({ timelineData: [] });

    // 立即同步清理actionSpriteMap (WeakMap没有size属性，我们只能直接创建新的)
    console.log('[resetTimelineStore] 创建新的actionSpriteMap');
    set({ actionSpriteMap: new WeakMap<TimelineAction, VisibleSprite>() });

    // 同步清理画布上的所有sprite
    if (avCanvas) {
      try {
        console.log('[resetTimelineStore] 清理画布sprites');
        const wasPlaying = avCanvas.playing;
        if (wasPlaying) {
          avCanvas.pause();
        }

        // 保存当前sprites的引用
        const sprites = [...(avCanvas.sprites || [])];

        // 立即从画布中移除所有sprite
        const removePromises = sprites.map(sprite => {
          try {
            return avCanvas.removeSprite(sprite).catch((error: Error) => {
              console.error('移除sprite失败:', error);
              return null;
            });
          } catch (error: unknown) {
            console.error('尝试移除sprite时出错:', error);
            return Promise.resolve(null);
          }
        });

        // 等待所有sprite移除完成
        Promise.all(removePromises).then(() => {
          // 然后再异步销毁sprite资源
          setTimeout(() => {
            sprites.forEach(sprite => {
              try {
                sprite.destroy();
              } catch (e) {
                console.error('销毁sprite失败:', e);
              }
            });
          }, 100);
        });
      } catch (e) {
        console.error('清理画布失败:', e);
      }
    }

    // 同步清理视频缓存
    if (videoCache.size > 0) {
      console.log('[resetTimelineStore] 清理视频缓存，数量:', videoCache.size);
      const oldVideoCache = new Map(videoCache);
      videoCache.clear(); // 立即清空缓存引用

      // 异步销毁视频资源
      setTimeout(() => {
        oldVideoCache.forEach((clip, url) => {
          try {
            clip.destroy();
            console.log('[清理视频缓存]:', url);
          } catch (e) {
            console.error(`清理视频缓存失败 ${url}:`, e);
          }
        });
      }, 100);
    }

    // 同步清理音频缓存
    if (audioCache.size > 0) {
      console.log('[resetTimelineStore] 清理音频缓存，数量:', audioCache.size);
      const oldAudioCache = new Map(audioCache);
      audioCache.clear(); // 立即清空缓存引用

      // 异步销毁音频资源
      setTimeout(() => {
        oldAudioCache.forEach((clip, url) => {
          try {
            clip.destroy();
            console.log('[清理音频缓存]:', url);
          } catch (e) {
            console.error(`清理音频缓存失败 ${url}:`, e);
          }
        });
      }, 100);
    }

    // 重置状态
    set({
      selectedAction: null,
      activeAction: null,
      prevScriptInfo: null,
      forceUpdateKey: get().forceUpdateKey + 1, // 强制更新
    });

    console.log('[resetTimelineStore] 时间轴存储已重置');
  },

  // 新增：统一视频缓存管理方法
  getVideoClip: async videoUrl => {
    // 规范化URL
    const normalizedUrl = videoUrl.replace('http://', 'https://');
    const { videoCache } = get();

    try {
      // 尝试从缓存获取
      if (videoCache.has(normalizedUrl)) {
        const cachedClip = videoCache.get(normalizedUrl);
        if (cachedClip) {
          console.log('[从缓存获取视频]:', normalizedUrl);
          return await cachedClip.clone();
        }
      }

      // 缓存未命中，加载新视频
      console.log('[缓存未命中，加载新视频]:', normalizedUrl);
      const response = await fetch(normalizedUrl);
      if (!response.ok) {
        throw new Error(`获取视频失败: ${response.status} ${response.statusText}`);
      }

      const videoClip = new MP4Clip(response.body!, {
        __unsafe_hardwareAcceleration__: HARDWARE_ACCELERATION,
      });
      await videoClip.ready;

      // 缓存管理 - 如果缓存过大，清理一部分
      if (videoCache.size >= MAX_VIDEO_CACHE_SIZE) {
        // 移除最早添加的5个缓存项
        const oldEntries = Array.from(videoCache.entries()).slice(0, 5);
        for (const [url, clip] of oldEntries) {
          try {
            clip.destroy();
            videoCache.delete(url);
            console.log('[清理过期视频缓存]:', url);
          } catch (e) {
            console.error('清理旧视频失败:', e);
          }
        }
      }

      // 添加到缓存
      videoCache.set(normalizedUrl, videoClip);
      console.log('[添加视频到缓存]:', normalizedUrl);

      return await videoClip.clone();
    } catch (error) {
      console.error('获取视频剪辑失败:', error);
      throw error;
    }
  },

  clearVideoClip: videoUrl => {
    const normalizedUrl = videoUrl.replace('http://', 'https://');
    const { videoCache } = get();

    if (videoCache.has(normalizedUrl)) {
      try {
        videoCache.get(normalizedUrl)?.destroy();
        videoCache.delete(normalizedUrl);
        console.log('[清理视频缓存]:', normalizedUrl);
      } catch (e) {
        console.error('清理视频缓存失败:', e);
      }
    }
  },

  clearAllVideoCache: () => {
    const { videoCache } = get();

    videoCache.forEach((clip, url) => {
      try {
        clip.destroy();
        console.log('[清理视频缓存]:', url);
      } catch (e) {
        console.error(`清理视频缓存失败 ${url}:`, e);
      }
    });

    videoCache.clear();
    console.log('[已清理所有视频缓存]');
  },

  checkStoryboardImagesUpdated: scriptInfo => {
    const prevScriptInfo = get().prevScriptInfo;
    if (!prevScriptInfo || !scriptInfo) return false;

    const prevStoryboards = prevScriptInfo.VecNewStoryboard || [];
    const currentStoryboards = scriptInfo.VecNewStoryboard || [];

    for (const current of currentStoryboards) {
      const prev = prevStoryboards.find((p: $StoryboardInfo) => p.strId === current.strId);
      if (!prev) continue;

      const prevHasValidPic = prev.stCurPic?.strPicUrl && prev.stCurPic?.eStatus === emStatus.EM_STATUS_SUCC;
      const currentHasValidPic = current.stCurPic?.strPicUrl && current.stCurPic?.eStatus === emStatus.EM_STATUS_SUCC;

      if (!prevHasValidPic && currentHasValidPic) {
        return true;
      }
    }

    return false;
  },

  loadTimelineData: async () => {
    // 检查执行锁
    if (loadTimelineDataLock.isLoading) {
      console.log('[TIMELINE DEBUG] 正在加载中，跳过本次执行');
      return;
    }

    // 获取执行锁
    loadTimelineDataLock.isLoading = true;

    try {
      const t1 = new Date();
      const state = get();

      // 从editorStore获取当前脚本信息
      const scriptInfo = useEditorStore.getState().scriptInfo;
      console.log('[TIMELINE DEBUG] 开始加载数据，分镜数量:', scriptInfo?.VecNewStoryboard?.length || 0);

      // 添加内存监控
      if ((performance as any).memory) {
        console.log('[TIMELINE DEBUG] 内存使用情况 - 开始加载:', {
          used: Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024) + 'MB',
          total: Math.round((performance as any).memory.totalJSHeapSize / 1024 / 1024) + 'MB',
          limit: Math.round((performance as any).memory.jsHeapSizeLimit / 1024 / 1024) + 'MB',
        });
      }

      // 如果没有脚本信息或分镜，则返回空轨道
      if (!scriptInfo?.VecNewStoryboard?.length) {
        set({ timelineData: [] });
        return; // 注意这里需要 return，因为后面loadTimelineDataLock.isLoading = false会在finally中处理
      }

      // 获取或创建actionSpriteMap
      const actionSpriteMap = state.actionSpriteMap || new WeakMap();

      // 差异更新处理 - 检查脚本是否真的发生变化
      const prevScriptInfo = state.prevScriptInfo;
      const isFirstLoad = !prevScriptInfo;
      let needFullReload = isFirstLoad;
      console.log('[TIMELINE DEBUG] 是否首次加载:', isFirstLoad, new Date().getTime());

      // 非首次加载时进行简单比较，判断是否需要全量重新加载
      if (!isFirstLoad) {
        // 简单比较，如果分镜数量或ID发生变化，则需要全量重新加载
        const prevStoryboards = prevScriptInfo.VecNewStoryboard || [];
        const currentStoryboards = scriptInfo.VecNewStoryboard || [];

        console.log('[TIMELINE DEBUG] 数据比较:', {
          prev分镜数量: prevStoryboards.length,
          current分镜数量: currentStoryboards.length,
          需要全量重载: needFullReload,
        });

        // 检查分镜数量是否变化
        if (prevStoryboards.length !== currentStoryboards.length) {
          needFullReload = true;
          console.log('[TIMELINE DEBUG] 分镜数量变化，需要全量重载');
        } else {
          // 检查分镜ID、视频URL和分镜图状态是否变化
          for (let i = 0; i < currentStoryboards.length; i++) {
            const prev = prevStoryboards[i] as $StoryboardInfo;
            const current = currentStoryboards[i] as $StoryboardInfo;

            if (
              prev.strId !== current.strId ||
              prev.stCurVideo.strVideo !== current.stCurVideo.strVideo ||
              prev.stCurVideo.iStartTs !== current.stCurVideo.iStartTs ||
              prev.stCurVideo.iEndTs !== current.stCurVideo.iEndTs ||
              prev.stCurVideo.eStatus !== current.stCurVideo.eStatus ||
              prev.stCurPic?.strPicUrl !== current.stCurPic?.strPicUrl ||
              prev.stCurPic?.eStatus !== current.stCurPic?.eStatus
            ) {
              needFullReload = true;
              console.log('[TIMELINE DEBUG] 分镜内容变化，需要全量重载:', {
                分镜ID: current.strId,
                视频状态变化: prev.stCurVideo.eStatus !== current.stCurVideo.eStatus,
                图片状态变化: prev.stCurPic?.eStatus !== current.stCurPic?.eStatus,
              });
              break;
            }
          }
        }

        // 检查音频数据是否变化
        if (!needFullReload && scriptInfo.stAudio && prevScriptInfo.stAudio) {
          const prevAudioTracks = prevScriptInfo.stAudio.vecAudio || [];
          const currentAudioTracks = scriptInfo.stAudio.vecAudio || [];

          // 检查音频轨道数量是否变化
          if (prevAudioTracks.length !== currentAudioTracks.length) {
            needFullReload = true;
          } else {
            // 检查音频内容是否变化
            for (let i = 0; i < currentAudioTracks.length; i++) {
              const prevTrack = prevAudioTracks[i];
              const currentTrack = currentAudioTracks[i];

              const prevContent = prevTrack.vecContent || [];
              const currentContent = currentTrack.vecContent || [];

              if (prevContent.length !== currentContent.length) {
                needFullReload = true;
                break;
              }

              // 检查每个音频项是否变化
              for (let j = 0; j < currentContent.length; j++) {
                const prevAudio = prevContent[j];
                const currentAudio = currentContent[j];

                if (
                  prevAudio.strId !== currentAudio.strId ||
                  prevAudio.strAudioUrl !== currentAudio.strAudioUrl ||
                  prevAudio.iStartTimeMs !== currentAudio.iStartTimeMs ||
                  prevAudio.iStopTimeMs !== currentAudio.iStopTimeMs ||
                  prevAudio.iAudioStartTimeMs !== currentAudio.iAudioStartTimeMs ||
                  prevAudio.iAudioStopTimeMs !== currentAudio.iAudioStopTimeMs
                ) {
                  needFullReload = true;
                  break;
                }
              }

              if (needFullReload) break;
            }
          }
        }

        // 特别检查：如果分镜图从无到有或状态从生成中变为成功，强制重新加载
        if (!needFullReload) {
          needFullReload = get().checkStoryboardImagesUpdated(scriptInfo);
          if (needFullReload) {
            console.log('[TIMELINE DEBUG] 分镜图已更新，需要重新加载时间轴');
          }
        }
      }

      // 如果需要全量重新加载，则清理画布上的所有sprite
      if (needFullReload) {
        console.log('[TIMELINE DEBUG] 进行全量重新加载');

        // 添加资源清理监控
        const currentSprites = state.avCanvas?.sprites?.length || 0;
        const currentVideoCache = state.videoCache.size;
        const currentAudioCache = state.audioCache.size;

        console.log('[TIMELINE DEBUG] 清理前资源统计:', {
          Sprites: currentSprites,
          视频缓存: currentVideoCache,
          音频缓存: currentAudioCache,
        });

        // 采用并行处理方式，且每个轨道处理完成后立即更新UI
        // 初始化一个空的轨道数据结构
        const initialTracks: CustomTimelineRow[] = [];

        // 首先设置一个空的轨道数据，后续会逐步更新
        set({
          timelineData: initialTracks,
          actionSpriteMap,
        });

        // 存储最终完成的各轨道数据
        const completedTracks: {
          audio?: CustomTimelineRow;
          video?: CustomTimelineRow;
          subtitle?: CustomTimelineRow[];
        } = {};

        // 创建更新UI的函数，每个轨道处理完成后会调用
        const updateTimelineData = () => {
          const currentTracks: CustomTimelineRow[] = [];

          // 按照视频、字幕、音频的顺序组装最终数据

          if (completedTracks.video) {
            currentTracks.push(completedTracks.video);
          }
          if (completedTracks.subtitle) {
            currentTracks.push(...completedTracks.subtitle);
          }

          if (completedTracks.audio) {
            currentTracks.push(completedTracks.audio);
          }

          // 更新UI
          set({ timelineData: currentTracks });
          console.log(
            '[TIMELINE DEBUG] 更新时间轴数据:',
            currentTracks.length,
            '个轨道',
            new Date().getTime() - t1.getTime()
          );

          // 添加内存监控
          if ((performance as any).memory) {
            console.log('[TIMELINE DEBUG] 内存使用情况 - 轨道更新后:', {
              used: Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024) + 'MB',
              total: Math.round((performance as any).memory.totalJSHeapSize / 1024 / 1024) + 'MB',
            });
          }
        };

        // 并行处理音频和字幕数据
        const audioPromise = processOptimizedAudioData(
          scriptInfo,
          state.avCanvas,
          get().timelineData,
          actionSpriteMap,
          state.audioCache
        ).then(audioActions => {
          console.log(
            '[TIMELINE DEBUG] 音频轨道处理完成:',
            audioActions.length,
            '个动作',
            new Date().getTime() - t1.getTime()
          );
          completedTracks.audio = { id: '1-audio', actions: audioActions };
          updateTimelineData();
          return audioActions;
        });

        const subtitlePromise = processSubtitleData(scriptInfo, state.avCanvas, actionSpriteMap).then(
          subtitleTracks => {
            console.log(
              '[TIMELINE DEBUG] 字幕轨道处理完成:',
              subtitleTracks.length,
              '个轨道',
              new Date().getTime() - t1.getTime()
            );
            completedTracks.subtitle = subtitleTracks;
            updateTimelineData();
            return subtitleTracks;
          }
        );

        // 统一处理视频轨道：根据分镜实际情况直接加载视频或分镜图
        const videoActions = await processVideoData(
          scriptInfo.VecNewStoryboard,
          state.avCanvas,
          get().timelineData,
          actionSpriteMap,
          state.videoCache,
          state.defaultImgCache,
          imgData => set({ defaultImgCache: imgData }),
          state.defaultImgClipCache,
          imgClip => get().setDefaultImgClipCache(imgClip)
        );

        completedTracks.video = { id: '3-video', actions: videoActions };
        updateTimelineData(); // 立即更新UI，显示视频轨道
        console.log(
          '[TIMELINE DEBUG] 视频轨道处理完成:',
          videoActions.length,
          '个动作',
          new Date().getTime() - t1.getTime()
        );

        // 等待音频和字幕处理完成
        await Promise.all([audioPromise, subtitlePromise]);

        // 保存当前脚本信息（作为下次比较的基准）
        set({ prevScriptInfo: JSON.parse(JSON.stringify(scriptInfo)) });

        // 最终内存监控
        if ((performance as any).memory) {
          console.log('[TIMELINE DEBUG] 内存使用情况 - 加载完成:', {
            used: Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024) + 'MB',
            total: Math.round((performance as any).memory.totalJSHeapSize / 1024 / 1024) + 'MB',
            增长:
              Math.round(
                ((performance as any).memory.usedJSHeapSize - ((window as any).initialMemory || 0)) / 1024 / 1024
              ) + 'MB',
          });
        }
      } else {
        console.log('[TIMELINE DEBUG] 脚本未发生实质变化，跳过重新加载');
      }
    } finally {
      // 释放执行锁
      loadTimelineDataLock.isLoading = false;
    }
  },

  setSelectedAction: data => {
    set({ selectedAction: data });
  },

  setActiveAction: action => {
    set({ activeAction: action });
  },

  setTimelineData: data => {
    set({ timelineData: data });
  },

  // 精细化差异检测 - 分析视频轨道的具体变化
  getVideoTrackChanges: (prevStoryboards, currentStoryboards) => {
    const changes = {
      added: [] as $StoryboardInfo[],
      updated: [] as $StoryboardInfo[],
      removed: [] as string[],
      unchanged: [] as $StoryboardInfo[],
    };

    // 创建映射便于查找
    const prevMap = new Map(prevStoryboards.map(sb => [sb.strId, sb]));
    const currentMap = new Map(currentStoryboards.map(sb => [sb.strId, sb]));

    // 检查新增和更新
    for (const current of currentStoryboards) {
      const prev = prevMap.get(current.strId);

      if (!prev) {
        // 新增的分镜
        changes.added.push(current);
      } else {
        // 检查是否有实质性变化
        const hasVideoChanged =
          prev.stCurVideo?.strVideo !== current.stCurVideo?.strVideo ||
          prev.stCurVideo?.eStatus !== current.stCurVideo?.eStatus ||
          prev.stCurVideo?.iStartTs !== current.stCurVideo?.iStartTs ||
          prev.stCurVideo?.iEndTs !== current.stCurVideo?.iEndTs;

        const hasPictureChanged =
          prev.stCurPic?.strPicUrl !== current.stCurPic?.strPicUrl ||
          prev.stCurPic?.eStatus !== current.stCurPic?.eStatus;

        const hasTimeChanged = prev.iTimeRange !== current.iTimeRange;

        if (hasVideoChanged || hasPictureChanged || hasTimeChanged) {
          changes.updated.push(current);
        } else {
          changes.unchanged.push(current);
        }
      }
    }

    // 检查删除的分镜
    for (const prev of prevStoryboards) {
      if (!currentMap.has(prev.strId)) {
        changes.removed.push(prev.strId);
      }
    }

    return changes;
  },

  // 获取特定轨道数据
  getTrackById: trackId => {
    const { timelineData } = get();
    return timelineData.find(track => track.id === trackId);
  },

  // 更新特定轨道 - 添加排序逻辑
  updateTrackById: (trackId, actions) => {
    const { timelineData } = get();

    // 确保 actions 按时间排序
    const sortedActions = getSortedActions(actions, `轨道${trackId}`);

    const updatedTracks = timelineData.map(track =>
      track.id === trackId ? { ...track, actions: sortedActions } : track
    );

    set({ timelineData: updatedTracks });

    console.log(`[updateTrackById] 轨道 ${trackId} 已更新并排序，包含 ${sortedActions.length} 个 actions`);
  },

  // 更新单个视频action
  updateSingleVideoAction: async (storyboardId, storyboard) => {
    const state = get();
    const videoTrack = state.getTrackById('3-video');

    if (!videoTrack || !state.avCanvas) return;

    try {
      // 找到需要更新的action索引
      const actionIndex = videoTrack.actions.findIndex(action => action.id === storyboardId);

      if (actionIndex === -1) {
        // 如果action不存在，创建新的
        const newActions = await processVideoData(
          [storyboard],
          state.avCanvas,
          state.timelineData,
          state.actionSpriteMap,
          state.videoCache,
          state.defaultImgCache,
          imgData => set({ defaultImgCache: imgData }),
          state.defaultImgClipCache,
          imgClip => state.setDefaultImgClipCache(imgClip)
        );

        if (newActions.length > 0) {
          const updatedActions = [...videoTrack.actions, ...newActions];
          // 使用统一的排序方法
          state.updateTrackById('3-video', updatedActions);
        }
      } else {
        // 更新现有action
        const oldAction = videoTrack.actions[actionIndex];

        // 清理旧的sprite
        const oldSprite = state.getSpriteFromMap(oldAction);
        if (oldSprite && state.avCanvas) {
          await state.avCanvas.removeSprite(oldSprite);
          state.removeFromSpriteMap(oldAction);
          setTimeout(() => {
            try {
              oldSprite.destroy();
            } catch (e) {
              console.error('销毁sprite失败:', e);
            }
          }, 100);
        }

        // 创建新的action和sprite
        const newActions = await processVideoData(
          [storyboard],
          state.avCanvas,
          state.timelineData,
          state.actionSpriteMap,
          state.videoCache,
          state.defaultImgCache,
          imgData => set({ defaultImgCache: imgData }),
          state.defaultImgClipCache,
          imgClip => state.setDefaultImgClipCache(imgClip)
        );

        if (newActions.length > 0) {
          const updatedActions = [...videoTrack.actions];
          updatedActions[actionIndex] = newActions[0];
          state.updateTrackById('3-video', updatedActions);
        }
      }
    } catch (error) {
      console.error('更新单个视频action失败:', error);
    }
  },

  // 更新整个视频轨道
  updateVideoTrack: async storyboards => {
    const state = get();
    if (!state.avCanvas) return;

    console.log('[updateVideoTrack] 更新视频轨道:', storyboards.length);

    try {
      // 获取变化信息
      const prevStoryboards = state.prevScriptInfo?.VecNewStoryboard || [];
      const changes = state.getVideoTrackChanges(prevStoryboards, storyboards);

      console.log('[视频轨道变化]:', changes);

      // 如果没有任何变化，直接返回
      if (changes.added.length === 0 && changes.updated.length === 0 && changes.removed.length === 0) {
        console.log('[视频轨道无变化，跳过更新]');
        return;
      }

      const videoTrack = state.getTrackById('3-video');
      const currentActions = videoTrack ? [...videoTrack.actions] : [];

      // 处理删除的分镜
      for (const removedId of changes.removed) {
        const actionIndex = currentActions.findIndex(action => action.id === removedId);
        if (actionIndex !== -1) {
          const action = currentActions[actionIndex];
          const sprite = state.getSpriteFromMap(action);
          if (sprite && state.avCanvas) {
            await state.avCanvas.removeSprite(sprite);
            state.removeFromSpriteMap(action);
            setTimeout(() => {
              try {
                sprite.destroy();
              } catch (e) {
                console.error('销毁sprite失败:', e);
              }
            }, 100);
          }
          currentActions.splice(actionIndex, 1);
        }
      }

      // 处理更新的分镜
      for (const updatedStoryboard of changes.updated) {
        await state.updateSingleVideoAction(updatedStoryboard.strId, updatedStoryboard);
      }

      // 处理新增的分镜
      for (const newStoryboard of changes.added) {
        await state.updateSingleVideoAction(newStoryboard.strId, newStoryboard);
      }

      console.log('[视频轨道更新完成]');
    } catch (error) {
      console.error('更新视频轨道失败:', error);
    }
  },

  // 更新音频轨道
  updateAudioTrack: async audioData => {
    const state = get();
    if (!state.avCanvas) return;

    console.log('[updateAudioTrack] 更新音频轨道');

    try {
      const audioActions = await processOptimizedAudioData(
        { stAudio: audioData } as any,
        state.avCanvas,
        state.timelineData,
        state.actionSpriteMap,
        state.audioCache
      );

      state.updateTrackById('1-audio', audioActions);
      console.log('[音频轨道更新完成]');
    } catch (error) {
      console.error('更新音频轨道失败:', error);
    }
  },

  // 更新字幕轨道
  updateSubtitleTrack: async subtitleData => {
    const state = get();
    if (!state.avCanvas) return;

    console.log('[updateSubtitleTrack] 更新字幕轨道');

    try {
      // 首先清理旧的字幕Sprite
      const { timelineData, actionSpriteMap } = state;
      const oldSubtitleTracks = timelineData.filter(track => track.id.includes('subtitle'));

      console.log('[updateSubtitleTrack] 清理旧的字幕Sprite，轨道数量:', oldSubtitleTracks.length);

      // 清理旧的字幕sprites
      for (const track of oldSubtitleTracks) {
        for (const action of track.actions) {
          const sprite = actionSpriteMap.get(action);
          if (sprite && state.avCanvas) {
            console.log('[updateSubtitleTrack] 移除旧的字幕sprite:', action.id);
            try {
              await state.avCanvas.removeSprite(sprite);
              actionSpriteMap.delete(action);
              // 异步销毁sprite资源
              setTimeout(() => {
                try {
                  sprite.destroy();
                } catch (e) {
                  console.error('销毁字幕sprite失败:', e);
                }
              }, 100);
            } catch (error) {
              console.error('移除字幕sprite失败:', error);
            }
          }
        }
      }

      // 创建新的字幕轨道
      console.log('[updateSubtitleTrack] 创建新的字幕轨道');
      const subtitleTracks = await processSubtitleData(
        { stSubtitle: subtitleData } as any,
        state.avCanvas,
        actionSpriteMap
      );

      // 移除旧的字幕轨道，保留其他轨道
      const nonSubtitleTracks = timelineData.filter(track => !track.id.includes('subtitle'));

      // 添加新的字幕轨道
      const updatedTracks = [...nonSubtitleTracks, ...subtitleTracks];

      // 按轨道类型排序：视频、字幕、音频
      updatedTracks.sort((a, b) => {
        const getOrder = (id: string) => {
          if (id.includes('video')) return 1;
          if (id.includes('subtitle')) return 2;
          if (id.includes('audio')) return 3;
          return 4;
        };
        return getOrder(a.id) - getOrder(b.id);
      });

      set({ timelineData: updatedTracks });
      console.log('[字幕轨道更新完成]，新轨道数量:', subtitleTracks.length);
    } catch (error) {
      console.error('更新字幕轨道失败:', error);
    }
  },
}));
