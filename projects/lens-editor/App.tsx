import React, { useEffect, useState, Component, ErrorInfo } from 'react';
import {
  MantineProvider,
  MantineColorScheme,
  defaultVariantColorsResolver,
  VariantColorsResolver,
} from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { HashRouter } from 'react-router-dom';
import Layout from './components/Layout';
import AppRoutes from './routes';
import { useLocalStorage } from '@mantine/hooks';
import { useLoginModal } from './components/LoginModal';
import { ConfirmationProvider } from './components/ConfirmationContext';

import { addLoginEventListener, removeLoginEventListener } from '../../common/request';

// 修正导入路径，使用相对路径
import btnBg from './assets/images/pages/btn_bg.png';

// 添加错误边界组件
class ErrorBoundary extends Component<{ children: React.ReactNode }, { hasError: boolean; error: Error | null }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('应用出错:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: 'red', backgroundColor: '#333', margin: '20px', borderRadius: '8px' }}>
          <h2>应用出现错误</h2>
          <p>{this.state.error?.message || '未知错误'}</p>
          <pre>{this.state.error?.stack}</pre>
          <button
            style={{
              padding: '8px 16px',
              marginTop: '10px',
              cursor: 'pointer',
              background: '#666',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
            }}
            onClick={() => window.location.reload()}>
            重新加载
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

const variantColorResolver: VariantColorsResolver = input => {
  const defaultResolvedColors = defaultVariantColorsResolver(input);

  if (input.variant === 'awsome') {
    return {
      background: `url(${btnBg}) no-repeat center center / 100% 100%`,
      hover: '#2C384B',
      color: '#fff',
      border: 'none',
    };
  }

  if (input.variant === 'primary') {
    return {
      background: '#3C485B',
      hover: '#2C384B',
      color: '#6AFFA3',
      border: 'none',
    };
  }

  // if (input.variant === 'proccess') {
  //   return {
  //     background: 'rgba(255, 255, 255, 0.1)',
  //     hover: 'rgba(255, 255, 255, 0.3)',
  //     color: '#fff',
  //     border: 'none',
  //   };
  // }

  return defaultResolvedColors;
};

export default function App() {
  const [colorScheme, setColorScheme] = useLocalStorage<MantineColorScheme>({
    key: 'mantine-color-scheme',
    defaultValue: 'dark',
  });
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const { openLoginModal, LoginModal } = useLoginModal();

  useEffect(() => {
    console.log('App组件useEffect执行1');

    // 添加登录事件监听器，当登录失效时自动打开登录弹窗
    const handleLoginRequired = () => {
      openLoginModal();
    };

    addLoginEventListener(handleLoginRequired);

    // 组件卸载时移除监听器
    return () => {
      removeLoginEventListener(handleLoginRequired);
    };
  }, []);

  // 登录成功回调
  const handleLoginSuccess = () => {
    setIsLoggedIn(true);
    console.log('登录成功，刷新页面');
    location.reload();
  };

  return (
    <ErrorBoundary>
      <MantineProvider
        defaultColorScheme={colorScheme}
        theme={{
          variantColorResolver,
          components: {
            Button: {
              defaultProps: {
                radius: 'xl',
                color: '#3C485B',
              },
            },
          },
        }}>
        <Notifications
          style={{ position: 'fixed', top: '10px', left: '50%', transform: 'translate(-50%, 0%)', zIndex: 1000 }}
        />
        <ConfirmationProvider>
          <HashRouter>
            <Layout isLoggedIn={isLoggedIn}>
              <AppRoutes />
            </Layout>
            <LoginModal
              onLoginSuccess={() => {
                handleLoginSuccess();
              }}
            />
          </HashRouter>
        </ConfirmationProvider>
      </MantineProvider>
    </ErrorBoundary>
  );
}
