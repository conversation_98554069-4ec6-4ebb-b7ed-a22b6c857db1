
## 项目背景
现在我要开发一个pc端的web项目，项目主要内容是：一个分步操作的mv视频制作应用。具体内容：第一步：用户选择歌曲，截取片段，设定一些背景信息等。第二步：基于ai大模型自动生成视频分镜脚本，支持用户进行修改，这里会分为多个场景，每个场景有1~2个分镜，场景支持增删改查。 第三步：根据前面的分镜脚本生成图片，这一步就只有分镜的概念，没有场景概念了，用户可以对每一个分镜进行单独生成，生成的时候，支持用户自定义文生图相关prompt。第四步：根据图片生成5秒左右的视频，每个分镜图片都会生成一个视频，同时，这一步也是最复杂的，需要包括视频模型的选择和效果的选择等，以及 视频合成剪辑工具区，剪辑工具区包含字幕、旁白、音频、视频四个轨道，这里我会用webav这个开源项目来实现视频编辑相关的能力。 第五步是合成最终的视频。 除此之外，还有一些弹框，编辑表单等模块，以及首页和个人中心等展示页面。    另外，除了分步操作，也支持一键生成，一键生成的话，需要轮询后台接口，同步展示当前的进度。 后台接口由另外的同学负责开发，我只负责web前端的开发。

ui框架：mantine 7.16.3 版本

## 项目目录结构
common 是公共模块，里面是一些公共的组件和方法
projects 是项目目录，里面是各个项目的目录
projects/lens-editor 是编辑器项目目录
projects/main-page 是官网主站项目目录
jce 是后台协议目录

## 后台接口协议：
jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce
jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce
对应的ts声明：
jce/interact/aigc/proto_lens_script/proto_lens_script_svr.d.ts
jce/interact/aigc/proto_lens_script/proto_lens_script_comm.d.ts

## 业务逻辑备忘
后台所有异步数据都是通过 /lens_script/get_script_info 接口拉取的，这里需要注意的是，有些步骤，返回的数据是分批的，比如 生成场景脚本、分镜图片、视频，都是先生成非媒体类的信息，然后过一段时间，图片视频等才会生成，才代表当前环节完成。比如场景脚本有文字和角色，角色里有图片，那么文字和角色的信息会先返回，然后过一段时间，图片信息才会返回，此时，才代表当前环节完成。 所以轮询的时候，这里就需要注意一下，需要根据返回的数据，来判断当前环节是否完成。