import React from 'react';

const SvgComp: React.FC = () => {
  return (
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        display: 'none',
      }}>
      <symbol id="placeholder" viewBox="0 0 100 95" fill="none">
        <path
          d="M21.424 0C20.8077 0 20.3067 0.50323 20.3067 1.12234V41.3236C20.3067 41.7792 20.0981 42.1967 19.7346 42.4681C19.3711 42.7396 18.9128 42.8206 18.4782 42.6904C17.8729 42.5078 17.4667 41.9586 17.4667 41.3236V14.7873C17.4667 13.1872 16.4916 11.8219 14.9839 11.3092C13.4762 10.7964 11.8752 11.2854 10.908 12.5553C10.4228 13.1919 10.1558 13.9857 10.1558 14.7873V40.1901C10.1558 40.8092 10.6567 41.3125 11.2731 41.3125C11.8895 41.3125 12.3905 40.8092 12.3905 40.1901V14.7873C12.3905 14.4762 12.4948 14.1682 12.6828 13.9206C13.1396 13.3221 13.8002 13.2776 14.2664 13.4364C14.7326 13.5951 15.2304 14.0333 15.2304 14.7873V41.3236C15.2304 42.9587 16.2767 44.3715 17.8349 44.8414C18.1826 44.9462 18.5398 44.9986 18.8954 44.9986C19.6777 44.9986 20.43 44.7462 21.0669 44.2699C22.004 43.5698 22.5414 42.4967 22.5414 41.3236V1.12234C22.5414 0.50323 22.0404 0 21.424 0Z"
          fill="url(#paint0_linear_676_285)"
        />
        <path
          d="M6.19523 22.1621C5.57887 22.1621 5.07788 22.6653 5.07788 23.2845V37.5289C5.07788 38.148 5.57887 38.6512 6.19523 38.6512C6.81158 38.6512 7.31257 38.148 7.31257 37.5289V23.2845C7.31257 22.6669 6.81158 22.1621 6.19523 22.1621Z"
          fill="url(#paint1_linear_676_285)"
        />
        <path
          d="M1.11893 29.8652C0.502568 29.8652 0 30.3685 0 30.9876V34.8944C0 35.5135 0.500988 36.0167 1.11893 36.0167C1.73528 36.0167 2.23627 35.5135 2.23627 34.8944V30.9876C2.23627 30.3685 1.73528 29.8652 1.11893 29.8652Z"
          fill="url(#paint2_linear_676_285)"
        />
        <path d="M25.3828 0L48 34.5609L25.3828 6.388V0Z" fill="url(#paint3_linear_676_285)" />
        <path d="M25.3828 12.7754L48 34.5603L25.3828 19.165V12.7754Z" fill="url(#paint4_linear_676_285)" />
        <path d="M25.3828 25.5547L48 34.562L25.3828 31.9427V25.5547Z" fill="url(#paint5_linear_676_285)" />
        <path d="M25.3828 44.7188L48 34.5605L25.3828 38.3292V44.7188Z" fill="url(#paint6_linear_676_285)" />
        <defs>
          <linearGradient
            id="paint0_linear_676_285"
            x1="16.3476"
            y1="2.05724"
            x2="16.3476"
            y2="45.3077"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_676_285"
            x1="6.19461"
            y1="0.321197"
            x2="6.19461"
            y2="43.1258"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_676_285"
            x1="1.11804"
            y1="0.327845"
            x2="1.11804"
            y2="41.8909"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
          <linearGradient
            id="paint3_linear_676_285"
            x1="47.9992"
            y1="17.2802"
            x2="25.3824"
            y2="17.2802"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
          <linearGradient
            id="paint4_linear_676_285"
            x1="47.9992"
            y1="23.6679"
            x2="25.3824"
            y2="23.6679"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
          <linearGradient
            id="paint5_linear_676_285"
            x1="47.9992"
            y1="30.058"
            x2="25.3824"
            y2="30.058"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
          <linearGradient
            id="paint6_linear_676_285"
            x1="47.9992"
            y1="39.639"
            x2="25.3824"
            y2="39.639"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#69FFA3" />
            <stop offset="0.9999" stopColor="#215DFF" />
          </linearGradient>
        </defs>
      </symbol>
    </svg>
  );
};

export default SvgComp;
