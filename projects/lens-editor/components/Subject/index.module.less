@import '../../../../common/styles/variables.less';

.fullScreenOverlay {
  &[class] {
    position: fixed;
    inset: 0;
    z-index: 1000;
  }
}

.grayTip {
  color: #888;
  font-size: 12px;
  margin-left: 8px;
}

.subjectCard {
  width: 372px;
  height: 104px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;



  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &.active {
    background: @green-bg-color ;
    border-color: @green-border-color;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .subjectName {
    font-style: italic;
  }
}

.songbox {
  background: #0A0F17;
  border-radius: 8px;
}

.betaTag {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 2px 6px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.progressBar {
  width: 100%;
  height: 80px;
}

.lyricsContainer {
  position: relative;
  max-height: calc(100vh - 410px);
  overflow-y: auto;
  padding: 8px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }

  &::before,
  &::after {
    display: none;
  }
}

.lyricItem {
  padding: 5px 8px !important;
  transition: background-color 0.2s;
  cursor: pointer;
  color: #676e79;
  font-size: 14px;
  border-top: 2px solid transparent;
  border-bottom: 2px solid transparent;

  &:hover {
    background-color: rgba(0, 255, 255, 0.05);
  }

  &.selected {
    background-color: rgba(186, 238, 255, 0.10);
    color: #69FFA3;


  }

  &.topBoundary,
  &.bottomBoundary {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: -6px;
      right: 0;
      width: 18px;
      height: 10px;
      background: url('projects/lens-editor/assets/images/pages/lyc_handle.png') no-repeat center center;
      background-size: 18px 10px;
    }
  }

  &.topBoundary {
    cursor: ns-resize !important;
    border-top: 2px solid #69FFA3;

  }


  &.bottomBoundary {
    cursor: ns-resize !important;
    border-bottom: 2px solid #69FFA3;

    &::after {
      top: auto;
      bottom: -6px;
    }
  }
}

.topExtend,
.bottomExtend {
  cursor: ns-resize !important;
}

.timelineContainer {
  width: 100%;
  padding: 10px 0;
}

.timeLabels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  color: #69FFA3;
  font-size: 12px;
}

.lyrbox {
  background: @bg-color;
  border-radius: 8px;
  gap: 14px !important;
  background: #1A202B;
  color: #565C65;
}

.lyricsScroll {
  max-height: calc(24px * 6);
  /* 假设每行歌词高度约为24px，最多显示6行 */
  overflow-y: auto;


  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }
}

.picMod {
  &[class] {
    display: flex;
    align-items: flex-end;
    box-sizing: content-box;
    position: relative;
    cursor: pointer;
    background-position: center top;
    border: 1px solid @gray-color-2;

    &.selected {
      border: 1px solid @green-slt-color;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 255, 153, 0.10);
        z-index: 1;
        pointer-events: none;
      }

      .picModText {
        color: @green-slt-color;
      }
    }
  }
}

.picModText {
  &[class] {
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 8px;
    font-size: 0.8rem;
    position: relative;
    z-index: 2;
  }
}

.timeline {
  position: relative;
  height: 48px;
  background: #0A0F17;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  touch-action: none;

  &::before {
    content: '';
    position: absolute;
    top: 11px;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('projects/lens-editor/assets/images/pages/audio_timeline_bg.png') repeat-x 0 center;
    background-size: 110px 26px;
  }
}

.selectedRange {
  position: relative;
  height: 48px;
  background: rgba(186, 238, 255, 0.2);
  border: 1px solid rgba(105, 255, 163, 1);
  overflow: hidden;
  border-radius: 6px;
  cursor: move;
  user-select: none;
  touch-action: none;

  &:hover {
    background: rgba(186, 238, 255, 0.25);
  }

  &:active {
    background: rgba(186, 238, 255, 0.3);
  }
}

.handle {
  position: absolute;
  top: 0;
  width: 16px;
  height: 100%;
  background: rgba(105, 255, 163, 1);
  cursor: ew-resize;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;

  &::after {
    content: '';
    background: rgba(31, 33, 39, 0.3);
    width: 1px;
    height: 16px;
  }
}

.totalDuration {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  color: var(--mantine-color-gray-6);
  font-size: 12px;
}

.styleOption {
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: scale(1.05);
  }

  &.selected {
    border: 2px solid var(--mantine-color-cyan-5);
  }
}

.timelineWrapper {
  width: 100%;
  display: flex;
}

.playBtn {
  margin: 23px 0 0 10px;
}

// 歌词选中时长
.timeLabels {
  position: absolute;
  top: -20px;
  width: 100%;
}

.wrap {
  background-color: #29313E;
  position: relative;
  border-radius: 10px;
  width: 1200px;
  margin: 160px auto 0;
  overflow: hidden;
}

.contentWrap {
  width: 1200px;
  margin: 0 auto;
}

.wrapBg {
  background-image: url(../../assets/images/pages/subjectBg.jpg);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 379px;
  background-size: cover;

}

.container {
  width: 100%;
  height: 100%;
  z-index: 1;
  padding: 0 32px;
  padding-top: 100px;
}

.title {
  color: #FFF;

  font-family: "PingFang SC";
  font-size: 36px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: -15px;
  margin-bottom: 8px;
}

.subTitle {
  color: #FFF;

  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.mvIcon {
  width: 21px;
  height: 16px;
  flex-shrink: 0;
  background-image: url(../../assets/images/pages/mvIcon.png);
}

.songSearchWrap {
  background-color: #0A0F17;
  border-radius: 8px;
}

.songSearchInput {
  width: 856px;
  height: 68px;
  background-color: #0A0F17;
  border-radius: 8px;
  border-width: 0;
  padding: 24px;
}

.songName:hover {
  color: #69FFA3;
}

.songName {
  cursor: pointer;
  padding: 0 24px;
  margin-bottom: 32px;
  font-size: 14px;
}

.picModWrap {
  &[class] {
    overflow: visible;
  }
}

.picModViewport {
  height: 92px;

  &[class] {
    overflow: visible;
  }
}

// 角色选择相关样式
.roleSelectWrap {
  &[class] {
    overflow: visible;
  }
}

.roleSelectViewport {
  &[class] {
    overflow: visible;
  }
}

.roleCardContainer {
  position: relative;
  display: flex;
  width: 90px;
}

.roleCard {
  flex: 1;
  aspect-ratio: 1 / 1;
  height: auto;

  min-width: 60px;
  max-width: 90px;

  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  border-radius: 8px;
  background-position: center top;
  overflow: hidden;
  border: 1px solid #29313E;


  .roleName {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    color: #fff;
    text-align: center;
    height: 1.5rem;
    line-height: 1.5rem;
    font-size: 0.8rem;
  }

  &:hover {
    .roleDeleteBtn {
      display: block;
    }
  }
}

.roleCardSelected {
  border: 1px solid @green-slt-color;
}

.roleCheckbox {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}

.emptyRoleCard,
.addRoleCard {
  width: 90px;
  height: 90px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8rem;
  border: 1px solid @bg-color;

  &:hover {
    border-color: #69FFA3;
    background: rgba(105, 255, 163, 0.05);
  }
}

.roleEditBtn {
  position: absolute;
  bottom: 5px;
  right: 5px;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.roleAvatar {
  border: 1px solid #2A303B;

  img {
    object-position: top
  }
}

.roleName {
  max-width: 140px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.roleDesc {
  margin-top: 4px;
  height: 32px;
  overflow: hidden;
  width: 150px;
}

.mvImageChipGroup {
  flex-wrap: wrap;
  gap: 8px !important;
  margin-bottom: 10px;
}

.chipLabel {
  &[class] {
    font-size: 12px;
    padding: 2px 8px;
  }
}

// 上传图片相关样式
.uploadBox {
  width: 100%;
  height: 120px;
  border: 1px dashed #6AFFA3;
  border-radius: 8px;
  cursor: pointer;
  background-color: rgba(106, 255, 163, 0.05);
  transition: all 0.3s ease;

  &:hover {
    background-color: rgba(106, 255, 163, 0.1);
  }
}

.imagePreviewContainer {
  width: 100%;
  border: 1px solid #333743;
  border-radius: 8px;
  padding: 12px;
  background-color: #1d2026;
}

.imagePreview {
  max-height: 120px;
  object-fit: contain;
  border-radius: 8px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

// 添加图片预览盒子样式
.imagePreviewBox {
  position: relative;
  border: 1px solid #333743;
  border-radius: 8px;
  overflow: hidden;
  background-color: #232730;
  height: 80px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #6AFFA3;

    .imageRemoveBtn {
      opacity: 1;
    }
  }
}

// 图片删除按钮
.imageRemoveBtn {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 3;
}



// 选中和未选中的Chip样式

.mvImageChipGroup .chipLabel {
  position: relative;

  font-size: 12px;
  font-weight: 500;
  color: #fff;
  background: none;
  padding: 0 10px;


  border: 1px solid #3B3F46;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: #3C485B;
  }

  &[data-checked] {

    border-color: rgba(105, 255, 163, 1);

    &:hover {
      background-color: #1A4332;
    }

  }

  :global(.mantine-Chip-iconWrapper) {
    position: absolute;
    top: -4px;
    right: -4px;
    padding: 2px;
    width: 10px;
    height: 10px;
    background-color: #6AFFA3;
    border-radius: 50%;

    svg {
      color: #000
    }
  }

}

.roleDeleteBtn {
  display: none;
  position: absolute;
  top: 5px;
  right: 5px;
  color: white;
  background-color: rgba(0, 0, 0, 0.5);

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.roleImg {
  display: flex;
  align-items: center;
  justify-content: center;
}

.playProgressBar {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #fff;
  box-shadow: 1px 1px 3px 0 rgba(0, 0, 0, 0.5);
  z-index: 10;
  pointer-events: none;

  &::before {
    content: '';
    position: absolute;
    top: 0px;
    left: -5px;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #fff;
  }
}