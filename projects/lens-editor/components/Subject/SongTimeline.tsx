import React, { useEffect, useRef, useState } from 'react';
import styles from './index.module.less';
import { LyricSelection } from 'projects/lens-editor/store/editorStore';

interface Lyric {
  iStartMs: number;
  iEndMs: number;
  strContent: string;
  iDurationMs?: number;
}

interface TimelineProps {
  duration: number;
  selectedRange: {
    start: number;
    end: number;
  };
  findLyricIndexByTime: (timeInSeconds: number, isEndTime?: boolean) => number;
  setLyricSelection: (lyricSelection: LyricSelection) => void;
  lyricSelection: LyricSelection;
  lyrics?: Lyric[];
  alignToLyrics?: boolean; // 是否对齐到歌词
  handleLyricScroll: (startIndex: number) => void;
  handleLyricScrollBtm: (endIndex: number) => void;
  onTimelineChange?: () => void; // 时间轴变化回调
  playProgress?: number; // 播放进度 (0-1)
  isPlaying?: boolean; // 是否正在播放
}

export default function SongTimeline({
  duration,
  selectedRange,
  findLyricIndexByTime,
  setLyricSelection,
  lyrics,
  alignToLyrics = false,
  lyricSelection,
  handleLyricScroll,
  handleLyricScrollBtm,
  onTimelineChange,
  playProgress = 0,
  isPlaying = false,
}: TimelineProps) {
  const [isDragging, setIsDragging] = useState<'start' | 'end' | 'range' | null>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const dragStartRef = useRef<{
    rangeWidth: number;
    startPosition: number;
  }>({ rangeWidth: 0, startPosition: 0 });
  // 保存临时的选中范围，用于mouseup时与mousemove分离
  const tempRangeRef = useRef<{ start: number; end: number }>(selectedRange);

  const formatTime = (seconds: number) => {
    // 四舍五入到最接近的秒
    const roundedSeconds = Math.round(seconds);
    const mins = Math.floor(roundedSeconds / 60);
    const secs = roundedSeconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleMouseDown = (handle: 'start' | 'end') => (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡
    setIsDragging(handle);
  };

  const handleRangeMouseDown = (e: React.MouseEvent) => {
    e.preventDefault(); // 阻止默认行为
    if (!timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const position = ((e.clientX - rect.left) / rect.width) * duration;

    setIsDragging('range');
    dragStartRef.current = {
      rangeWidth: selectedRange.end - selectedRange.start,
      startPosition: position - selectedRange.start,
    };
  };

  // 根据时间找到最近的歌词时间点
  const findNearestLyricTime = (timeInSeconds: number, type: 'start' | 'end'): number => {
    console.log('[findNearestLyricTime]:', timeInSeconds, type, lyrics);
    if (!lyrics || lyrics.length === 0) return timeInSeconds;

    const timeMs = timeInSeconds * 1000;

    // 如果早于第一句歌词或晚于最后一句歌词，直接返回用户选择的时间
    if (timeMs < lyrics[0].iStartMs) return timeInSeconds;
    if (timeMs > lyrics[lyrics.length - 1].iEndMs) return timeInSeconds;

    // 检查时间点是否落在某个歌词的时间范围内
    let isWithinLyric = false;
    let containingLyricIndex = -1;

    for (let i = 0; i < lyrics.length; i++) {
      if (timeMs >= lyrics[i].iStartMs && timeMs <= lyrics[i].iEndMs) {
        isWithinLyric = true;
        containingLyricIndex = i;
        break;
      }
    }
    console.log('[findNearestLyricTime]:', timeInSeconds, type, isWithinLyric, containingLyricIndex);

    // 如果时间点落在某个歌词内部
    if (isWithinLyric && containingLyricIndex !== -1) {
      const lyric = lyrics[containingLyricIndex];
      const isLastLyric = containingLyricIndex === lyrics.length - 1;

      // 特殊处理最后一句歌词：如果其结束时间超过歌曲总时长，则使用歌曲总时长 案例：新贵妃醉酒-李玉刚
      if (isLastLyric && type === 'end') {
        const lyricEndTime = lyric.iEndMs / 1000;
        if (lyricEndTime > duration) {
          console.log('[findNearestLyricTime] 最后一句歌词超出歌曲时长，使用歌曲总时长:', duration);
          return duration;
        }
      }

      // 正常情况下对齐到歌词边界
      if (type === 'start') {
        return lyric.iStartMs / 1000;
      } else {
        return lyric.iEndMs / 1000;
      }
    }

    // 如果不在任何歌词内部，保持原始时间点
    return timeInSeconds;
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const position = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const newTime = position * duration;

    let newStart = selectedRange.start;
    let newEnd = selectedRange.end;

    if (isDragging === 'start') {
      // 允许自由拖动开始位置，不对齐到歌词
      newStart = Math.min(newTime, selectedRange.end - 1);
      // 实时显示拖动效果，但不立即对齐到歌词
      onRangeChange(newStart, selectedRange.end);
    } else if (isDragging === 'end') {
      // 允许自由拖动结束位置，不对齐到歌词
      newEnd = Math.min(newTime, duration);
      // 确保新的结束时间不小于开始时间+1
      newEnd = Math.max(newEnd, selectedRange.start + 1);
      // 更新范围
      onRangeChange(selectedRange.start, newEnd);
    } else if (isDragging === 'range') {
      const rangeWidth = dragStartRef.current.rangeWidth;
      const dragPosition = dragStartRef.current.startPosition;
      newStart = newTime - dragPosition;

      // 添加边界检查
      newStart = Math.max(0, Math.min(duration - rangeWidth, newStart));
      newEnd = newStart + rangeWidth;

      // 实时显示拖动效果，但不立即对齐到歌词
      onRangeChange(newStart, newEnd);
    }

    console.log('newEnd', newEnd);
    // 更新临时引用，以便在mouseup时使用
    tempRangeRef.current = { start: newStart, end: newEnd };
  };

  const handleMouseUp = () => {
    if (isDragging) {
      console.log('[lyrics]:', lyrics, alignToLyrics);

      if (lyrics && lyrics.length > 0 && alignToLyrics) {
        // 在鼠标释放时，只有当时间点落在歌词内部时才对齐到歌词边界
        const alignedStart = findNearestLyricTime(tempRangeRef.current.start, 'start');
        const alignedEnd = findNearestLyricTime(tempRangeRef.current.end, 'end');

        // 确保结束时间大于开始时间至少1秒
        const finalEnd = Math.max(alignedEnd, alignedStart + 1);

        console.log('Aligned to lyrics:', alignedStart, finalEnd);
        onRangeChange(alignedStart, finalEnd);

        // 更新显示的选中范围
        tempRangeRef.current = { start: alignedStart, end: finalEnd };
      } else {
        // 不对齐到歌词，直接使用拖动的时间点
        // 但仍然确保时间点在有效范围内
        const clampedStart = Math.max(0, tempRangeRef.current.start);
        const clampedEnd = Math.min(duration, tempRangeRef.current.end);

        // 确保结束时间大于开始时间至少1秒
        const finalEnd = Math.max(clampedEnd, clampedStart + 1);
        console.log('不对齐到歌词, finalEnd:', clampedStart, finalEnd);
        onRangeChange(clampedStart, finalEnd);
      }
    }

    setIsDragging(null);
  };

  // 监听鼠标移动和抬起事件
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);

      return () => {
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, selectedRange, duration]); // 添加依赖项

  // 时间轴变化的处理函数
  const onRangeChange = (start: number, end: number) => {
    // 调用外部传入的时间轴变化回调
    onTimelineChange?.();

    console.log('[onRangeChange] 开始处理时间变化:', {
      start,
      end,
      duration,
      lyricsLength: lyrics?.length || 0,
      lastLyricEndTime: lyrics?.length ? lyrics[lyrics.length - 1].iEndMs / 1000 : null,
    });

    const startIndex = findLyricIndexByTime(start);
    const endIndex = findLyricIndexByTime(end, true);

    console.log('[onRangeChange] findLyricIndexByTime 结果:', {
      startIndex,
      endIndex,
      startLyric: lyrics?.[startIndex],
      endLyric: lyrics?.[endIndex],
    });

    setLyricSelection({
      startIndex,
      endIndex,
      startTime: start,
      endTime: end,
    });

    // 自动滚动到选中的歌词
    setTimeout(() => {
      // 判断是拖动的左边还是右边，调用相应的滚动函数
      if (isDragging === 'start' || isDragging === 'range') {
        // 如果拖动的是左边句柄或整个范围，滚动到开始位置
        handleLyricScroll(startIndex);
      } else if (isDragging === 'end') {
        // 如果拖动的是右边句柄，滚动到结束位置
        // 将结束位置的歌词滚动到容器底部附近，而不是顶部
        handleLyricScrollBtm(endIndex);
      }
    }, 100);
  };

  return (
    <div className={styles.timelineContainer}>
      {/* 进度条 */}
      <div className={styles.timeline} ref={timelineRef}>
        <div
          style={{
            left: `${(selectedRange.start / duration) * 100}%`,
            width: `${((selectedRange.end - selectedRange.start) / duration) * 100}%`,
            position: 'absolute',
            top: 0,
          }}>
          {/* 时间显示 - 修改这里显示实际结束时间 */}
          <div className={styles.timeLabels}>
            <span style={{ marginRight: '5px' }}>{formatTime(lyricSelection.startTime)}</span>
            <span>{formatTime(lyricSelection.endTime)}</span>
          </div>
          <div className={styles.selectedRange} onMouseDown={handleRangeMouseDown}>
            {/* 播放进度指示器 */}
            {isPlaying && (
              <div
                className={styles.playProgressBar}
                style={{
                  left: `${playProgress * 100}%`,
                }}
              />
            )}
            <div className={styles.handle} style={{ left: 0 }} onMouseDown={handleMouseDown('start')} />
            <div className={styles.handle} style={{ right: 0 }} onMouseDown={handleMouseDown('end')} />
          </div>
        </div>
      </div>

      {/* 总时长显示 */}
      <div className={styles.totalDuration}>
        <span>00:00</span>
        <span>{formatTime(duration)}</span>
      </div>
    </div>
  );
}
