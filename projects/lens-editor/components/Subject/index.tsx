// 主题确定
import React, { useEffect, useRef, useState } from 'react';
import { Box, Paper, Text, Group, Stack, TextInput, Button, Divider, LoadingOverlay, ActionIcon } from '@mantine/core';
import {
  $AudioUrlRsp,
  $BasicInfoRsp,
  $DetermineTopicRsp,
  $SearchSongListRsp,
  $SongInfoRsp,
  $SongList,
  emBasicMask,
  emEditType,
  emScriptGetMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';

import { useEditorStore } from '../../store/editorStore';

import req from 'common/request';
import {
  $MVQuality,
  $SongInfo,
  emFlow,
  emStatus,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { notifications } from '@mantine/notifications';
import BasicConfig from './BasicConfig';
import Lyc from './Lyc';
import { MVIcon, SearchIcon } from '../svg';
import { formatMsToTime, formatSecondsToTime } from 'common/utils';
import SongTimeline from './SongTimeline';
import { IconPlayerPauseFilled, IconPlayerPlayFilled } from '@tabler/icons-react';

export default function Subject() {
  const navigate = useNavigate();
  const {
    searchList,
    isFailed,
    setSearchList,
    selectedSong,
    setSelectedSong,
    lyricSelection,
    setLyricSelection,
    showBasicConfig,
    setShowBasicConfig,
    basicConfig,
    setScriptId,
    scriptInfo,
    modifyTopic,
    setScriptInfo,
    startPolling,
    scriptId,
    fetchScriptInfoOnce,
    searchSongList,
    getSongInfo,
    getAudioUrl,
    determineTopic,
    getBasicInfo,
    setBasicConfig,
  } = useEditorStore();

  const [currentSongInfo, setCurrentSongInfo] = useState<$SongInfo>(); // 当前选中的歌曲信息
  const [currentAudioUrl, setCurrentAudioUrl] = useState(''); // 当前选中的歌曲音频文件
  const [selectedMvQuality, setSelectedMvQuality] = useState<$MVQuality>(); // 选中的mv品质

  const [searchKeyword, setSearchKeyword] = useState('');

  const [isMounted, setIsMounted] = useState(false);
  const [songPlaying, setSongPlaying] = useState(false); //歌曲试听播放状态
  const [playProgress, setPlayProgress] = useState(0); // 播放进度 (0-1)

  const lyricsRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null); // 音频播放器引用
  const progressUpdateTimer = useRef<number | null>(null); // 进度更新定时器

  const handleSearch = (keyword: string) => {
    if (!keyword.trim()) return;
    searchSongList(keyword)
      .then(data => {
        if (isMounted) {
          setSearchList(data.vecList);
        }
      })
      .catch(error => {
        notifications.show({
          title: '获取歌曲列表失败',
          message: error.message || '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      });
  };

  const handleGetSongInfo = (song: $SongList) => {
    if (!song) return;
    setCurrentSongInfo(undefined);
    getAudioUrl(song.strSongId)
      .then((data: $AudioUrlRsp) => {
        console.log('[getAudioUrl]:', data);
        setCurrentAudioUrl(data.strAudioUrl);
      })
      .catch(err => {
        notifications.show({
          title: '获取音频文件失败',
          message: err.message || '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      });

    getSongInfo(song)
      .then(data => {
        if (isMounted) {
          setCurrentSongInfo(data.stSong);
        }
      })
      .catch(err => {
        notifications.show({
          title: '获取歌曲信息失败',
          message: err.message || '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      });
  };

  // 确定歌词主题
  const handleDetermineTopic = (editType?: emEditType) => {
    console.log('[emBasicMask]:', emBasicMask, lyricSelection, currentSongInfo, scriptInfo);
    const songinfo = currentSongInfo || scriptInfo?.stTopic?.stSongInfo;
    if (!songinfo) {
      notifications.show({
        title: '确定主题失败',
        message: '请选择歌曲',
        color: 'red',
        autoClose: 5000,
      });
      return;
    }

    // 只有在编辑已有作品且确实有分镜数据时才阻止重新生成
    if (scriptId && scriptInfo?.strScriptId === scriptId && scriptInfo?.vecNewScene?.length) {
      notifications.show({
        title: '无法重新生成创意',
        message: '已经生成分镜，无法重新生成创意',
        color: 'red',
        autoClose: 5000,
      });
      return;
    }

    // 直接使用用户选择的时间范围
    let iStartMs = Math.round(lyricSelection.startTime * 1000);
    const iEndMs = Math.round(lyricSelection.endTime * 1000);

    // 过滤出真正在时间范围内的歌词
    const startTimeMs = Math.round(lyricSelection.startTime * 1000);
    const endTimeMs = Math.round(lyricSelection.endTime * 1000);

    let actualStartIndex = lyricSelection.startIndex;
    let actualEndIndex = lyricSelection.endIndex;

    // 查找第一个开始时间大于等于所选时间的歌词的索引
    if (songinfo.veclyric.length) {
      for (let i = 0; i < songinfo.veclyric.length; i++) {
        if (songinfo.veclyric[i].iStartMs >= startTimeMs) {
          actualStartIndex = i;
          break;
        }
      }

      // 查找最后一个结束时间小于等于所选时间的歌词的索引
      // 如果用户选择了超过最后一个歌词的时间，我们仍然包含所有歌词
      actualEndIndex = songinfo.veclyric.length - 1;
      for (let i = songinfo.veclyric.length - 1; i >= 0; i--) {
        if (songinfo.veclyric[i].iStartMs <= endTimeMs) {
          actualEndIndex = i;
          break;
        }
      }

      // 使用歌词的实际开始时间，但保留用户选择的结束时间
      iStartMs = songinfo.veclyric[actualStartIndex].iStartMs;
      // 不再强制使用歌词的结束时间，而是使用用户选择的时间
      // iEndMs = songinfo.veclyric[actualEndIndex].iEndMs;
    }

    // 创建深拷贝并过滤歌词
    const filteredSongInfo = {
      ...songinfo,
      veclyric: songinfo.veclyric.length ? songinfo.veclyric.slice(actualStartIndex, actualEndIndex + 1) : [],
      iAudioLength: songinfo.iAudioLength,
      // 使用正确的歌词时间作为起始时间，用户选择的时间作为结束时间
      iStartMs,
      iEndMs,
    };
    console.log('[editType]:', editType);

    const params = {
      strScriptId: scriptId,
      vectorEditField: scriptId ? (editType ? [editType] : [emEditType.EM_EDIT_TOPIC_TYPE_SONGINFO]) : [],
      stTopic: {
        stSongInfo: filteredSongInfo,
      },
    };

    determineTopic(params)
      .then(data => {
        const newScriptId = data.strScriptId;
        setScriptId(newScriptId);

        // 获取新的画面风格配置
        getBasicInfo(newScriptId)
          .then((basicData: $BasicInfoRsp) => {
            console.log('[确定主题-更新基础配置]:', basicData);
            setBasicConfig(basicData);
          })
          .catch(error => {
            console.error('获取基础配置失败', error);
            notifications.show({
              title: '获取基础配置失败',
              message: error.message || '请稍后重试',
              color: 'red',
              autoClose: 5000,
            });
          });

        navigate(`/editor/${newScriptId}`, { replace: true });

        startPolling(
          emScriptGetMask.EM_SCRIPT_GET_TYPE_TOPIC,
          () => {
            console.log('[startPolling callback]');
          },
          undefined,
          'topic'
        );
      })
      .catch(error => {
        notifications.show({
          title: '确定主题失败',
          message: error.message || '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      });

    // 设置显示基础配置
    setShowBasicConfig(true);
  };

  // 根据时间找到歌词索引
  const findLyricIndexByTime = (timeInSeconds: number, isEndTime = false) => {
    console.log('[findLyricIndexByTime] 输入参数:', {
      timeInSeconds,
      isEndTime,
      currentSongInfoExists: !!currentSongInfo,
      lyricsCount: currentSongInfo?.veclyric?.length || 0,
    });

    if (!currentSongInfo || !currentSongInfo.veclyric?.length) {
      console.log('[findLyricIndexByTime] 没有歌词数据，返回0');
      return 0;
    }

    const timeMs = timeInSeconds * 1000;
    const lastIndex = currentSongInfo.veclyric.length - 1;
    const firstLyricTime = currentSongInfo.veclyric[0].iStartMs;
    const lastLyricTime = currentSongInfo.veclyric[lastIndex].iEndMs;

    console.log('[findLyricIndexByTime] 时间范围检查:', {
      timeMs,
      firstLyricTime,
      lastLyricTime,
      lastIndex,
      isTimeBeforeFirst: timeMs < firstLyricTime,
      isTimeAfterLast: timeMs > lastLyricTime,
    });

    // 允许选择整首歌的时长，但需要返回有效的歌词索引
    // 如果时间早于第一句歌词，仍然返回第一句歌词的索引
    if (timeMs < firstLyricTime) {
      console.log('[findLyricIndexByTime] 时间早于第一句歌词，返回0');
      return 0;
    }

    // 如果时间晚于最后一句歌词，仍然返回最后一句歌词的索引
    if (timeMs > lastLyricTime) {
      console.log('[findLyricIndexByTime] 时间晚于最后一句歌词，返回lastIndex:', lastIndex);
      return lastIndex;
    }

    // 处理结束时间和开始时间的方式不同
    if (isEndTime) {
      console.log('[findLyricIndexByTime] 处理结束时间逻辑');
      // 对于结束时间，找到最后一个结束时间小于等于指定时间的歌词索引
      for (let i = lastIndex; i >= 0; i--) {
        if (currentSongInfo.veclyric[i].iStartMs <= timeMs) {
          // 如果这句歌词的开始时间小于等于指定时间
          // 但我们需要确认该歌词不是从指定时间后开始的完整歌词
          if (i === lastIndex || currentSongInfo.veclyric[i].iEndMs <= timeMs) {
            console.log('[findLyricIndexByTime] 结束时间逻辑返回索引:', i);
            return i;
          }
          // 如果这句歌词的结束时间超过了指定时间，检查是否有大部分时间在指定时间之前
          if (currentSongInfo.veclyric[i].iEndMs > timeMs) {
            const lyricDuration = currentSongInfo.veclyric[i].iEndMs - currentSongInfo.veclyric[i].iStartMs;
            const beforeEndDuration = timeMs - currentSongInfo.veclyric[i].iStartMs;
            // 如果至少一半的歌词在指定时间之前，则包含这句歌词
            if (beforeEndDuration >= lyricDuration / 2) {
              console.log('[findLyricIndexByTime] 歌词过半，返回索引:', i);
              return i;
            } else if (i > 0) {
              // 否则，使用前一句歌词的索引
              console.log('[findLyricIndexByTime] 歌词不过半，返回前一句索引:', i - 1);
              return i - 1;
            }
          }
        }
      }
      console.log('[findLyricIndexByTime] 结束时间逻辑默认返回0');
      return 0; // 默认返回第一句歌词的索引
    } else {
      console.log('[findLyricIndexByTime] 处理开始时间逻辑');
      // 对于开始时间，找到第一个开始时间大于等于指定时间的歌词索引
      for (let i = 0; i < currentSongInfo.veclyric.length; i++) {
        if (currentSongInfo.veclyric[i].iStartMs >= timeMs) {
          console.log('[findLyricIndexByTime] 开始时间逻辑返回索引:', i);
          return i;
        }
      }
      // 如果没有找到，返回最后一个歌词的索引
      console.log('[findLyricIndexByTime] 开始时间逻辑默认返回lastIndex:', lastIndex);
      return lastIndex;
    }
  };

  // 往上面滚动
  const handleLyricScroll = (startIndex: number) => {
    if (!lyricsRef.current) return;

    const items = lyricsRef.current.querySelectorAll(`.${styles.lyricItem}`);
    // console.log('handleLyricScroll', startIndex, endIndex, items);

    // 往前面多露出一行
    const index = Math.max(startIndex - 1, 0);
    if (items[index]) {
      items[index].scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // 往下面滚动
  const handleLyricScrollBtm = (endIndex: number) => {
    if (!lyricsRef.current) return;

    const items = lyricsRef.current.querySelectorAll(`.${styles.lyricItem}`);
    // console.log('handleLyricScroll', startIndex, endIndex, items);

    // 往下面多露出一行
    const index = Math.min(endIndex + 1, items.length - 1);
    if (items[index]) {
      // 修改为 block: 'end'，将元素底部与容器底部对齐
      items[index].scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  };

  // 时间轴选区初始化函数
  const handleTimeRangeChange = (value: string) => {
    console.log('handleTimeRangeChange setLyricSelection', value, currentSongInfo);

    // 停止当前播放的音频
    if (songPlaying) {
      handlePauseSong();
    }

    // 安全地解析为数字，如果解析失败则使用默认值30
    const duration = Number.isNaN(parseInt(value)) ? 30 : parseInt(value);

    if (!currentSongInfo?.veclyric?.length) return;

    // 总时长（秒）
    const totalDurationSec = currentSongInfo.iAudioLength / 1000;

    // 声明所有可能用到的变量
    let startIndex = 0;
    let endIndex = 0;
    let startTime = 0;
    let endTime = 0;
    let newApproxStartTime = 0;
    let newApproxEndTime = 0;

    // 抽取共用的设置选择区域和滚动的逻辑
    const setSelectionAndScroll = (startIdx: number, endIdx: number, startT: number, endT: number) => {
      setLyricSelection({
        startIndex: startIdx,
        endIndex: endIdx,
        startTime: startT,
        endTime: endT,
      });

      // 添加自动滚动到选中的第一句歌词
      setTimeout(() => {
        handleLyricScroll(startIdx);
      }, 100);
    };

    // 判断是否是默认选择且之前已有选择
    const isNonDefaultSelection =
      !(
        lyricSelection.startTime === 0 &&
        lyricSelection.endTime === 30 &&
        lyricSelection.startIndex === 0 &&
        lyricSelection.endIndex === 5
      ) && lyricSelection.startIndex !== lyricSelection.endIndex;

    if (isNonDefaultSelection) {
      const startLyric = currentSongInfo.veclyric[lyricSelection.startIndex];
      const endLyric = currentSongInfo.veclyric[lyricSelection.endIndex];

      if (startLyric && endLyric) {
        // 使用歌词的实际起止时间
        const actualStartTime = startLyric.iStartMs / 1000;
        const actualEndTime = endLyric.iEndMs / 1000;

        // 计算当前选择区间的中心点
        const selectionCenter = (actualStartTime + actualEndTime) / 2;
        const halfDur = duration / 2;

        // 查找最接近新起始和结束时间的歌词
        newApproxStartTime = Math.max(0, selectionCenter - halfDur);
        newApproxEndTime = Math.min(totalDurationSec, selectionCenter + halfDur);

        startIndex = findLyricIndexByTime(newApproxStartTime);
        endIndex = findLyricIndexByTime(newApproxEndTime, true);

        // 获取实际的歌词时间边界
        startTime = currentSongInfo.veclyric[startIndex].iStartMs / 1000;
        endTime = currentSongInfo.veclyric[endIndex].iEndMs / 1000;

        console.log(
          'handleTimeRangeChange 使用歌词边界调整时间',
          'startIndex:',
          startIndex,
          'endIndex:',
          endIndex,
          'startTime:',
          startTime,
          'endTime:',
          endTime
        );

        setSelectionAndScroll(startIndex, endIndex, startTime, endTime);
        return;
      }
    }

    // 默认处理逻辑（初始加载或无有效选择时）
    const defaultCenter = Math.min(totalDurationSec / 2, duration / 2);

    // 计算新的起止时间
    newApproxStartTime = Math.max(0, defaultCenter - duration / 2);
    newApproxEndTime = Math.min(totalDurationSec, newApproxStartTime + duration);

    // 找到对应的歌词索引
    startIndex = findLyricIndexByTime(newApproxStartTime);
    endIndex = findLyricIndexByTime(newApproxEndTime, true);

    // 获取实际的歌词时间边界
    startTime = currentSongInfo.veclyric[startIndex].iStartMs / 1000;
    endTime = currentSongInfo.veclyric[endIndex].iEndMs / 1000;

    console.log(
      'handleTimeRangeChange 初始化时间范围',
      'startIndex:',
      startIndex,
      'endIndex:',
      endIndex,
      'startTime:',
      startTime,
      'endTime:',
      endTime
    );

    setSelectionAndScroll(startIndex, endIndex, startTime, endTime);
  };

  // 搜索歌曲防抖效果
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchKeyword.trim()) {
        handleSearch(searchKeyword);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchKeyword]);

  // 选择歌曲后拉取歌曲详情
  useEffect(() => {
    if (selectedSong && selectedSong.strSongId !== currentSongInfo?.strSongId) {
      handleGetSongInfo(selectedSong);
    }
  }, [selectedSong]);

  useEffect(() => {
    console.log('[scriptId]2:', scriptId, scriptInfo);

    if (scriptId) {
      setShowBasicConfig(true);
    } else {
      setShowBasicConfig(false);
      setSelectedSong(null);
      setSearchKeyword('');
      setSearchList([]);
    }
  }, [scriptId]);

  // 当画面风格存在时，获取最新的基础配置信息
  useEffect(() => {
    const picModelId = scriptInfo?.stTopic?.stPicModel?.strId;
    console.log('[获取基础配置] picModelId:', picModelId);

    if (picModelId && scriptId) {
      getBasicInfo(scriptId)
        .then((data: $BasicInfoRsp) => {
          console.log('[更新基础配置]:', data);
          setBasicConfig(data);
        })
        .catch(error => {
          console.error('获取基础配置失败', error);
          notifications.show({
            title: '获取基础配置失败',
            message: error.message || '请稍后重试',
            color: 'red',
            autoClose: 5000,
          });
        });
    }
  }, [scriptInfo?.stTopic?.stPicModel?.strId, scriptId, getBasicInfo, setBasicConfig]);

  // 当点取消按钮，重新选择歌曲时触发
  useEffect(() => {
    console.log('[更换歌曲]:', currentSongInfo);
    if (currentSongInfo) {
      // 初始化歌词选区相关
      // 默认选中前5句歌词
      setLyricSelection({
        startIndex: 0,
        endIndex: 5,
        startTime: 0,
        endTime: Math.min(lyricSelection.endTime, currentSongInfo?.iAudioLength / 1000),
      });

      // 重置时间轴
      handleTimeRangeChange(Math.round(lyricSelection.endTime - lyricSelection.startTime).toString());

      // 自动滚动到选中区域
      setTimeout(() => {
        handleLyricScroll(lyricSelection.startIndex);
      }, 100);
    }
  }, [currentSongInfo]);

  // 更新播放进度
  const updatePlayProgress = () => {
    if (!audioRef.current || !currentSongInfo) return;

    const currentTime = audioRef.current.currentTime;
    const startTime = lyricSelection.startTime;
    const endTime = lyricSelection.endTime;
    const duration = endTime - startTime;

    if (duration > 0) {
      const progress = Math.max(0, Math.min(1, (currentTime - startTime) / duration));
      setPlayProgress(progress);
    }
  };

  // 开始更新播放进度
  const startProgressUpdate = () => {
    if (progressUpdateTimer.current) {
      clearInterval(progressUpdateTimer.current);
    }

    progressUpdateTimer.current = window.setInterval(updatePlayProgress, 100); // 每100ms更新一次
  };

  // 停止更新播放进度
  const stopProgressUpdate = () => {
    if (progressUpdateTimer.current) {
      clearInterval(progressUpdateTimer.current);
      progressUpdateTimer.current = null;
    }
    setPlayProgress(0);
  };

  // 播放选中区域的音频
  const handlePlaySong = () => {
    if (!currentAudioUrl) {
      notifications.show({
        title: '播放失败',
        message: '没有可播放的音频文件',
        color: 'red',
        autoClose: 3000,
      });
      return;
    }

    if (songPlaying) {
      // 如果正在播放，则暂停
      handlePauseSong();
      return;
    }

    try {
      // 停止并清理之前的音频
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.removeEventListener('ended', handleAudioEnded);
        audioRef.current.removeEventListener('error', handleAudioError);
      }

      // 创建新的音频实例
      const audio = new Audio();
      audioRef.current = audio;

      // 设置音频源
      const audioUrl = currentAudioUrl.startsWith('http')
        ? currentAudioUrl.replace('http://', 'https://')
        : currentAudioUrl;
      console.log('[audioUrl]:', audioUrl);
      audio.src = audioUrl;
      audio.preload = 'metadata';

      // 添加事件监听器
      audio.addEventListener('loadedmetadata', () => {
        // 设置播放起始时间（转换为秒）
        audio.currentTime = lyricSelection.startTime;

        // 开始播放
        audio
          .play()
          .then(() => {
            setSongPlaying(true);
            startProgressUpdate(); // 开始更新进度

            // 设置定时器在指定结束时间停止播放
            const playDuration = (lyricSelection.endTime - lyricSelection.startTime) * 1000; // 转换为毫秒
            setTimeout(() => {
              if (audioRef.current === audio && !audio.paused) {
                handlePauseSong();
              }
            }, playDuration);
          })
          .catch(error => {
            console.error('播放失败:', error);
            setSongPlaying(false);
            notifications.show({
              title: '播放失败',
              message: '音频播放出错，请检查网络连接',
              color: 'red',
              autoClose: 3000,
            });
          });
      });

      audio.addEventListener('ended', handleAudioEnded);
      audio.addEventListener('error', handleAudioError);

      // 开始加载音频
      audio.load();
    } catch (error) {
      console.error('创建音频播放器失败:', error);
      setSongPlaying(false);
      notifications.show({
        title: '播放失败',
        message: '无法创建音频播放器',
        color: 'red',
        autoClose: 3000,
      });
    }
  };

  // 暂停播放
  const handlePauseSong = () => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
    setSongPlaying(false);
    stopProgressUpdate(); // 停止进度更新
  };

  // 音频播放结束回调
  const handleAudioEnded = () => {
    setSongPlaying(false);
    stopProgressUpdate(); // 停止进度更新
  };

  // 音频播放错误回调
  const handleAudioError = (error: Event) => {
    console.error('音频播放错误:', error);
    setSongPlaying(false);
    notifications.show({
      title: '播放失败',
      message: '音频文件加载失败',
      color: 'red',
      autoClose: 3000,
    });
  };

  useEffect(() => {
    console.log('[subject] 挂载');
    setIsMounted(true);
    // 只有当scriptId存在时才调用fetchScriptInfoOnce，避免在新建作品时获取错误数据
    if (scriptId) {
      fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_TOPIC);
    }
    return () => {
      console.log('[subject] 卸载');
      setIsMounted(false);
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.removeEventListener('ended', handleAudioEnded);
        audioRef.current.removeEventListener('error', handleAudioError);
        audioRef.current = null;
      }
    };
  }, [scriptId]);

  // console.log(
  //   '[selectedMvTemplate]:',
  //   scriptInfo,
  //   basicConfig.vecMvTpl,
  //   selectedMvQuality?.strId,
  //   scriptInfo?.stTopic?.stSize?.sizeName,
  //   lyricSelection
  // );
  // console.log('[lyricSelection]:', lyricSelection);

  return (
    <>
      {!selectedSong && !showBasicConfig ? (
        <Stack gap="xl" className={styles.wrap} style={{ height: searchList.length ? 506 : 379 }}>
          {/* 页面初始状态  搜索歌曲 */}
          <div className={styles.wrapBg}></div>
          <div className={styles.container}>
            {
              <Stack>
                <Group className={styles.subTitle}>
                  <MVIcon />
                  <Text>MV创作</Text>
                </Group>
                <p className={styles.title}>输入歌曲名，即可一键成片</p>
                <div className={styles.songSearchWrap}>
                  <TextInput
                    placeholder="请输入你想要制作mv的歌曲名称，将为你自动检索音频和歌词"
                    value={searchKeyword}
                    classNames={{ input: styles.songSearchInput }}
                    onChange={e => setSearchKeyword(e.target.value)}
                    autoFocus={true}
                    rightSection={
                      <span style={{ marginRight: 24, height: 24 }} onClick={() => handleSearch(searchKeyword)}>
                        <SearchIcon />
                      </span>
                    }
                  />
                  {searchList?.map(song => (
                    <Text key={song.strSongId} className={styles.songName} onClick={() => setSelectedSong(song)}>
                      {song.strSongName} - {song.strSinger}
                    </Text>
                  ))}
                </div>
              </Stack>
            }
          </div>
        </Stack>
      ) : (
        <Paper p="md" className={styles.contentWrap}>
          <Paper>
            {/* 确定主题前 */}
            {selectedSong && !showBasicConfig ? (
              <Stack style={{ position: 'relative' }}>
                <LoadingOverlay
                  visible={!currentSongInfo}
                  overlayProps={{ radius: 'sm', blur: 2 }}
                  loaderProps={{ size: 'md' }}
                />
                <Group
                  justify="space-between"
                  align="center"
                  p="xs"
                  className={styles.songbox}
                  onClick={() => setSelectedSong(null)}>
                  <Text size="sm">
                    {selectedSong.strSongName} - {selectedSong.strSinger}
                  </Text>
                </Group>

                <Group justify="space-between" align="center" style={{ margin: '15px 0' }}>
                  <Text>
                    选择音乐时长
                    <span style={{ color: '#6AFFA3', marginLeft: 5 }}>
                      {currentSongInfo?.veclyric[lyricSelection.endIndex]?.iEndMs
                        ? formatSecondsToTime((lyricSelection.endTime || 0) - (lyricSelection.startTime || 0))
                        : ''}
                    </span>
                  </Text>
                </Group>

                {/* 音频时间轴 */}
                <div className={styles.timelineWrapper}>
                  <SongTimeline
                    duration={(currentSongInfo?.iAudioLength || 0) / 1000}
                    selectedRange={{
                      start: lyricSelection.startTime,
                      end: lyricSelection.endTime,
                    }}
                    findLyricIndexByTime={findLyricIndexByTime}
                    lyrics={currentSongInfo?.veclyric}
                    setLyricSelection={setLyricSelection}
                    alignToLyrics={true}
                    lyricSelection={lyricSelection}
                    handleLyricScroll={handleLyricScroll}
                    handleLyricScrollBtm={handleLyricScrollBtm}
                    playProgress={playProgress}
                    isPlaying={songPlaying}
                    onTimelineChange={() => {
                      // 当时间轴发生变化时停止播放
                      if (songPlaying) {
                        handlePauseSong();
                      }
                    }}
                  />
                  <ActionIcon
                    className={styles.playBtn}
                    variant="filled"
                    color="#6AFFA3"
                    size="sm"
                    radius="xl"
                    onClick={handlePlaySong}>
                    {songPlaying ? (
                      <IconPlayerPauseFilled size={14} color="#0F1114" />
                    ) : (
                      <IconPlayerPlayFilled size={14} color="#0F1114" />
                    )}
                  </ActionIcon>
                </div>

                {/* 歌词选择区域 */}
                <Lyc
                  lyricsRef={lyricsRef}
                  currentSongInfo={currentSongInfo}
                  lyricSelection={lyricSelection}
                  setLyricSelection={setLyricSelection}
                  onLyricSelectionChange={() => {
                    // 当歌词选择发生变化时停止播放
                    if (songPlaying) {
                      handlePauseSong();
                    }
                  }}
                />
                <Group justify="space-between">
                  <Button onClick={() => setSelectedSong(null)}>取消</Button>
                  <Button variant="awsome" onClick={() => handleDetermineTopic()}>
                    确定
                  </Button>
                </Group>
              </Stack>
            ) : null}

            {/* 确定主题之后 */}
            {showBasicConfig ? (
              <>
                <Group justify="space-between" align="center" mb="md">
                  <Text size="sm" fw={500}>
                    歌曲内容
                  </Text>
                  {!scriptInfo?.vecNewScene?.length && (
                    <Button
                      size="xs"
                      onClick={() => {
                        setSelectedSong(null);
                        setShowBasicConfig(false);
                      }}>
                      重新选歌
                    </Button>
                  )}
                </Group>
                <Stack className={styles.lyrbox} p="md">
                  <Group justify="space-between" align="center">
                    <Text size="sm">
                      {scriptInfo?.stTopic.stSongInfo.strSongName} - {scriptInfo?.stTopic.stSongInfo.strSinger}
                    </Text>
                    <Group gap="xs">
                      <Text size="xs" c="dimmed">
                        {formatMsToTime(scriptInfo?.iTimeRange || 0)}
                      </Text>
                    </Group>
                  </Group>
                  <Divider color="#333743" />
                  <Box className={styles.lyricsScroll}>
                    {scriptInfo?.stTopic?.stSongInfo?.veclyric?.map(lyric => (
                      <Text size="sm" key={lyric.iStartMs}>
                        {lyric.strContent}
                      </Text>
                    ))}
                  </Box>
                </Stack>
              </>
            ) : null}
          </Paper>

          {/* 拉取展示基础配置 */}
          {showBasicConfig && (
            <BasicConfig
              basicConfig={basicConfig}
              scriptInfo={scriptInfo}
              isFailed={isFailed}
              selectedMvQuality={selectedMvQuality}
              setSelectedMvQuality={setSelectedMvQuality}
              modifyTopic={modifyTopic}
              setScriptInfo={setScriptInfo}
              handleDetermineTopic={handleDetermineTopic}
            />
          )}
        </Paper>
      )}
    </>
  );
}
