import React from 'react';
import { SegmentedControl, Text, Tooltip } from '@mantine/core';
import {
  $MVTemplate,
  $VideoSize,
  $MVQuality,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';

interface SelectProps {
  value?: string;
  onChange: (value: string) => void;
  data: $VideoSize[] | $MVTemplate[] | $MVQuality[];
  type: 'mv' | 'size' | 'quality';
}

export const SegSelect = ({ value, onChange, data, type }: SelectProps) => {
  const itemOptions = data.map(item => {
    if (type === 'size') {
      const videoSize = item as $VideoSize;
      return {
        value: videoSize.sizeName,
        label: (
          <Text size="sm">
            {videoSize.sizeName} {videoSize.lWidth}:{videoSize.lHeight}
          </Text>
        ),
      };
    } else if (type === 'quality') {
      const mvQuality = item as $MVQuality;
      return {
        value: mvQuality.strId,
        disabled: mvQuality.iCanUse === 0,
        label: (
          <Tooltip withArrow disabled={!mvQuality?.strDesc} label={mvQuality?.strDesc || ''}>
            <Text size="sm">{mvQuality.strName}</Text>
          </Tooltip>
        ),
      };
    } else {
      const mvTemplate = item as $MVTemplate;
      return {
        value: mvTemplate.strId,
        label: <Text size="sm">{mvTemplate.strName}</Text>,
      };
    }
  });

  return <SegmentedControl mb="xs" value={value} onChange={onChange} data={itemOptions} transitionDuration={0} />;
};
