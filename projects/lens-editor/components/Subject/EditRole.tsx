import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Modal,
  TextInput,
  Button,
  Group,
  Stack,
  Text,
  Image,
  ActionIcon,
  Select,
  Box,
  Flex,
  Alert,
  LoadingOverlay,
  Tabs,
  SimpleGrid,
  BackgroundImage,
} from '@mantine/core';
import { IconX, IconInfoCircle } from '@tabler/icons-react';
import { useEditorStore } from '../../store/editorStore';
import req from 'common/request';
import { defaultImg } from 'common/utils';
import { emScriptGetMask, emEditType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import {
  emStatus,
  $RoleInfo,
  $StoryboardInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { notifications } from '@mantine/notifications';
import { isNotEmpty, useForm } from '@mantine/form';
import useConfirm from '../ConfirmationContext';
import AIInputField from '../common/AIInputField';
import styles from './index.module.less';
import appcss from '../../App.less';

interface EditRoleProps {
  storyboard?: $StoryboardInfo; // 当前分镜
  onClose: () => void;
  initialData: $RoleInfo | null;
  showRoleSelectTab?: boolean; // 是否显示角色列表选择标签页
}

const ageOptions = Array.from({ length: 100 }, (_, i) => ({
  value: `${i + 1}岁`,
  label: `${i + 1}岁`,
}));

export default function EditRole({ storyboard, onClose, initialData, showRoleSelectTab = false }: EditRoleProps) {
  const { scriptId, fetchScriptInfoOnce, basicConfig, scriptInfo } = useEditorStore();
  const [loading, setLoading] = useState(false);
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [hasGeneratedPreview, setHasGeneratedPreview] = useState(false);
  const [generatedRoleId, setGeneratedRoleId] = useState<string | null>(null);
  const [generatedRole, setGeneratedRole] = useState<$RoleInfo | null>(null);
  const [pollCount, setPollCount] = useState(0);
  const [activeTab, setActiveTab] = useState<string>(showRoleSelectTab ? 'roleList' : 'edit'); // 'edit' 或 'roleList'

  // 初始化选中的角色ID，默认包含当前分镜已绑定的角色ID
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>(
    storyboard?.vecRoleId ? [...storyboard.vecRoleId] : []
  );
  const { updateStoryboardRoles } = useEditorStore.getState();
  const MAX_POLL_COUNT = 100; // 最大轮询次数
  const POLL_INTERVAL = 3000; // 轮询间隔(毫秒)
  const confirm = useConfirm();
  console.log('[basicConfig vecRegion]:', basicConfig?.vecRegion[0].eRegion);
  const form = useForm({
    mode: 'controlled',
    initialValues: {
      name: initialData?.strName || '',
      gender: initialData?.strGen || '',
      age: initialData?.strAge || '',
      eRegion: initialData?.eRegion?.toString() || basicConfig?.vecRegion?.[0]?.eRegion?.toString() || '',
      desc: initialData?.strDesc || '',
      imageUrl: initialData?.strUrl || '',
      faceImageUrl: initialData?.strFaceReferPic || '',
      clothesImageUrl: initialData?.strDressRefrePic || '',
    },
    validate: {
      name: isNotEmpty('请输入姓名'),
      gender: isNotEmpty('请选择性别'),
      age: isNotEmpty('请选择年龄'),
      eRegion: isNotEmpty('请选择地区'),
      desc: isNotEmpty('请输入角色描述'),
    },
  });

  const title = useMemo(() => {
    return initialData ? '编辑角色' : '创建角色';
  }, [initialData]);

  const isEditMode = !!initialData;
  const usedInStoryboards = initialData?.iUseStoryboardCnt || 0;
  const displayConfirmButtons = hasGeneratedPreview && generatedRole?.eStatus === emStatus.EM_STATUS_SUCC;

  // 从scriptInfo中获取已选中使用的角色列表
  const selectedRoles = useMemo(() => {
    if (!scriptInfo || !scriptInfo.vecRoleInfo) {
      return [];
    }

    // 过滤出已使用的角色
    return scriptInfo.vecRoleInfo.filter(role => role.iUse === 1);
  }, [scriptInfo]);

  // 当storyboard变化时更新选中状态
  useEffect(() => {
    if (storyboard?.vecRoleId) {
      setSelectedRoleIds([...storyboard.vecRoleId]);
    } else {
      setSelectedRoleIds([]);
    }
  }, [storyboard]);

  // 清理轮询定时器
  useEffect(() => {
    return () => {
      if (pollingTimerRef.current) {
        console.log('[组件卸载] 清理轮询定时器');
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
    };
  }, []);

  // 处理关闭请求，根据条件判断是否弹出二次确认
  const handleRequestClose = () => {
    if (hasGeneratedPreview) {
      console.log('[处理关闭请求] 弹出二次确认');
      confirm({
        title: '确认关闭',
        description: '您当前生成的预览角色还未应用或保存，关闭后将丢失当前生成的预览角色，建议保存或应用后再关闭。',
        confirmLabel: '确认关闭',
        onCancel: () => {
          console.log('用户取消关闭角色编辑弹窗');
        },
        onConfirm: () => {
          console.log('用户确认关闭角色编辑弹窗');
          onClose(); // 调用父组件传递的 onClose
        },
      });
    } else {
      onClose(); // 直接关闭
    }
  };

  // 开始轮询角色生成状态
  const startPollingRoleInfo = (roleId: string) => {
    setPollCount(0);

    // 先清除之前的定时器
    if (pollingTimerRef.current) {
      console.log('[开始新轮询] 清理旧定时器');
      clearInterval(pollingTimerRef.current);
      pollingTimerRef.current = null;
    }

    console.log('[开始轮询] roleId:', roleId);

    // 创建新的定时器
    const timer = setInterval(() => {
      setPollCount(prev => {
        const newCount = prev + 1;
        if (newCount >= MAX_POLL_COUNT) {
          console.log('[轮询超时] 达到最大轮询次数，停止轮询');
          clearInterval(timer);
          pollingTimerRef.current = null;
          setLoading(false);
          return newCount;
        }
        return newCount;
      });

      // 调用轮询函数
      pollRoleInfo(roleId);
    }, POLL_INTERVAL);

    // 保存定时器引用到ref
    pollingTimerRef.current = timer;
  };

  // 获取角色生成状态
  const pollRoleInfo = async (roleId: string) => {
    try {
      console.log(`[轮询请求] 第${pollCount + 1}次请求, roleId: ${roleId}`);
      const response = await req.post('/lens_script/get_role_info', {
        strScriptId: scriptId,
        strRoleId: roleId,
      });

      if (response.data.error_code === 0) {
        const roleInfo = response.data.data.stRoleInfo;
        setGeneratedRole(roleInfo);

        // 如果角色生成完成或失败，停止轮询
        if (roleInfo.eStatus === emStatus.EM_STATUS_SUCC || roleInfo.eStatus === emStatus.EM_STATUS_FAIL) {
          console.log(
            '[角色生成完成] roleInfo:',
            roleInfo,
            '状态:',
            roleInfo.eStatus === emStatus.EM_STATUS_SUCC ? '成功' : '失败'
          );

          // 使用ref直接清除定时器
          if (pollingTimerRef.current) {
            console.log('[停止轮询] 清理定时器');
            clearInterval(pollingTimerRef.current);
            pollingTimerRef.current = null;
          }

          setLoading(false);

          // 如果生成失败，显示提示
          if (roleInfo.eStatus === emStatus.EM_STATUS_FAIL) {
            notifications.show({
              title: '角色生成失败',
              message: '请修改描述后重新尝试',
              color: 'red',
            });
            setHasGeneratedPreview(false);
          }
        } else {
          console.log('[角色生成中] 状态:', roleInfo.eStatus);
        }
      }
    } catch (error) {
      console.error('[轮询错误] 获取角色信息失败', error);
    }
  };

  // 生成角色预览
  const handleGenerate = () => {
    // 表单验证
    const validation = form.validate();

    if (validation.hasErrors) {
      return;
    }

    setLoading(true);
    const formData = form.values;

    // 准备角色数据
    const roleData: Partial<$RoleInfo> = {
      strName: formData.name,
      strDesc: formData.desc,
      strGen: formData.gender,
      strAge: formData.age,
      eRegion: Number(formData.eRegion),
    };

    // 如果是编辑模式，添加ID
    if (initialData?.strId) {
      roleData.strId = initialData.strId;
    }

    (async () => {
      try {
        const response = await req.post('/lens_script/edit_role', {
          strScriptId: scriptId,
          vectorEditField: [emEditType.EM_EDIT_ROLE_TYPE_AI_GEN],
          vecRole: [roleData],
        });

        if (response.data.error_code !== 0 || response.data.data.iRes !== 0) {
          throw new Error(response.data.error_msg || response.data.data.strTips);
        }

        // 获取生成的角色ID
        const generatedRoleId = response.data.data.stRole?.strId;
        if (!generatedRoleId) {
          throw new Error('未获取到生成的角色ID');
        }

        setGeneratedRoleId(generatedRoleId);
        setHasGeneratedPreview(true);

        // 开始轮询角色生成状态
        startPollingRoleInfo(generatedRoleId);
      } catch (error: unknown) {
        setLoading(false);
        const errorMessage = error instanceof Error ? error.message : '请稍后重试';
        notifications.show({
          title: '生成预览失败',
          message: errorMessage,
          color: 'red',
        });
      }
    })();
  };

  // 确认创建角色
  const handleConfirm = () => {
    if (!generatedRoleId) {
      return;
    }

    setLoading(true);

    (async () => {
      try {
        const response = await req.post('/lens_script/edit_role', {
          strScriptId: scriptId,
          vectorEditField: [emEditType.EM_EDIT_ROLE_TYPE_CONFIRM],
          vecRole: [{ strId: generatedRoleId }],
        });

        if (response.data.error_code !== 0 || response.data.data.iRes !== 0) {
          throw new Error(response.data.error_msg || response.data.data.strTips);
        }

        notifications.show({
          title: isEditMode ? '修改角色成功' : '创建角色成功',
          message: response.data.data.strTips,
        });
        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ROLE);

        // 如果是从分镜打开的,则绑定角色到分镜
        if (storyboard) {
          try {
            const bindResponse = await req.post('/lens_script/edit_storyboard', {
              strScriptId: scriptId,
              vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_ROLEID],
              stBoardInfo: [
                {
                  ...storyboard,
                  vecRoleId: [...(storyboard.vecRoleId || []), generatedRoleId],
                },
              ],
            });

            if (bindResponse.data.error_code !== 0 || bindResponse.data.data.iRes !== 0) {
              throw new Error(bindResponse.data.error_msg || bindResponse.data.data.strTips);
            }

            notifications.show({
              title: '角色绑定成功',
              message: '已将新创建的角色绑定到当前分镜',
              color: 'green',
            });

            // 刷新分镜数据
            fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL);
          } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : '请稍后重试';
            notifications.show({
              title: '角色绑定失败',
              message: errorMessage,
              color: 'red',
            });
          }
        }

        onClose();
      } catch (error: unknown) {
        setLoading(false);
        const errorMessage = error instanceof Error ? error.message : '请稍后重试';
        notifications.show({
          title: isEditMode ? '修改角色失败' : '创建角色失败',
          message: errorMessage,
          color: 'red',
        });
      }
    })();
  };

  // 一键应用到所有分镜
  const handleApplyToAll = () => {
    if (!initialData?.strId || !generatedRoleId) {
      return;
    }

    setLoading(true);

    (async () => {
      try {
        const response = await req.post('/lens_script/edit_role', {
          strScriptId: scriptId,
          vectorEditField: [emEditType.EM_EDIT_ROLE_TYPE_CHANGE_ROLE],
          vecRole: [
            {
              strId: initialData.strId, // 原角色ID
            },
            {
              strId: generatedRoleId, // 新角色ID
            },
          ],
        });

        if (response.data.error_code !== 0 || response.data.data.iRes !== 0) {
          throw new Error(response.data.error_msg || response.data.data.strTips);
        }

        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ROLE);

        notifications.show({
          title: '一键应用成功',
          message: response.data.data.strTips,
        });

        onClose();
      } catch (error: unknown) {
        setLoading(false);
        const errorMessage = error instanceof Error ? error.message : '请稍后重试';
        notifications.show({
          title: '一键应用失败',
          message: errorMessage,
          color: 'red',
        });
      }
    })();
  };

  // 选择角色 - 切换角色的选中状态
  const handleSelectRole = (role: $RoleInfo) => {
    setSelectedRoleIds(prev => {
      // 如果已经选中，则取消选中
      if (prev.includes(role.strId)) {
        return prev.filter(id => id !== role.strId);
      }
      // 否则添加到选中列表
      return [...prev, role.strId];
    });
  };

  const handleBindRoles = () => {
    if (!storyboard) {
      return;
    }

    // 创建更新后的分镜信息，包含选中的角色ID
    const updatedStoryboard = {
      ...storyboard,
      vecRoleId: selectedRoleIds,
    };

    // 调用updateStoryboardRoles方法更新分镜的角色绑定
    updateStoryboardRoles(updatedStoryboard, [], true)
      .then(() => {
        notifications.show({
          title: '角色绑定成功',
          message: `已更新分镜角色绑定，共${selectedRoleIds.length}个角色`,
          color: 'green',
        });
        onClose();
      })
      .catch(error => {
        console.error('角色绑定失败:', error);
        notifications.show({
          title: '角色绑定失败',
          message: '请稍后重试',
          color: 'red',
        });
      })
      .finally(() => {
        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL);
      });
  };

  // 处理标签页切换
  const handleTabChange = (value: string | null) => {
    if (value !== null) {
      // 切换标签页，但保持选中状态
      setActiveTab(value);
    }
  };

  return (
    <Modal
      opened={true}
      onClose={handleRequestClose}
      closeOnEscape={true}
      closeOnClickOutside={true}
      size="xl"
      padding={0}
      withCloseButton={false}
      styles={{
        content: {
          backgroundColor: '#2C3040',
          borderRadius: '8px',
        },
        body: {
          padding: 0,
        },
      }}>
      <Box p="md">
        {/* 顶部标签页和关闭按钮 */}
        <Group justify="space-between" mb="md">
          {showRoleSelectTab ? (
            <Tabs className={appcss.mytab} value={activeTab} onChange={handleTabChange}>
              <Tabs.List>
                <Tabs.Tab value="edit">{isEditMode ? '编辑角色' : '创建角色'}</Tabs.Tab>
                <Tabs.Tab value="roleList">角色列表</Tabs.Tab>
              </Tabs.List>
            </Tabs>
          ) : (
            <Text size="lg" fw={500} c="white">
              {title}
            </Text>
          )}
          <ActionIcon variant="transparent" onClick={handleRequestClose} c="white">
            <IconX size={24} />
          </ActionIcon>
        </Group>

        {activeTab === 'edit' ? (
          <form noValidate>
            <Flex gap="md">
              {/* 左侧角色图片 */}
              <Box w={200} pos="relative" className={styles.roleImg}>
                <LoadingOverlay visible={loading} />
                <Image
                  src={generatedRole?.strUrl || form.values.imageUrl}
                  fallbackSrc={defaultImg}
                  alt="角色头像"
                  radius="sm"
                  fit="contain"
                />
              </Box>

              {/* 右侧表单区域 */}
              <Stack gap="md" style={{ flex: 1 }}>
                {/* 姓名 */}
                <TextInput
                  label="姓名"
                  placeholder="请输入姓名"
                  {...form.getInputProps('name')}
                  styles={{
                    label: {
                      color: 'white',
                      marginBottom: '8px',
                    },
                    input: {
                      backgroundColor: '#141517',
                      color: 'white',
                      border: 'none',
                      height: '40px',
                    },
                  }}
                  disabled={loading || displayConfirmButtons}
                  required
                />

                {/* 性别、年龄、类别 */}
                <Group grow>
                  <Select
                    label="性别"
                    placeholder="请选择性别"
                    {...form.getInputProps('gender')}
                    data={[
                      { value: '女', label: '女' },
                      { value: '男', label: '男' },
                    ]}
                    styles={{
                      label: {
                        color: 'white',
                        marginBottom: '8px',
                      },
                      input: {
                        backgroundColor: '#141517',
                        color: 'white',
                        border: 'none',
                        height: '40px',
                      },
                    }}
                    rightSection={<IconX size={14} style={{ transform: 'rotate(45deg)' }} />}
                    disabled={loading || displayConfirmButtons}
                    required
                  />

                  <Select
                    label="年龄"
                    placeholder="请选择年龄"
                    {...form.getInputProps('age')}
                    data={ageOptions}
                    searchable
                    styles={{
                      label: {
                        color: 'white',
                        marginBottom: '8px',
                      },
                      input: {
                        backgroundColor: '#141517',
                        color: 'white',
                        border: 'none',
                        height: '40px',
                      },
                    }}
                    rightSection={<IconX size={14} style={{ transform: 'rotate(45deg)' }} />}
                    disabled={loading || displayConfirmButtons}
                    required
                  />

                  <Select
                    label="地区"
                    placeholder="请选择地区"
                    {...form.getInputProps('eRegion')}
                    data={
                      basicConfig?.vecRegion?.map(item => ({
                        value: item.eRegion?.toString() || '',
                        label: item.strRegionName || '',
                      })) || []
                    }
                    styles={{
                      label: {
                        color: 'white',
                        marginBottom: '8px',
                      },
                      input: {
                        backgroundColor: '#141517',
                        color: 'white',
                        border: 'none',
                        height: '40px',
                      },
                    }}
                    rightSection={<IconX size={14} style={{ transform: 'rotate(45deg)' }} />}
                    disabled={loading || displayConfirmButtons}
                    required
                  />
                </Group>

                {/* 角色描述 - 使用AIInputField替换Textarea */}
                <Stack gap="xs">
                  <AIInputField
                    type="textarea"
                    value={form.values.desc}
                    onChange={value => form.setFieldValue('desc', value)}
                    apiPath="/lens_script/edit_role"
                    apiField={emEditType.EM_EDIT_ROLE_TYPE_GEN_ROLE_TIPS}
                    apiParams={{
                      strScriptId: scriptId,
                      vecRole: [
                        {
                          strName: form.values.name,
                          strGen: form.values.gender,
                          strAge: form.values.age,
                          eRegion: Number(form.values.eRegion),
                          strDesc: form.values.desc,
                        },
                      ],
                    }}
                    apiResponseHandler={data => data.stRole?.strDesc || ''}
                    disabled={loading}
                    onValidation={() => {
                      // 验证必填字段，但排除描述字段（因为AI文案就是用来生成描述的）
                      const requiredFields = ['name', 'gender', 'age', 'eRegion'];
                      for (const field of requiredFields) {
                        const value = form.values[field as keyof typeof form.values];
                        if (!value || (typeof value === 'string' && value.trim() === '')) {
                          // 只验证必填字段，不验证描述字段
                          const validationErrors: Record<string, string> = {};
                          requiredFields.forEach(f => {
                            const v = form.values[f as keyof typeof form.values];
                            if (!v || (typeof v === 'string' && v.trim() === '')) {
                              switch (f) {
                                case 'name':
                                  validationErrors.name = '请输入姓名';
                                  break;
                                case 'gender':
                                  validationErrors.gender = '请选择性别';
                                  break;
                                case 'age':
                                  validationErrors.age = '请选择年龄';
                                  break;
                                case 'eRegion':
                                  validationErrors.eRegion = '请选择地区';
                                  break;
                              }
                            }
                          });
                          form.setErrors(validationErrors);
                          return false;
                        }
                      }
                      return true;
                    }}
                    textareaProps={{
                      label: '角色描述',
                      placeholder: '请输入角色描述，或点击AI文案自动生成',
                      minRows: 4,
                      error: form.errors.desc,
                      styles: {
                        label: {
                          marginBottom: '8px',
                        },
                        input: {
                          backgroundColor: '#141517',
                          color: 'white',
                          border: 'none',
                        },
                      },
                      required: true,
                      onFocus: () => form.setFieldError('desc', undefined),
                    }}
                  />
                </Stack>

                {/* 提示信息 */}
                {isEditMode && usedInStoryboards > 0 && displayConfirmButtons ? (
                  <Alert icon={<IconInfoCircle size={16} />} color="red" radius="md">
                    该角色涉及 {usedInStoryboards}
                    个分镜需要重绘，请点击&quot;一键应用&quot;，将新角色应用到相关分镜。未应用的角色不会被保存。
                  </Alert>
                ) : (
                  hasGeneratedPreview &&
                  generatedRole?.eStatus === emStatus.EM_STATUS_SUCC && (
                    <Alert icon={<IconInfoCircle size={16} />} color="red" radius="md">
                      角色已生成，但是还未保存，请点击&quot;确认&quot;保存角色。
                    </Alert>
                  )
                )}
              </Stack>
            </Flex>

            {/* 底部按钮 */}
            <Group justify="flex-end" mt="xl">
              {/* 根据不同阶段显示不同按钮 */}

              <Button onClick={handleGenerate} loading={loading}>
                {hasGeneratedPreview ? '重新生成' : initialData ? '重新生成' : '立即生成'}
              </Button>

              {/* 确认按钮，仅在生成预览成功后显示 */}
              {displayConfirmButtons && (
                <>
                  {isEditMode ? (
                    <Button onClick={handleApplyToAll} loading={loading} variant="awsome">
                      {usedInStoryboards > 0 ? '一键应用' : '确认修改'}
                    </Button>
                  ) : (
                    <Button onClick={handleConfirm} loading={loading} variant="awsome">
                      确认创建
                    </Button>
                  )}
                </>
              )}
            </Group>
          </form>
        ) : (
          /* 角色列表标签页 */
          <Box>
            {selectedRoles.length > 0 ? (
              <SimpleGrid cols={3} spacing="sm" mt="xl" verticalSpacing="sm">
                {selectedRoles.map((role, index) => {
                  const isSelected = selectedRoleIds.includes(role.strId);
                  return (
                    <BackgroundImage
                      key={role.strId || index}
                      src={role.strUrl + '?imageMogr2/format/webp|imageMogr2/crop/180x180/gravity/north'}
                      className={`${styles.roleCard} ${isSelected ? styles.roleCardSelected : ''}`}
                      onClick={() => handleSelectRole(role)}></BackgroundImage>
                  );
                })}
              </SimpleGrid>
            ) : (
              <Text c="dimmed" size="sm">
                暂无角色
              </Text>
            )}
            <Group justify="flex-end" mt="xl">
              <Button onClick={onClose} loading={loading} variant="outline">
                取消
              </Button>
              <Button onClick={handleBindRoles} loading={loading}>
                更新角色绑定 {selectedRoleIds.length > 0 && `(${selectedRoleIds.length})`}
              </Button>
            </Group>
          </Box>
        )}
      </Box>
    </Modal>
  );
}
