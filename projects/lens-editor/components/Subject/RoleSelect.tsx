import React, { useState, useEffect } from 'react';
import {
  Box,
  Text,
  Group,
  Avatar,
  Card,
  Badge,
  SimpleGrid,
  ActionIcon,
  Tooltip,
  ScrollArea,
  Loader,
  BackgroundImage,
  LoadingOverlay,
  Checkbox,
} from '@mantine/core';
import { IconEdit, IconPlus, IconSettings, IconTrash, IconX } from '@tabler/icons-react';
import styles from './index.module.less';
import {
  $RoleInfo,
  EmRoleType,
  emStatus,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { emEditType, emScriptGetMask } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import req from 'common/request';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import { notifications } from '@mantine/notifications';
import { defaultImg } from 'common/utils';

interface RoleSelectProps {
  roles: $RoleInfo[];
  onAddRole?: () => void;
  onEditRole?: (role: $RoleInfo) => void;
  onToggleRoles?: (roleIds: string[]) => void;
  confirm: (options: {
    title: string;
    description: string;
    onConfirm: () => Promise<void> | void;
    onCancel?: () => void;
  }) => void;
  onRoleDeleted: () => void;
}

export const RoleSelect: React.FC<RoleSelectProps> = ({
  roles,
  onAddRole,
  onEditRole,
  onToggleRoles,
  confirm,
  onRoleDeleted,
}) => {
  const { scriptInfo, scriptId, fetchScriptInfoOnce } = useEditorStore();
  console.log('[角色选择] roles:', roles);

  // 用于跟踪当前选中的角色ID列表
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>([]);

  // 初始化选中状态
  useEffect(() => {
    if (roles) {
      // 根据iUse字段初始化选中状态
      const initialSelectedIds = roles.filter(role => role.iUse === 1).map(role => role.strId);
      setSelectedRoleIds(initialSelectedIds);
    }
  }, [roles]);

  // 处理角色选择变更
  const handleToggleRole = (roleId: string, isChecked: boolean) => {
    const newSelectedIds = isChecked ? [...selectedRoleIds, roleId] : selectedRoleIds.filter(id => id !== roleId);

    setSelectedRoleIds(newSelectedIds);
    console.log('[newSelectedIds]:', newSelectedIds);

    // 调用父组件回调
    if (onToggleRoles) {
      onToggleRoles(newSelectedIds);
    }
  };
  const handleReGenerate = (role: $RoleInfo) => {
    console.log('[重新生成] role:', role);
    req
      .post('/lens_script/edit_role', {
        strScriptId: scriptId,
        vectorEditField: [emEditType.EM_EDIT_ROLE_TYPE_REGEN],
        vecRole: [role],
      })
      .then(res => {
        if (res.data.error_code !== 0) {
          notifications.show({
            title: '重新生成失败',
            message: res.data.error_msg,
            color: 'red',
          });
          return;
        }
        notifications.show({
          message: '重新生成成功',
          color: 'green',
        });
        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ROLE);
      })
      .catch(error => {
        console.error('重新生成失败:', error);
        notifications.show({
          title: '重新生成失败',
          message: '请稍后重试',
          color: 'red',
        });
      });
  };

  const onDeleteRole = (roleId: string) => {
    console.log('[删除角色] roleId:', roleId);
    confirm({
      title: '确认删除',
      description: '您确定要删除这个角色吗？此操作无法撤销。',
      onConfirm: async () => {
        try {
          const response = await req.post('/lens_script/edit_role', {
            strScriptId: scriptInfo?.strScriptId,
            vectorEditField: [emEditType.EM_EDIT_ROLE_TYPE_DELETE_ROLE],
            vecRole: [{ strId: roleId }],
          });

          if (response.data.error_code === 0) {
            notifications.show({
              title: '成功',
              message: '角色删除成功',
              color: 'green',
            });
            onRoleDeleted();
          } else {
            notifications.show({
              title: '删除失败',
              message: response.data.error_msg || '删除角色失败，请稍后重试',
              color: 'red',
            });
          }
        } catch (error: any) {
          console.error('删除角色失败', error);
          notifications.show({
            title: '删除失败',
            message: error.message || '网络错误，删除角色失败',
            color: 'red',
          });
        }
      },
    });
  };

  return (
    <ScrollArea
      offsetScrollbars={'x'}
      w="100%"
      classNames={{ root: styles.roleSelectWrap, viewport: styles.roleSelectViewport }}>
      <Group wrap="nowrap" gap="8">
        {roles?.map(role => {
          const isSelected = selectedRoleIds.includes(role.strId);
          const isGenerating = role.eStatus === emStatus.EM_STATUS_RUNNING;
          if (role.eStatus === emStatus.EM_STATUS_FAIL) {
            return (
              <Box
                key={role.strId}
                pos="relative"
                onClick={() => handleReGenerate(role)}
                className={styles.roleCardContainer}>
                <Text c="dimmed" size="sm" style={{ cursor: 'pointer', textAlign: 'center' }}>
                  角色生成失败 点击重试
                </Text>
              </Box>
            );
          }

          return (
            <Box key={role.strId} pos="relative" className={styles.roleCardContainer}>
              <BackgroundImage
                src={role.strUrl || defaultImg}
                onClick={e => {
                  e.stopPropagation();
                  handleToggleRole(role.strId, !isSelected);
                }}
                className={`${styles.roleCard} ${isSelected ? styles.roleCardSelected : ''}`}>
                <LoadingOverlay visible={isGenerating} loaderProps={{ type: 'dots' }} />
                <Text className={styles.roleName}>{role.strName}</Text>
                <ActionIcon
                  className={styles.roleDeleteBtn}
                  size="sm"
                  onClick={e => {
                    e.stopPropagation();
                    onDeleteRole(role.strId);
                  }}>
                  <IconTrash size={12} />
                </ActionIcon>

                {onEditRole && (
                  <ActionIcon
                    className={styles.roleEditBtn}
                    size="sm"
                    disabled={role.eStatus === emStatus.EM_STATUS_RUNNING}
                    onClick={e => {
                      e.stopPropagation();
                      onEditRole(role);
                    }}>
                    <IconEdit size={12} />
                  </ActionIcon>
                )}
              </BackgroundImage>
            </Box>
          );
        })}

        {/* 添加角色按钮，始终显示 */}
        {onAddRole && (
          <Card className={styles.addRoleCard} onClick={onAddRole}>
            <Group justify="center" align="center" gap={0} style={{ height: '100%' }}>
              <IconPlus size={24} />
              <Text size="xs">添加角色</Text>
            </Group>
          </Card>
        )}
      </Group>
    </ScrollArea>
  );
};
