import React, { useRef } from 'react';
import { Box, Text } from '@mantine/core';

import { $SongInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { LyricSelection } from '../../store/editorStore';
import { formatMsToTime } from 'common/utils';
import styles from './index.module.less';

// 拖拽状态类型
type DragEdgeType = 'top' | 'bottom' | null;

// 歌词选择组件
export default function Lyc({
  lyricsRef,
  currentSongInfo,
  lyricSelection,
  setLyricSelection,
  onLyricSelectionChange,
}: {
  lyricsRef: React.RefObject<HTMLDivElement>;
  currentSongInfo?: $SongInfo;
  lyricSelection: LyricSelection;
  setLyricSelection: (lyricSelection: LyricSelection) => void;
  onLyricSelectionChange?: () => void;
}) {
  const dragEdgeRef = useRef<DragEdgeType>(null);
  const dragStartY = useRef<number>(0);
  const initialSelection = useRef<LyricSelection>(lyricSelection);

  // 检查是否在拖拽边界
  const checkIfInDraggableEdge = (e: React.MouseEvent<HTMLDivElement>): DragEdgeType => {
    if (!lyricsRef.current) return null;

    const rect = lyricsRef.current.getBoundingClientRect();
    const mouseY = e.clientY;
    const firstLyricEl = lyricsRef.current.querySelector(`.${styles.lyricItem}`);
    const lineHeight = firstLyricEl?.getBoundingClientRect().height || 28;

    // 计算鼠标所在的歌词索引 -8 是因为容器有padding
    const index = Math.floor((mouseY + lyricsRef.current.scrollTop - rect.top - 8) / lineHeight);
    // 往外扩一行
    if (index === lyricSelection.startIndex || index === lyricSelection.startIndex - 1) {
      return 'top';
    } else if (index === lyricSelection.endIndex || index === lyricSelection.endIndex + 1) {
      return 'bottom';
    }

    return null;
  };

  const handleLyricMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const currentDragEdge = dragEdgeRef.current || checkIfInDraggableEdge(e);
    console.log('dragEdge', currentDragEdge);

    if (currentDragEdge) {
      e.preventDefault();
      dragStartY.current = e.clientY;
      initialSelection.current = { ...lyricSelection };

      dragEdgeRef.current = currentDragEdge;
      if (lyricsRef.current) {
        lyricsRef.current.style.cursor = 'ns-resize';
      }

      window.addEventListener('mousemove', handleDragMove);
      window.addEventListener('mouseup', handleDragEnd);
      window.addEventListener('mouseleave', handleDragEnd);
    }
  };

  // 歌词拖动处理函数
  const handleDragMove = (e: MouseEvent) => {
    console.log('handleDragMove', e, lyricsRef.current, dragEdgeRef.current);
    if (!lyricsRef.current || !dragEdgeRef.current) return;

    const deltaY = e.clientY - dragStartY.current;
    // 使用正确的选择器获取歌词元素
    const firstLyricEl = lyricsRef.current.querySelector(`.${styles.lyricItem}`);
    const lineHeight = firstLyricEl?.getBoundingClientRect().height || 28;
    const deltaLines = Math.round(deltaY / lineHeight);

    let newStart = initialSelection.current.startIndex;
    let newEnd = initialSelection.current.endIndex;

    // 根据拖拽边界更新选区
    if (dragEdgeRef.current === 'top') {
      newStart = Math.max(0, initialSelection.current.startIndex + deltaLines);
      newStart = Math.min(newStart, initialSelection.current.endIndex - 1);

      // 检查鼠标是否接近容器顶部，需要滚动
      const containerRect = lyricsRef.current.getBoundingClientRect();
      const mouseDistanceFromTop = e.clientY - containerRect.top;
      const scrollThreshold = lineHeight; // 使用一行歌词的高度作为滚动阈值
      console.log('[mouseDistanceFromTop]:', mouseDistanceFromTop);

      if (mouseDistanceFromTop < scrollThreshold) {
        // 距离边缘越近，滚动速度越快
        const scrollSpeed = Math.max(1, Math.round((scrollThreshold - mouseDistanceFromTop) / 5));

        // 直接控制容器滚动位置，进行像素级的滚动
        lyricsRef.current.scrollTop = Math.max(0, lyricsRef.current.scrollTop - scrollSpeed);

        // 根据滚动后的位置计算可见的第一行歌词
        const currentTopRow = Math.floor(lyricsRef.current.scrollTop / lineHeight);

        // 如果第一行歌词比当前选区起始位置要早，扩展选区
        if (currentTopRow < newStart) {
          newStart = Math.max(0, currentTopRow);
        }
      }
    } else if (dragEdgeRef.current === 'bottom') {
      newEnd = Math.min((currentSongInfo?.veclyric?.length || 0) - 1, initialSelection.current.endIndex + deltaLines);
      newEnd = Math.max(newEnd, initialSelection.current.startIndex + 1);

      // 检查鼠标是否接近容器底部，需要滚动
      const containerRect = lyricsRef.current.getBoundingClientRect();
      const mouseDistanceFromBottom = containerRect.bottom - e.clientY;
      const scrollThreshold = lineHeight; // 使用一行歌词的高度作为滚动阈值

      if (mouseDistanceFromBottom < scrollThreshold) {
        // 距离边缘越近，滚动速度越快
        const scrollSpeed = Math.max(1, Math.round((scrollThreshold - mouseDistanceFromBottom) / 5));

        // 直接控制容器滚动位置，进行像素级的滚动
        lyricsRef.current.scrollTop = lyricsRef.current.scrollTop + scrollSpeed;

        // 计算当前可见的最后一行歌词
        const containerHeight = lyricsRef.current.clientHeight;
        const currentBottomRow = Math.ceil((lyricsRef.current.scrollTop + containerHeight) / lineHeight) - 1;

        // 如果最后一行歌词比当前选区结束位置要晚，扩展选区
        if (currentBottomRow > newEnd) {
          newEnd = Math.min((currentSongInfo?.veclyric?.length || 0) - 1, currentBottomRow);
        }
      }
    }

    // 只有在选区变化时才更新
    if (newStart !== lyricSelection.startIndex || newEnd !== lyricSelection.endIndex) {
      onLyricSelectionChange?.();

      const startTime = (currentSongInfo?.veclyric[newStart]?.iStartMs || 0) / 1000;
      const endTime = (currentSongInfo?.veclyric[newEnd]?.iEndMs || 0) / 1000;

      setLyricSelection({
        startIndex: newStart,
        endIndex: newEnd,
        startTime,
        endTime,
      });
    }
  };

  const handleDragEnd = () => {
    dragEdgeRef.current = null;
    if (lyricsRef.current) {
      lyricsRef.current.style.cursor = 'default';
    }
    window.removeEventListener('mousemove', handleDragMove);
    window.removeEventListener('mouseup', handleDragEnd);
    window.removeEventListener('mouseleave', handleDragEnd);
  };

  const handleLyricClick = (index: number) => {
    // 获取当前选区边界
    const currentStart = lyricSelection.startIndex;
    const currentEnd = lyricSelection.endIndex;

    let newStartIndex = currentStart;
    let newEndIndex = currentEnd;

    // 计算新的选区范围
    if (index < currentStart) {
      // 点击在选区左侧：扩展左边界
      newStartIndex = index;
    } else if (index > currentEnd) {
      // 点击在选区右侧：扩展右边界
      newEndIndex = index;
    } else {
      console.log('点击在选区中间：扩展选区', index, newEndIndex);
      // 点击在选区中间：扩展选区
      newEndIndex = index;
    }

    // 获取对应时间（毫秒转秒）
    const newStartTime = (currentSongInfo?.veclyric[newStartIndex]?.iStartMs || 0) / 1000;
    const newEndTime = (currentSongInfo?.veclyric[newEndIndex]?.iEndMs || 0) / 1000;

    // 边界检查（确保不超过歌曲总时长）
    const totalDuration = (currentSongInfo?.iAudioLength || 0) / 1000;
    const clampedEndTime = Math.min(newEndTime, totalDuration);

    // console.log(
    //   'handleLyricClick setLyricSelection',
    //   newStartIndex,
    //   'newEndIndex',
    //   newEndIndex,
    //   'newStartTime',
    //   newStartTime,
    //   'clampedEndTime',
    //   clampedEndTime
    // );
    onLyricSelectionChange?.();

    setLyricSelection({
      startIndex: newStartIndex,
      endIndex: newEndIndex,
      startTime: newStartTime,
      endTime: clampedEndTime,
    });
  };

  // 获取歌词行的样式名
  const getLyricItemClassName = (index: number) => {
    const isSelected = index >= lyricSelection.startIndex && index <= lyricSelection.endIndex;
    const isTopBoundary = isSelected && index === lyricSelection.startIndex;
    const isTopExtend = index === lyricSelection.startIndex - 1;
    const isBottomBoundary = isSelected && index === lyricSelection.endIndex;
    const isBottomExtend = index === lyricSelection.endIndex + 1;

    return `${styles.lyricItem} ${isSelected ? styles.selected : ''} ${isTopBoundary ? styles.topBoundary : ''} ${isTopExtend ? styles.topExtend : ''} ${isBottomBoundary ? styles.bottomBoundary : ''} ${isBottomExtend ? styles.bottomExtend : ''}`;
  };

  // console.log('[dragEdge]:', dragEdgeRef.current);

  return (
    <div ref={lyricsRef} className={styles.lyricsContainer} onMouseDown={handleLyricMouseDown}>
      {currentSongInfo?.veclyric?.length ? (
        currentSongInfo.veclyric?.map((lyric, index) => {
          // console.log('[lyricSelection]:', lyricSelection);
          return (
            <Text key={index} className={getLyricItemClassName(index)} onClick={() => handleLyricClick(index)}>
              <span>{formatMsToTime(lyric.iStartMs)}</span> {lyric.strContent}
            </Text>
          );
        })
      ) : (
        <Box>暂无歌词</Box>
      )}
    </div>
  );
}
