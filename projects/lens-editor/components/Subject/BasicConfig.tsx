import React, { useRef, useState, useEffect } from 'react';
import {
  Box,
  Text,
  Group,
  Stack,
  Button,
  ScrollArea,
  LoadingOverlay,
  BackgroundImage,
  Tooltip,
  ActionIcon,
  FileButton,
  Image,
  Divider,
  Center,
  Chip,
  SimpleGrid,
} from '@mantine/core';
import styles from './index.module.less';
import { SegSelect } from './segSelect';
import { RoleSelect } from './RoleSelect';
import EditRole from './EditRole';
import { notifications } from '@mantine/notifications';
import { EditableTextarea } from '../common/EditableTextarea';
import {
  $MVQuality,
  emResolution,
  $RoleInfo,
  emStatus,
  $PictureModel,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import {
  $BasicInfoRsp,
  emEditType,
  emScriptGetMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { IconInfoCircle, IconRotate, IconUpload, IconPhoto, IconX } from '@tabler/icons-react';
import req from 'common/request';
import { selectIsFlowLoading, useEditorStore } from '../../store/editorStore';
import useConfirm from '../ConfirmationContext';

// 添加接口类型定义
interface VideoSizeItem {
  sizeName: string;
  lWidth: number;
  lHeight: number;
  stResolution: number;
}

interface PicModelItem {
  strId: string;
  strModel: string;
  strReferPic: string;
}

interface QualityItem {
  strId: string;
  [key: string]: any;
}

interface MVTemplateItem {
  strId: string;
  vecInfo: QualityItem[];
  [key: string]: any;
}

interface BasicConfigProps {
  basicConfig: $BasicInfoRsp;
  scriptInfo: any;
  isFailed: boolean;
  selectedMvQuality: $MVQuality | undefined;
  setSelectedMvQuality: (quality: $MVQuality | undefined) => void;
  modifyTopic: (editType: emEditType, obj: any) => Promise<any>;
  setScriptInfo: (scriptInfo: any) => void;
  handleDetermineTopic: (editType?: emEditType) => void;
}

const BasicConfig: React.FC<BasicConfigProps> = ({
  basicConfig,
  scriptInfo,
  isFailed,
  selectedMvQuality,
  setSelectedMvQuality,

  modifyTopic,
  setScriptInfo,
  handleDetermineTopic,
}) => {
  const isFlowLoading = useEditorStore(selectIsFlowLoading);
  const { inputSummary, setInputSummary } = useEditorStore();
  const [hasSummaryUnsavedChanges, setHasSummaryUnsavedChanges] = useState(false);
  const textareaRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [uploadLoading, setUploadLoading] = useState(false);
  const confirm = useConfirm();
  // 使用全局状态管理中的imageTags
  const { imageTags, setImageTags } = useEditorStore();

  // 添加选中标签状态
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showRoleEditor, setShowRoleEditor] = useState(false);
  const [currentRole, setCurrentRole] = useState<$RoleInfo | null>(null);

  // 更新Chip.Group的onChange处理函数
  const handleTagsChange = (newTags: string[]) => {
    setSelectedTags(newTags);
    // 只向后台发送图片标签更新
    try {
      if (scriptInfo?.strScriptId) {
        modifyTopic(emEditType.EM_EDIT_TOPIC_MODIFY_TAG, {
          stReferInfo: {
            vecUseChoose: newTags,
          },
        });
      }
    } catch (error) {
      console.error('更新图片标签失败', error);
    }
  };

  // 添加useEffect以在组件挂载和scriptInfo变化时初始化数据
  useEffect(() => {
    // 当scriptInfo加载完成后初始化数据
    console.log('[初始化数据] scriptInfo:', scriptInfo?.stTopic?.stReferInfo);

    if (scriptInfo?.stTopic?.stReferInfo) {
      // 设置图片URL（如果有）
      if (scriptInfo.stTopic.stReferInfo.vecPicUrl && scriptInfo.stTopic.stReferInfo.vecPicUrl.length > 0) {
        console.log('[初始化图片URL]:', scriptInfo.stTopic.stReferInfo.vecPicUrl);
        setPreviewUrls(prevUrls => {
          console.log('[设置前的previewUrls]:', prevUrls);
          const newUrls = [...scriptInfo.stTopic.stReferInfo.vecPicUrl];
          console.log('[设置后的previewUrls]:', newUrls);
          return newUrls;
        });
      }

      // 设置图片标签（如果有）
      if (scriptInfo.stTopic.stReferInfo.vecTag && scriptInfo.stTopic.stReferInfo.vecTag.length > 0) {
        console.log('[初始化图片标签]:', scriptInfo.stTopic.stReferInfo.vecTag);
        setImageTags(scriptInfo.stTopic.stReferInfo.vecTag);
      }

      // 设置用户选择的标签（如果有）
      if (scriptInfo.stTopic.stReferInfo.vecUseChoose && scriptInfo.stTopic.stReferInfo.vecUseChoose.length > 0) {
        console.log('[初始化选中标签]:', scriptInfo.stTopic.stReferInfo.vecUseChoose);
        setSelectedTags(scriptInfo.stTopic.stReferInfo.vecUseChoose);
      }
    }
  }, [scriptInfo?.stTopic?.stReferInfo]); // 优化依赖，只在stReferInfo变化时执行

  // 添加一个单独的useEffect来监控previewUrls变化
  useEffect(() => {
    console.log('[previewUrls更新]:', previewUrls);
  }, [previewUrls]);

  useEffect(() => {
    if (scriptInfo?.stTopic) {
      setInputSummary(scriptInfo?.stTopic.strInspirationSummary);
    }
  }, [scriptInfo?.stTopic?.strInspirationSummary, setInputSummary]);

  console.log('[渲染时的previewUrls]:', previewUrls);

  if (!basicConfig?.vecVideoSize?.length) return null;
  // console.log(
  //   '[selectedMvTemplate]:',
  //   scriptInfo?.stTopic?.stMVTemplate,
  //   basicConfig.vecMvTpl,
  //   selectedMvQuality?.strId,
  //   scriptInfo?.stTopic?.stSize?.sizeName,
  //   hasSummaryUnsavedChanges
  // );

  // 处理图片上传并解析
  // const handleImageUpload = async (files: File[] | null) => {
  //   console.log('[handleImageUpload开始]:', files, '当前previewUrls:', previewUrls);

  //   if (!files || files.length === 0) return;
  //   if (!scriptInfo?.strScriptId) {
  //     notifications.show({
  //       title: '上传失败',
  //       message: '请先选择歌曲并确定主题',
  //       color: 'red',
  //       autoClose: 5000,
  //     });
  //     return;
  //   }

  //   // 验证所有文件都是图片
  //   for (const file of files) {
  //     if (!file.type.startsWith('image/')) {
  //       notifications.show({
  //         title: '上传失败',
  //         message: '请只上传图片文件',
  //         color: 'red',
  //         autoClose: 5000,
  //       });
  //       return;
  //     }
  //   }

  //   setUploadLoading(true);

  //   // 清空现有图片URL和标签
  //   setPreviewUrls([]);
  //   setImageTags([]);
  //   setSelectedTags([]);

  //   // const uploadedImageUrls: string[] = []; // 将在这里收集URL

  //   try {
  //     // 并行上传图片
  //     const uploadPromises = files.map(file => {
  //       const formData = new FormData();
  //       formData.append('file', file); // 确认后端接收的字段名
  //       return req.post('/mv/UploadFile', formData, {
  //         headers: {
  //           'Content-Type': 'multipart/form-data',
  //         },
  //       });
  //     });

  //     const results = await Promise.allSettled(uploadPromises);
  //     const successfullyUploadedUrls: string[] = [];

  //     results.forEach((result, index) => {
  //       console.log('[result]:', result);

  //       if (result.status === 'fulfilled' && result.value.data.data?.url) {
  //         successfullyUploadedUrls.push(result.value.data.data.url);
  //       } else {
  //         const fileName = files[index]?.name || '未知文件';
  //         const errorMessage = result.status === 'rejected' ? result.reason?.message : '上传后未返回URL';
  //         console.error(
  //           `图片 ${fileName} 上传失败`,
  //           result.status === 'rejected' ? result.reason : 'No URL in response'
  //         );
  //         notifications.show({
  //           title: '单张图片上传失败',
  //           message: `图片 ${fileName} 上传失败: ${errorMessage}`,
  //           color: 'red',
  //           autoClose: 5000,
  //         });
  //       }
  //     });

  //     if (successfullyUploadedUrls.length > 0) {
  //       console.log('[设置新的previewUrls from server]:', successfullyUploadedUrls);
  //       setPreviewUrls(successfullyUploadedUrls);
  //       // 处理上传的所有图片进行标签解析
  //       analyzeImageUrls(successfullyUploadedUrls);
  //     } else {
  //       // 如果没有一张图片成功上传
  //       setUploadLoading(false);
  //     }
  //   } catch (error) {
  //     console.error('图片上传或解析过程中发生错误', error);
  //     notifications.show({
  //       title: '操作失败',
  //       message: '图片上传或解析过程中发生错误',
  //       color: 'red',
  //       autoClose: 5000,
  //     });
  //     setUploadLoading(false); // 确保在任何错误后都停止加载状态
  //   }
  //   // setUploadLoading(false) 会在 analyzeImageUrls 内部的 finally 中调用
  // };

  // 上传并分析多张图片
  // const analyzeImageUrls = async (imageUrls: string[]) => {
  //   try {
  //     // 发送图片解析请求
  //     const response = await req.post('/lens_script/picture_analyze', {
  //       strScriptId: scriptInfo.strScriptId,
  //       vecPic: imageUrls, // 发送图片URL数组
  //     });

  //     console.log('图片解析结果:', response);

  //     if (response.data.error_code === 0) {
  //       const tags = response.data.data.vecTag || [];
  //       setImageTags(tags);
  //     } else {
  //       notifications.show({
  //         title: '图片解析失败',
  //         message: response.data.error_msg || '请稍后重试',
  //         color: 'red',
  //         autoClose: 5000,
  //       });
  //     }
  //   } catch (error) {
  //     console.error('图片解析请求失败', error);
  //     notifications.show({
  //       title: '图片解析失败',
  //       message: '网络请求错误，请稍后重试',
  //       color: 'red',
  //       autoClose: 5000,
  //     });
  //   } finally {
  //     setUploadLoading(false); // 确保在这里停止加载状态
  //   }
  // };

  // 删除单张图片
  // const handleRemoveImage = (index: number) => {
  //   const newPreviewUrls = [...previewUrls];
  //   // const newUploadedImages = [...uploadedImages]; // 移除

  //   // URL现在是服务器URL，不需要revokeObjectURL
  //   // URL.revokeObjectURL(newPreviewUrls[index]);

  //   // 删除对应索引的图片
  //   newPreviewUrls.splice(index, 1);
  //   // newUploadedImages.splice(index, 1); // 移除

  //   setPreviewUrls(newPreviewUrls);
  //   // setUploadedImages(newUploadedImages); // 移除

  //   // 如果所有图片都被删除了，清空标签
  //   if (newPreviewUrls.length === 0) {
  //     setImageTags([]);
  //     setSelectedTags([]);
  //     // 可以选择在这里通知后端图片列表已清空（如果需要）
  //     if (scriptInfo?.strScriptId) {
  //       modifyTopic(emEditType.EM_EDIT_TOPIC_MODIFY_TAG, {
  //         stReferInfo: {
  //           vecUseChoose: [], // 清空已选标签
  //           vecPicUrl: [], // 清空图片URL
  //         },
  //       });
  //     }
  //   }
  // };

  // 在角色部分添加处理角色选择的函数
  const handleToggleRoles = (selectedRoleIds: string[]) => {
    (async () => {
      try {
        // console.log('[选择角色] selectedRoleIds:', selectedRoleIds);
        // 调用角色选择API
        const response = await req.post('/lens_script/edit_role', {
          strScriptId: scriptInfo?.strScriptId,
          vectorEditField: [emEditType.EM_EDIT_ROLE_TYPE_CHOOSE_ROLE_LIST],
          vecRole: selectedRoleIds.map(roleId => ({ strId: roleId })),
        });

        if (response.data.error_code !== 0) {
          throw new Error(response.data.error_msg || '选择角色失败');
        }

        // 刷新角色列表数据
        modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_INSPRATION, {}); // 触发刷新
      } catch (error: any) {
        console.error('选择角色失败', error);
        notifications.show({
          title: '选择角色失败',
          message: error.message || '请稍后重试',
          color: 'red',
          autoClose: 5000,
        });
      }
    })();
  };

  return (
    <Stack gap="xl" mt="md">
      {/* 故事主题和情绪 */}
      <Box>
        <Group gap="xs" justify="space-between">
          <Text size="sm" fw={500} mb="md" mt="md">
            MV创意描述 <span className={styles.grayTip}>根据歌词释义以及图片解析自动生成</span>
          </Text>
          <Tooltip label="点击重新生成故事主题">
            <ActionIcon color="#3C485B" onClick={() => handleDetermineTopic(emEditType.EM_EDIT_TOPIC_TYPE_REGEN)}>
              <IconRotate style={{ rotate: '145deg' }} size={16} />
            </ActionIcon>
          </Tooltip>
        </Group>
        {/* 选择MV剧本类型 */}
        {/* {scriptInfo?.stTopic?.stMVTemplate?.strId && (
          <SegSelect
            data={basicConfig.vecMvTpl}
            type="mv"
            value={scriptInfo?.stTopic?.stMVTemplate?.strId || ''}
            onChange={value => {
              // 保存之前的选择状态（用于出错回退）
              const previousScriptInfo = scriptInfo ? { ...scriptInfo } : null;

              // 找到选中的模板
              const newMvTemplate = structuredClone(
                basicConfig.vecMvTpl.find((item: MVTemplateItem) => item.strId === value)
              );

              // 确保只保留一个品质选项
              if (newMvTemplate?.vecInfo.length) {
                newMvTemplate.vecInfo = [newMvTemplate.vecInfo[0]];
              }

              // 立即更新UI状态（乐观更新）
              if (scriptInfo && scriptInfo.stTopic && newMvTemplate) {
                setScriptInfo({
                  ...scriptInfo,
                  stTopic: {
                    ...scriptInfo.stTopic,
                    stMVTemplate: newMvTemplate,
                  },
                });
              }

              // 更新质量选择状态
              setSelectedMvQuality(newMvTemplate?.vecInfo[0]);

              // 发送更新请求
              modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_MVTEMPLATE, {
                stMVTemplate: newMvTemplate,
              })
                .then(() => {
                  // 更新成功，无需额外操作
                })
                .catch(error => {
                  // 请求失败时回退到之前的选择
                  console.error('更新MV类型失败', error);
                  setScriptInfo(previousScriptInfo);
                  notifications.show({
                    title: '更新MV类型失败',
                    message: '请稍后重试',
                    color: 'red',
                    autoClose: 5000,
                  });
                });
            }}
          />
        )} */}

        {isFailed ? (
          <Button
            size="sm"
            c="red"
            fw={500}
            mb="md"
            mt="md"
            onClick={() => {
              handleDetermineTopic();
            }}>
            故事主题和情绪生成失败，点击重试
          </Button>
        ) : (
          <div style={{ position: 'relative' }}>
            <LoadingOverlay
              visible={isFlowLoading && !scriptInfo?.stTopic?.strInspirationSummary}
              overlayProps={{ radius: 'sm', blur: 2 }}
              zIndex={100}
            />
            <EditableTextarea
              ref={textareaRef}
              value={inputSummary}
              onChange={value => {
                console.log('正在编辑灵感摘要:', value);
                setInputSummary(value);
              }}
              onSave={value => {
                console.log('[onSave]:', value);
                if (value.trim()) {
                  setInputSummary(value);
                  modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_INSPRATION, {
                    strInspirationSummary: value,
                  }).catch(error => {
                    console.error('更新灵感摘要失败', error);
                    notifications.show({
                      title: '更新灵感摘要失败',
                      message: '请稍后重试',
                      color: 'red',
                      autoClose: 5000,
                    });
                  });
                  setHasSummaryUnsavedChanges(false);
                }
              }}
              onIsDirtyChange={isDirty => {
                console.log('onIsDirtyChange', isDirty);
                setHasSummaryUnsavedChanges(isDirty);
              }}
              resetDirtyStateAfterSave={true}
              autosize
              minRows={3}
              required={true}
            />
            <Group justify="flex-end">
              <Button
                size="xs"
                mt="sm"
                disabled={!inputSummary?.trim() || !hasSummaryUnsavedChanges}
                onClick={() => {
                  // 这里无需执行任何逻辑，因为点击该按钮会触发onSave，无需重复保存。
                }}>
                {hasSummaryUnsavedChanges ? '保存故事主题和情绪 *' : '保存故事主题和情绪'}
              </Button>
            </Group>

            {/* 上传图片解析风格 */}
          </div>
        )}
      </Box>

      {/* 画面风格选择 */}
      <Box>
        <Text size="sm" fw={500} mb="md">
          画面风格 <span className={styles.grayTip}>后续角色和场景都将根据这个风格生成，保持画面统一</span>
        </Text>
        <ScrollArea
          offsetScrollbars={'x'}
          w="100%"
          classNames={{ root: styles.picModWrap, viewport: styles.picModViewport }}>
          <LoadingOverlay visible={!basicConfig.vecPicMod?.length} overlayProps={{ radius: 'sm', blur: 2 }} />
          <Group wrap="nowrap">
            {/* 从选中的MV模板中获取画面风格 */}
            {basicConfig.vecPicMod?.map((style: $PictureModel) => {
              // 检查当前风格是否被选中
              const isSelected = scriptInfo?.stTopic?.stPicModel?.strId === style.strId;

              return (
                <BackgroundImage
                  key={style.strId}
                  src={style.strReferPic}
                  radius="sm"
                  h={90}
                  w={120}
                  className={`${styles.picMod} ${isSelected ? styles.selected : ''}`}
                  onClick={() => {
                    // 如果选中的画面风格和当前的画面风格相同，则不进行修改
                    if (scriptInfo?.stTopic?.stPicModel?.strId === style.strId) {
                      return;
                    }

                    // 显示确认弹框
                    confirm({
                      title: '切换画面风格',
                      description: '切换画面风格后，MV创意描述会重新生成，是否确认修改？',
                      confirmLabel: '确认修改',
                      cancelLabel: '取消',
                      onConfirm: () => {
                        // 保存之前的选择状态（用于出错回退）
                        const previousPicModel = scriptInfo?.stTopic?.stPicModel
                          ? { ...scriptInfo.stTopic.stPicModel }
                          : null;

                        // 立即更新UI状态
                        setScriptInfo(
                          scriptInfo
                            ? {
                                ...scriptInfo,
                                stTopic: {
                                  ...scriptInfo.stTopic,
                                  stPicModel: style,
                                },
                              }
                            : null
                        );

                        modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_PICMODEL, {
                          stPicModel: style,
                        }).catch(error => {
                          // 请求失败时回退到之前的选择
                          console.error('更新画面风格失败', error);

                          if (previousPicModel) {
                            // 回退UI状态
                            setScriptInfo(
                              scriptInfo
                                ? {
                                    ...scriptInfo,
                                    stTopic: {
                                      ...scriptInfo.stTopic,
                                      stPicModel: previousPicModel,
                                    },
                                  }
                                : null
                            );
                          }

                          notifications.show({
                            title: '更新画面风格失败',
                            message: '请稍后重试',
                            color: 'red',
                            autoClose: 5000,
                          });
                        });
                      },
                    });
                  }}>
                  <Text size="sm" ta="center" className={styles.picModText}>
                    {style.strModel}
                  </Text>
                </BackgroundImage>
              );
            })}
          </Group>
        </ScrollArea>
      </Box>

      {/* 角色 */}
      <Box>
        <Group justify="space-between">
          <Text size="sm" fw={500} mb="md">
            角色
          </Text>
        </Group>
        <Box style={{ position: 'relative' }}>
          <LoadingOverlay visible={isFlowLoading} overlayProps={{ radius: 'sm', blur: 2 }} />
          {scriptInfo?.vecRoleInfo && (
            <RoleSelect
              roles={scriptInfo.vecRoleInfo}
              onAddRole={() => {
                setCurrentRole(null);
                setShowRoleEditor(true);
              }}
              onEditRole={(role: $RoleInfo) => {
                setCurrentRole(role);
                setShowRoleEditor(true);
              }}
              onToggleRoles={handleToggleRoles}
              confirm={confirm}
              onRoleDeleted={() => {
                modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_INSPRATION, {});
              }}
            />
          )}
        </Box>
        {showRoleEditor && (
          <EditRole
            initialData={currentRole}
            onClose={() => {
              setShowRoleEditor(false);
              setCurrentRole(null);
            }}
          />
        )}
      </Box>

      {/* MV尺寸选择 */}
      <Box>
        <Text size="sm" fw={500} mb="md">
          选择MV尺寸
        </Text>
        <SegSelect
          data={basicConfig.vecVideoSize}
          type="size"
          value={scriptInfo?.stTopic?.stSize?.sizeName || ''}
          onChange={value => {
            // 保存之前的选择状态（用于出错回退）
            const previousScriptInfo = scriptInfo ? { ...scriptInfo } : null;

            // 构建新的MV尺寸对象
            const newMvSize = {
              sizeName: value,
              lWidth: basicConfig.vecVideoSize.find((item: VideoSizeItem) => item.sizeName === value)?.lWidth || 0,
              lHeight: basicConfig.vecVideoSize.find((item: VideoSizeItem) => item.sizeName === value)?.lHeight || 0,
              stResolution:
                basicConfig.vecVideoSize.find((item: VideoSizeItem) => item.sizeName === value)?.stResolution ||
                emResolution.EM_VIDEO_720P_H,
            };

            // 立即更新UI状态（乐观更新）
            setScriptInfo(
              scriptInfo
                ? {
                    ...scriptInfo,
                    stTopic: {
                      ...scriptInfo.stTopic,
                      stSize: newMvSize,
                    },
                  }
                : null
            );

            // 使用新构建的对象发送请求
            modifyTopic(emEditType.EM_EDIT_TOPIC_TYPE_MVSIZE, {
              stSize: newMvSize,
            }).catch(error => {
              // 请求失败时回退到之前的选择
              console.error('更新MV尺寸失败', error);
              setScriptInfo(previousScriptInfo);
              notifications.show({
                title: '更新MV尺寸失败',
                message: '请稍后重试',
                color: 'red',
                autoClose: 5000,
              });
            });
          }}
        />
      </Box>

      {/* mv品质，品质在picmodel中 */}
      {scriptInfo?.stTopic?.stPicModel?.strId && basicConfig.vecPicMod?.length && (
        <Box pos="relative">
          <Text size="sm" fw={500} mb="md">
            选择MV品质
          </Text>

          <SegSelect
            data={
              basicConfig.vecPicMod.find((pic: $PictureModel) => pic.strId === scriptInfo?.stTopic?.stPicModel?.strId)
                ?.vecInfo || []
            }
            type="quality"
            value={selectedMvQuality?.strId || scriptInfo?.stTopic?.stPicModel?.vecInfo[0]?.strId || ''}
            onChange={value => {
              // 找到用户选择的品质项
              const qualityItem = basicConfig.vecPicMod
                .find((pic: $PictureModel) => pic.strId === scriptInfo?.stTopic?.stPicModel?.strId)
                ?.vecInfo.find((qua: $MVQuality) => qua.strId === value);
              console.log('[qualityItem]:', qualityItem, value);

              // 保存之前的选择状态（用于出错回退）
              const previousScriptInfo = scriptInfo ? { ...scriptInfo } : null;
              const previousQuality = selectedMvQuality;

              // 立即更新UI状态
              setSelectedMvQuality(qualityItem);

              // 构建新的对象
              const newPicModel = {
                ...scriptInfo?.stTopic?.stPicModel,
                vecInfo: [qualityItem],
              };

              // 更新scriptInfo
              if (scriptInfo && scriptInfo.stTopic && newPicModel && qualityItem) {
                setScriptInfo({
                  ...scriptInfo,
                  stTopic: {
                    ...scriptInfo.stTopic,
                    stPicModel: newPicModel,
                  },
                });
              }

              // 调用后台接口
              modifyTopic(emEditType.EM_EDIT_TOPIC_MODIFY_QUALITY, {
                stPicModel: newPicModel,
              }).catch(error => {
                // 接口调用失败时回退到之前的选择
                console.error('更新MV品质失败', error);
                setScriptInfo(previousScriptInfo);
                setSelectedMvQuality(previousQuality);
                notifications.show({
                  title: '更新MV品质失败',
                  message: '请稍后重试',
                  color: 'red',
                  autoClose: 5000,
                });
              });
            }}
          />
        </Box>
      )}
    </Stack>
  );
};

export default BasicConfig;
