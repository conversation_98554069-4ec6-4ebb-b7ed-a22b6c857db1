import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Textarea, TextareaProps } from '@mantine/core';
import { useEditableField } from '../../hooks/useEditableField';

export interface EditableTextareaProps extends Omit<TextareaProps, 'value' | 'onChange'> {
  /** 文本框的值 */
  value: string;
  /** 当内容修改且有必要保存时的回调函数 */
  onSave?: (value: string) => void;
  /** 是否启用防抖，默认为true */
  debounce?: boolean;
  /** 防抖时间（毫秒），默认为300ms */
  debounceTime?: number;
  /** 自定义比较函数，用于判断值是否变化 */
  equalityFn?: (prev: string, next: string) => boolean;
  /** 当内容变化时的回调函数（可选，如果需要在父组件中同步状态） */
  onChange?: (value: string) => void;
  /** 状态变化回调，当isDirty状态变化时调用 */
  onIsDirtyChange?: (isDirty: boolean) => void;
  /** 是否在保存后重置脏状态（将当前值视为新的基准值） */
  resetDirtyStateAfterSave?: boolean;
}

/**
 * 可编辑文本区域组件，自动处理内容变更检测和保存逻辑
 */
export const EditableTextarea = forwardRef<{ updateOriginalValue: (value: string) => void }, EditableTextareaProps>(
  (
    {
      value: initialValue,
      onSave,
      debounce = true,
      debounceTime = 300,
      equalityFn,
      onChange: externalOnChange,
      onFocus,
      onBlur,
      required = false,
      onIsDirtyChange,
      resetDirtyStateAfterSave = true,
      ...props
    },
    ref
  ) => {
    // 添加错误状态
    const [error, setError] = useState<string | null>(null);

    // 跟踪初始值的变化
    useEffect(() => {
      // 当外部传入的值变化时，重置错误状态
      setError(null);
    }, [initialValue]);

    // 使用useEditableField处理状态和焦点/失焦事件
    const { value, handleFocus, handleChange, handleBlur, isDirty, reset, updateOriginalValue } = useEditableField({
      initialValue,
      onSave: newValue => {
        console.log('[EditableTextarea] onSave被调用，值:', newValue, '是否脏:', isDirty);

        // 如果是必填项且值为空，不触发保存
        if (required && (!newValue || !String(newValue).trim())) {
          console.log('[EditableTextarea] 必填项不能为空，不触发保存');
          return;
        }

        if (onSave) {
          onSave(newValue);

          // 在保存成功后，将当前值设置为新的原始值
          if (resetDirtyStateAfterSave) {
            console.log('[EditableTextarea] 保存后重置脏状态，值:', newValue);
            updateOriginalValue(newValue);
          }
        }
      },
      equalityFn,
      debounce,
      debounceTime,
    });

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      updateOriginalValue: (value: string) => {
        console.log('[EditableTextarea] 外部调用updateOriginalValue，值:', value);
        updateOriginalValue(value);
      },
    }));

    // 当isDirty状态变化时通知父组件
    useEffect(() => {
      if (onIsDirtyChange) {
        onIsDirtyChange(isDirty);
      }
    }, [isDirty, onIsDirtyChange]);

    // 处理值为空的检查函数
    const handleEmptyCheck = (value: string): boolean => {
      if (required && (!value || !value.trim())) {
        setError('此字段不能为空');
        return true;
      }
      return false;
    };

    return (
      <Textarea
        {...props}
        value={value}
        error={error}
        required={required}
        onFocus={e => {
          console.log('[EditableTextarea] 获得焦点，当前值:', value);
          // 清除错误状态
          setError(null);
          handleFocus();
          if (onFocus) {
            onFocus(e);
          }
        }}
        onChange={e => {
          const newValue = e.currentTarget.value;

          // 如果正在输入时值变为空，可以立即显示错误提示，但不阻止输入
          if (required && !newValue.trim()) {
            setError('此字段不能为空');
          } else {
            setError(null);
          }

          handleChange(newValue);
          if (externalOnChange) {
            externalOnChange(newValue);
          }
        }}
        onBlur={e => {
          console.log('[EditableTextarea] 失去焦点，当前值:', e.currentTarget.value);

          // 如果是必填项且值为空，显示错误信息并恢复原始值
          if (handleEmptyCheck(e.currentTarget.value)) {
            console.log('[EditableTextarea] 恢复初始值:', initialValue);
            // 恢复到原始值
            reset();
            if (onBlur) {
              onBlur(e);
            }
            return;
          }

          // 正常情况下处理失焦事件
          handleBlur();
          if (onBlur) {
            onBlur(e);
          }
        }}
      />
    );
  }
);

// 添加displayName
EditableTextarea.displayName = 'EditableTextarea';

export default EditableTextarea;
