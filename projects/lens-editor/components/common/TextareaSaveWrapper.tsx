import React, { useState, useImperativeHandle, forwardRef } from 'react';
import { Textarea, TextareaProps } from '@mantine/core';
import { useSaveableComponent } from '../../hooks/useSaveableComponent';

export interface TextareaSaveWrapperProps extends Omit<TextareaProps, 'value' | 'onChange'> {
  /** 文本框的值 */
  value: string;
  /** 当内容修改且有必要保存时的回调函数 */
  onSave?: (value: string) => Promise<void>;
  /** 当内容变化时的回调函数（可选，如果需要在父组件中同步状态） */
  onChange?: (value: string) => void;
  id?: string; // 可选的自定义ID
}

export interface TextareaSaveWrapperRef {
  save: () => Promise<void>;
  isDirty: () => boolean;
  reset: () => void;
}

/**
 * Textarea组件的包装器，集成到可保存组件系统中
 */
export const TextareaSaveWrapper = forwardRef<TextareaSaveWrapperRef, TextareaSaveWrapperProps>(
  ({ value: initialValue, onSave = async () => {}, onChange, id, ...props }, ref) => {
    // 使用可保存组件Hook
    const { value, setValue, save, isDirty, reset, componentId } = useSaveableComponent({
      initialValue,
      onSave: async newValue => {
        console.log(`[TextareaSaveWrapper] 准备保存值:`, newValue);
        await onSave(newValue);
      },
      componentType: 'TextareaSaveWrapper',
      id: id || `TextareaSaveWrapper-${Math.random().toString(36).substring(2, 9)}`,
    });

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        save,
        isDirty,
        reset,
      }),
      [save, isDirty, reset]
    );

    return (
      <Textarea
        {...props}
        value={value}
        onChange={e => {
          const newValue = e.currentTarget.value;
          setValue(newValue);
          if (onChange) onChange(newValue);
        }}
      />
    );
  }
);

// 添加这一行来设置 displayName
TextareaSaveWrapper.displayName = 'TextareaSaveWrapper';

export default TextareaSaveWrapper;
