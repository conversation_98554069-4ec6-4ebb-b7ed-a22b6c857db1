import React, { useState } from 'react';
import { TextInput, Textarea, Box, Text, Button, TextInputProps, TextareaProps } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import req from 'common/request';
import appcss from 'projects/lens-editor/App.less';

// 定义组件接口
export interface AIInputFieldProps {
  // 通用属性
  type: 'input' | 'textarea'; // 输入框类型
  value: string;
  onChange: (value: string) => void;
  apiPath: string; // API路径
  apiField: number; // API调用的field值
  apiParams: Record<string, any>; // API调用的参数
  apiResponseHandler?: (response: any) => string; // 自定义响应处理函数

  // TextInput或Textarea的属性
  inputProps?: TextInputProps;
  textareaProps?: TextareaProps;

  // AI按钮相关
  aiButtonText?: string;

  // 禁用状态
  disabled?: boolean;
  maxLength?: number;

  // 表单验证
  onValidation?: () => boolean; // 验证函数，返回true表示验证通过，false表示验证失败
}

export const AIInputField: React.FC<AIInputFieldProps> = ({
  type,
  value,
  onChange,
  apiPath,
  apiField,
  apiParams,
  apiResponseHandler,
  inputProps = {},
  textareaProps = {},
  aiButtonText = 'AI文案',
  disabled = false,
  maxLength = 500,
  onValidation,
}) => {
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);

  // 处理AI文案生成
  const handleGenerateAI = () => {
    if (isGeneratingAI || disabled) return;

    // 如果提供了验证函数，先进行表单验证
    if (onValidation && !onValidation()) {
      return;
    }

    setIsGeneratingAI(true);
    req
      .post(apiPath, {
        vectorEditField: [apiField],
        ...apiParams,
      })
      .then(response => {
        console.log('[AI response]:', response);

        if (response.data.error_code !== 0 || response.data.data.iRes !== 0) {
          notifications.show({
            title: '获取AI文案失败',
            message: response.data.error_msg || response.data.data.strTips,
            color: 'red',
          });
          return;
        }

        // 处理响应并更新值
        const newValue = apiResponseHandler
          ? apiResponseHandler(response.data.data)
          : response.data.data.storyboard?.strStory ||
            response.data.data.scene?.strSceneTitle ||
            response.data.data.stRole?.strDesc ||
            '';

        onChange(newValue);
      })
      .catch(error => {
        notifications.show({
          title: '网络异常，获取AI文案失败',
          message: error.message,
          color: 'red',
        });
      })
      .finally(() => {
        setIsGeneratingAI(false);
      });
  };

  // 根据类型渲染不同的输入框
  return (
    <Box style={{ position: 'relative' }}>
      {type === 'input' ? (
        <TextInput
          value={value}
          className={appcss.aiTxtInput}
          onChange={e => onChange(e.currentTarget.value)}
          disabled={disabled}
          maxLength={maxLength}
          {...inputProps}
        />
      ) : (
        <Textarea
          value={value}
          className={appcss.aiTxtInput}
          style={{
            paddingBottom: '36px',
          }}
          onChange={e => onChange(e.target.value)}
          disabled={disabled}
          maxLength={maxLength}
          {...textareaProps}
        />
      )}
      <Text size="xs" c="dimmed" className={appcss.aiTxtLoadCount}>
        {value.length}/{maxLength}
      </Text>
      <Button
        size="xs"
        variant="lite"
        className={appcss.aiTxtLoadBtn}
        onClick={handleGenerateAI}
        style={{ cursor: isGeneratingAI || disabled ? 'not-allowed' : 'pointer' }}
        c={isGeneratingAI ? 'dimmed' : ''}>
        <span className={appcss.aiTxtLoadIcon + ' ' + (isGeneratingAI ? appcss.aiTxtLoadIconIng : '')}>
          {/* 生成中动画 */}
        </span>
        {aiButtonText}
      </Button>
    </Box>
  );
};

export default AIInputField;
