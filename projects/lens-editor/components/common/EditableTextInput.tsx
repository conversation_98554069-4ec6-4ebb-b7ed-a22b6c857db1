import React from 'react';
import { TextInput, TextInputProps } from '@mantine/core';
import { useEditableField } from '../../hooks/useEditableField';

export interface EditableTextInputProps extends Omit<TextInputProps, 'value' | 'onChange'> {
  /** 文本框的值 */
  value: string;
  /** 当内容修改且有必要保存时的回调函数 */
  onSave: (value: string) => void;
  /** 是否启用防抖，默认为false */
  debounce?: boolean;
  /** 防抖时间（毫秒），默认为300ms */
  debounceTime?: number;
  /** 自定义比较函数，用于判断值是否变化 */
  equalityFn?: (prev: string, next: string) => boolean;
  /** 当内容变化时的回调函数（可选，如果需要在父组件中同步状态） */
  onChange?: (value: string) => void;
}

/**
 * 可编辑文本输入组件，自动处理内容变更检测和保存逻辑
 */
export const EditableTextInput: React.FC<EditableTextInputProps> = ({
  value: initialValue,
  onSave,
  debounce = false,
  debounceTime = 300,
  equalityFn,
  onChange: externalOnChange,
  onFocus,
  onBlur,
  ...props
}) => {
  const { value, handleFocus, handleChange, handleBlur, isDirty } = useEditableField({
    initialValue,
    onSave,
    equalityFn,
    debounce,
    debounceTime,
  });

  return (
    <TextInput
      {...props}
      value={value}
      onFocus={e => {
        handleFocus();
        onFocus?.(e);
      }}
      onChange={e => {
        const newValue = e.currentTarget.value;
        handleChange(newValue);
        if (externalOnChange) {
          externalOnChange(newValue);
        }
      }}
      onBlur={e => {
        handleBlur();
        onBlur?.(e);
      }}
    />
  );
};

export default EditableTextInput;
