import React, { useState, useEffect } from 'react';
import { Mo<PERSON>, Stack, Title, Button, Group, Textarea } from '@mantine/core';
import { ModelSelect } from './modalSelect';
import { CameraOptions } from './cameraOptions';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import {
  $VideoInfo,
  eVideoModelType,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { $StoryboardInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';

interface VideoGenerationModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: (config: VideoGenerationConfig) => void;
  mode: 'single' | 'batch'; // 单个视频生成还是批量生成
  selectedStoryboard?: $StoryboardInfo | null; // 单个生成时的分镜
}

export interface VideoGenerationConfig {
  modelId: string;
  movementId?: string;
  videoTips?: string;
}

export const VideoGenerationModal: React.FC<VideoGenerationModalProps> = ({
  opened,
  onClose,
  onConfirm,
  mode,
  selectedStoryboard,
}) => {
  const { basicConfig } = useEditorStore();

  // 状态管理
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  const [selectedMovementId, setSelectedMovementId] = useState<string>('');
  const [videoTips, setVideoTips] = useState<string>('');
  const [isMovementType, setIsMovementType] = useState<boolean>(false);
  const [showPrompt, setShowPrompt] = useState<boolean>(false);

  // 初始化默认值
  useEffect(() => {
    if (opened) {
      if (mode === 'single' && selectedStoryboard?.stCurVideo) {
        // 单个生成时，使用当前分镜的视频配置
        const currentVideo = selectedStoryboard.stCurVideo;
        setSelectedModelId(currentVideo.strVideoModelId || '');
        setSelectedMovementId(currentVideo.strMovementId || '');
        setVideoTips(currentVideo.strVideoTips || '');
      } else {
        // 批量生成时，使用默认配置
        const defaultModel = basicConfig?.vecViMod?.[0];
        if (defaultModel) {
          setSelectedModelId(defaultModel.strId);
          setIsMovementType(defaultModel.eType === eVideoModelType.EM_VIDEO_MODEL_TYPE_MOVEMENT);
          setShowPrompt(!!defaultModel.iNeedTips);
        }
        setSelectedMovementId('');
        setVideoTips('');
      }
    }
  }, [opened, mode, selectedStoryboard, basicConfig]);

  // 模型变化时更新相关状态
  useEffect(() => {
    if (selectedModelId && basicConfig?.vecViMod) {
      const selectedModel = basicConfig.vecViMod.find(model => model.strId === selectedModelId);
      if (selectedModel) {
        setIsMovementType(selectedModel.eType === eVideoModelType.EM_VIDEO_MODEL_TYPE_MOVEMENT);
        setShowPrompt(!!selectedModel.iNeedTips);
      }
    }
  }, [selectedModelId, basicConfig]);

  const handleModelChange = (value: string | null) => {
    if (value) {
      setSelectedModelId(value);
    }
  };

  const handleCameraChange = (cameraId: string) => {
    setSelectedMovementId(cameraId);
  };

  const handleConfirm = () => {
    const config: VideoGenerationConfig = {
      modelId: selectedModelId,
      movementId: isMovementType ? selectedMovementId : undefined,
      videoTips: showPrompt ? videoTips : undefined,
    };
    onConfirm(config);
  };

  const handleClose = () => {
    // 重置状态
    setSelectedModelId('');
    setSelectedMovementId('');
    setVideoTips('');
    setIsMovementType(false);
    setShowPrompt(false);
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={mode === 'single' ? '视频生成配置' : '批量视频生成配置'}
      size="md"
      centered>
      <Stack gap="md">
        <Title order={4} size="sm">
          视频模型
        </Title>
        <ModelSelect type="video" value={selectedModelId} onChange={handleModelChange} />

        {/* 只有运镜类型才显示运镜选项 */}
        {isMovementType && (
          <>
            <Title order={4} size="sm">
              运镜方式
            </Title>
            <CameraOptions
              data={{ strMovementId: selectedMovementId } as $VideoInfo}
              onCameraChange={handleCameraChange}
            />
          </>
        )}

        {/* 显示提示词输入 */}
        {showPrompt && (
          <>
            <Title order={4} size="sm">
              视频描述提示词
            </Title>
            <Textarea
              placeholder="请输入视频描述提示词"
              minRows={3}
              maxRows={6}
              autosize
              value={videoTips}
              onChange={e => setVideoTips(e.currentTarget.value)}
            />
          </>
        )}

        <Group justify="flex-end" mt="md">
          <Button variant="light" onClick={handleClose}>
            取消
          </Button>
          <Button onClick={handleConfirm} disabled={!selectedModelId}>
            {mode === 'single' ? '生成视频' : '批量生成视频'}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};
