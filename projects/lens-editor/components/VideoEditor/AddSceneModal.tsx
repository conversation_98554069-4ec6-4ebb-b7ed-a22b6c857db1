import React, { useState, useEffect } from 'react';
import { Modal, Button, Group, Text } from '@mantine/core';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import { emEditType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import AIInputField from '../common/AIInputField';

interface AddSceneModalProps {
  opened: boolean;
  onClose: () => void;
  onSubmit: (sceneTitle: string) => void | Promise<void>;
  isLoading?: boolean;
}

const MAX_TITLE_LENGTH = 20; // 场景标题最大长度

export const AddSceneModal: React.FC<AddSceneModalProps> = ({ opened, onClose, onSubmit, isLoading }) => {
  const [sceneTitle, setSceneTitle] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { scriptId } = useEditorStore();

  const handleClose = () => {
    setSceneTitle('');
    setError(null);
    onClose();
  };

  const handleSubmit = () => {
    if (!sceneTitle.trim()) {
      setError('场景标题不能为空');
      return;
    }
    if (sceneTitle.length > MAX_TITLE_LENGTH) {
      setError(`场景标题不能超过 ${MAX_TITLE_LENGTH} 个字符`);
      return;
    }
    setError(null);
    onSubmit(sceneTitle);
  };

  // 在弹窗打开时清空状态，确保每次打开都是初始状态
  useEffect(() => {
    if (opened) {
      setSceneTitle('');
      setError(null);
    }
  }, [opened]);

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="新建场景"
      centered
      overlayProps={{
        backgroundOpacity: 0.55,
        blur: 3,
      }}
      closeOnClickOutside={!isLoading} // 防止在加载时意外关闭
      withCloseButton={!isLoading} // 加载时隐藏关闭按钮
    >
      <AIInputField
        type="input"
        value={sceneTitle}
        onChange={value => {
          setSceneTitle(value);
          if (error) setError(null); // 用户开始输入时清除错误
        }}
        apiPath="/lens_script/edit_scene"
        apiField={emEditType.EM_EDIT_SCEN_TYPE_GEN_TITLE}
        apiParams={{
          strScriptId: scriptId,
          vecScene: [{}], // 空对象，由后端生成场景标题
        }}
        disabled={isLoading}
        inputProps={{
          label: '场景标题',
          placeholder: '请填写你的描述',
          error,
          autoFocus: true,
          required: true,
          mb: 'md',
        }}
      />

      <Group justify="flex-end">
        <Button variant="default" onClick={handleClose} disabled={isLoading}>
          取消
        </Button>
        <Button onClick={handleSubmit} loading={isLoading}>
          确认创建
        </Button>
      </Group>
    </Modal>
  );
};
