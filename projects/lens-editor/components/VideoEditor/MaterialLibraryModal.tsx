import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  Group,
  Text,
  Box,
  Image,
  LoadingOverlay,
  ScrollArea,
  SimpleGrid,
  TextInput,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import { IconEye, IconSearch } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import {
  $MaterialItem,
  $GetMaterialListRsp,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { $StoryboardInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { notifications } from '@mantine/notifications';
import req from 'common/request';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import videoStyles from './videoEditor.module.less';
import { StoryboardOperationHandler } from './types';
import { emEditType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';

interface MaterialLibraryModalProps {
  opened: boolean;
  onClose: () => void;
  selectedStoryboard: $StoryboardInfo | null;
  handleStoryboardOperation: StoryboardOperationHandler;
}

export const MaterialLibraryModal: React.FC<MaterialLibraryModalProps> = ({
  opened,
  onClose,
  selectedStoryboard,
  handleStoryboardOperation,
}) => {
  const { scriptId } = useEditorStore();
  const [searchTerm, setSearchTerm] = useState(selectedStoryboard?.strStory);
  const [isLoading, setIsLoading] = useState(false);
  const [materials, setMaterials] = useState<$MaterialItem[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<$MaterialItem | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [previewImageOpened, { open: openPreviewImage, close: closePreviewImage }] = useDisclosure(false);

  // 搜索素材库
  const searchMaterials = () => {
    if (!searchTerm?.trim()) {
      notifications.show({
        title: '请输入搜索关键词',
        message: '请输入关键词以搜索素材',
        color: 'red',
      });
      return;
    }

    setIsLoading(true);
    setHasSearched(true);

    req
      .post('/lens_script/get_material_list', {
        strScriptId: scriptId,
        strPicTips: searchTerm,
        strPicModelId: selectedStoryboard?.stCurPic?.stUseModelId || '',
      })
      .then((response: { data: { error_code: number; data: $GetMaterialListRsp; error_msg: string } }) => {
        console.log('[material search results]:', response);
        if (response.data.error_code === 0 && response.data.data.vecInfo) {
          setMaterials(response.data.data.vecInfo);
        } else {
          notifications.show({
            title: '获取素材失败',
            message: response.data.error_msg || '未找到相关素材',
            color: 'red',
          });
        }
      })
      .catch(error => {
        notifications.show({
          title: '获取素材失败',
          message: error.message,
          color: 'red',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // 选择素材
  const handleSelectMaterial = (material: $MaterialItem) => {
    if (selectedMaterial?.materialID === material.materialID) {
      setSelectedMaterial(null);
    } else {
      setSelectedMaterial(material);
    }
  };

  // 处理图片预览
  const handleImageClickForPreview = (imageUrl: string) => {
    // 移除图片URL中的缩略图参数，以显示原图
    const originalImageUrl = imageUrl.split('?')[0];
    setImagePreviewUrl(originalImageUrl);
    openPreviewImage();
  };

  // 应用所选素材到分镜
  const applyMaterial = () => {
    if (!selectedMaterial || !selectedStoryboard) {
      notifications.show({
        title: '请先选择素材',
        message: '请选择一个素材应用到分镜中',
        color: 'red',
      });
      return;
    }

    // 使用操作处理函数修改分镜
    handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_USE_MATERIAL, selectedStoryboard, selectedMaterial);

    // 关闭弹窗
    onClose();

    notifications.show({
      title: '已应用素材',
      message: '素材已成功应用到当前分镜',
      color: 'green',
    });
  };

  // 按回车键搜索
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      searchMaterials();
    }
  };

  return (
    <>
      <Modal opened={opened} onClose={onClose} title="素材库" size="xl" centered>
        <LoadingOverlay visible={isLoading} />
        <Box mb="md">
          <Group>
            <TextInput
              placeholder="输入关键词搜索素材"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              onKeyPress={handleKeyPress}
              style={{ flex: 1 }}
            />
            <Button size="xs" leftSection={<IconSearch size={16} />} onClick={searchMaterials}>
              搜索
            </Button>
          </Group>
        </Box>

        {/* 素材显示区域 */}
        {materials.length > 0 ? (
          <ScrollArea h={400}>
            <SimpleGrid cols={3} spacing="md" verticalSpacing="md">
              {materials.map((material, index) => (
                <Box
                  key={index}
                  data-id={material.stPicInfo?.strId}
                  className={`${videoStyles.imglistItem} ${
                    material.materialID === selectedMaterial?.materialID ? videoStyles.selected : ''
                  }`}>
                  <Image
                    onClick={() => handleSelectMaterial(material)}
                    style={{ borderRadius: '4px', cursor: 'pointer' }}
                    src={material.stPicInfo?.strPicUrl + '?imageMogr2/format/webp|imageMogr2/thumbnail/!50p'}
                    alt={`素材 ${index + 1}`}
                  />
                  <ActionIcon
                    className={videoStyles.previewIcon}
                    onClick={() => handleImageClickForPreview(material.stPicInfo?.strPicUrl)}>
                    <IconEye size={16} />
                  </ActionIcon>
                </Box>
              ))}
            </SimpleGrid>
          </ScrollArea>
        ) : (
          <Text c="dimmed" ta="center" py="xl">
            {isLoading ? '正在加载素材...' : hasSearched ? '没有找到匹配的素材' : '请输入描述查找素材'}
          </Text>
        )}

        {/* 操作按钮 */}
        <Group justify="flex-end" mt="xl">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            取消
          </Button>
          <Button onClick={applyMaterial} disabled={!selectedMaterial || isLoading}>
            应用素材
          </Button>
        </Group>
      </Modal>

      {/* 图片预览 Modal */}
      <Modal opened={previewImageOpened} onClose={closePreviewImage} size="80%" centered>
        {imagePreviewUrl && <Image w={'90%'} mx="auto" src={imagePreviewUrl} />}
      </Modal>
    </>
  );
};

export default MaterialLibraryModal;
