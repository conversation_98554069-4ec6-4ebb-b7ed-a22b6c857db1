import React from 'react';
import { Modal, Box, Grid, Text, Image } from '@mantine/core';
import { $RoleInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { defaultImg } from 'common/utils';
import { IconCheck } from '@tabler/icons-react';
import styles from './scene.module.less';
import appStyles from 'projects/lens-editor/App.less';

interface SelectRoleProps {
  onClose: () => void;
  container: {
    vecRoleInfo?: $RoleInfo[];
    selectedRoleIds?: string[];
  };
  roles: $RoleInfo[];
  onClick: (container: any, roleId: string, isAdd: boolean) => void;
}

const SelectRole: React.FC<SelectRoleProps> = ({ onClose, roles, container, onClick }) => {
  return (
    <Modal
      opened={true}
      onClose={onClose}
      title={`选择角色`}
      centered
      overlayProps={{
        backgroundOpacity: 0.55,
        blur: 8,
      }}
      size="lg">
      <Box>
        {roles.length > 0 ? (
          <Grid>
            {roles.map((role, idx) => {
              // 检查角色是否已在选中列表中，兼容两种情况
              const isSelected =
                container?.vecRoleInfo?.some(sceneRole => sceneRole.strId === role.strId) ||
                container?.selectedRoleIds?.includes(role.strId) ||
                false;

              return (
                <Grid.Col
                  span={3}
                  key={idx}
                  className={styles.roleCard}
                  onClick={() => {
                    onClick(container, role.strId, !isSelected);
                  }}>
                  <Image radius="md" src={role.strUrl || defaultImg} alt={role.strName} />
                  <Text ta="center" size="sm" mt="xs">
                    {role.strName}
                  </Text>
                  {isSelected && (
                    <IconCheck className={appStyles.checkIcon} color="var(--green-text-color)" size={26} />
                  )}
                </Grid.Col>
              );
            })}
          </Grid>
        ) : (
          <Text ta="center" c="dimmed">
            暂无角色，请先添加角色
          </Text>
        )}
      </Box>
    </Modal>
  );
};

export default SelectRole;
