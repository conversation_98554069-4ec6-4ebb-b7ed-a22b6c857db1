// 视频编辑页面
import { Flex, Paper, Title, ScrollArea, Button, Box, LoadingOverlay, Tabs, Center } from '@mantine/core';
import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { AVCanvas } from '@webav/av-canvas';
import { TimelineState } from '@xzdarcy/react-timeline-editor';
import { useEditorStore, selectIsFlowLoading, selectAllStoryboards } from 'projects/lens-editor/store/editorStore';
import { useTimelineStore } from '../../store/timelineStore';
import {
  $VideoInfo,
  $SubtitleItem,
  eVideoModelType,
  $SceneInfo,
  emStatus,
  $StoryboardInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { emEditType, emScriptGetMask } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import req from 'common/request';
import { notifications } from '@mantine/notifications';
import { VIDEOHEIGHT, VIDEOWIDTH } from 'projects/lens-editor/constants';
import { TimelineEditor } from './TimelineEditor';
import { SelectionState, TLActionWithName, StoryboardOperationHandler } from './types';
import classes from './videoEditor.module.less';
import appcss from '../../App.less';
import AddBoardModal from './AddBoardModal';
import LeftSide from './LeftSide';
import { StoryboardTab } from './StoryboardTab';
import { VideoTab } from './VideoTab';
import { useVideoProcessing } from '../../hooks/useVideoProcessing';
import { LyricEditor } from './LyricEditor';
import { VideoGenerationModal } from './VideoGenerationModal';
import { useSmartTimelineUpdate } from '../../hooks/useSmartTimelineUpdate';
import { useStoryboardNavigation } from '../../hooks/useStoryboardNavigation';
import { useVideoGeneration } from '../../hooks/useVideoGeneration';
import { useTimelineBusinessLogic } from '../../hooks/useTimelineBusinessLogic';

export default function VideoEditor({ fetchStoryboardProcess }: { fetchStoryboardProcess: () => void }) {
  const isFlowLoading = useEditorStore(selectIsFlowLoading);
  const { scriptInfo, scriptId, basicConfig, isFailed, startPolling, setButtonLoading } = useEditorStore();

  const {
    timelineData,
    setActiveAction,
    setAVCanvas,
    setTLState,
    setPrevScriptInfo,
    actionSpriteMap,
    avCanvas,
    forceUpdateKey,
  } = useTimelineStore();

  const [historyVideo, setHistoryVideo] = React.useState<$VideoInfo[]>([]); // 每一个分镜视频，包含当前视频和历史视频，所以历史视频是根据当前用户选中的视频来展示的。可以根据VecNewStoryboard.strId来判断归属。
  const allStoryboards = useEditorStore(selectAllStoryboards);
  const [playing, setPlaying] = useState(false);
  const [cvsWrapEl, setCvsWrapEl] = useState<HTMLDivElement | null>(null);
  const [videoTipsInput, setVideoTipsInput] = useState<string>(''); // 视频提示词输入框的值
  const tlState = useRef<TimelineState>(); // 时间轴控制器

  const [activeTab, setActiveTab] = useState<string>('storyboard'); // 添加Tab状态，默认为分镜绘制
  const [userManuallyChangedTab, setUserManuallyChangedTab] = useState(false); // 记录用户是否手动切换过tab

  // 统一的当前选择状态
  const [selectionState, setSelectionState] = useState<SelectionState>({
    selectedStoryboard: null,
    activeTimelineAction: null,
  });

  // 从选择状态派生的计算属性
  const selectedScene = useMemo(() => {
    return selectionState.selectedStoryboard?.strSceneId
      ? (scriptInfo?.vecNewScene || []).find(scene => scene.strId === selectionState.selectedStoryboard?.strSceneId) ||
          null
      : null;
  }, [selectionState.selectedStoryboard?.strSceneId, scriptInfo?.vecNewScene]);

  const selectedMedia = useMemo(() => {
    return selectionState.selectedStoryboard?.stCurVideo || null;
  }, [
    selectionState.selectedStoryboard?.stCurVideo?.strId,
    selectionState.selectedStoryboard?.stCurVideo?.eStatus,
    selectionState.selectedStoryboard?.stCurVideo?.strVideoModelId,
    selectionState.selectedStoryboard?.stCurVideo?.strMovementId,
    selectionState.selectedStoryboard?.stCurVideo?.strVideoTips,
  ]);

  const mediaType = useMemo(() => {
    return selectedMedia ? 'video' : null;
  }, [selectedMedia]);

  // 在组件顶部添加一个ref
  const scriptInfoRef = useRef(scriptInfo);
  const storyboardRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const isMounted = useRef(true);
  const prevStoryboardsLengthRef = useRef<number>(0); // 添加跟踪上一次分镜数量的引用

  const [isAddBoardModalOpen, setIsAddBoardModalOpen] = useState(false);
  const [addBoardStoryboard, setAddBoardStoryboard] = useState<$StoryboardInfo | null>(null);

  // 默认展开状态 - 场景
  const scenes = useMemo(() => scriptInfo?.vecNewScene || [], [scriptInfo?.vecNewScene]);
  const [expandedScenes, setExpandedScenes] = useState<Record<string, boolean>>({});

  // 添加一个标记正在处理的状态变量
  const [isMovementType, setIsMovementType] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isModelChanged, setIsModelChanged] = useState(false); //模型修改后不立即保存，需要加个标记

  // 添加歌词编辑相关状态
  const [isLyricEditorOpen, setIsLyricEditorOpen] = useState(false);
  const [currentEditingAction, setCurrentEditingAction] = useState<TLActionWithName | null>(null);
  const [currentEditingSubtitleItem, setCurrentEditingSubtitleItem] = useState<$SubtitleItem | null>(null);

  // 初始化视频处理hook - 使用全局配置
  const { regenerateVideo, generateAllVideos } = useVideoProcessing();

  // 按需更新时间轴，避免全量重新渲染 - 使用全局配置
  const { updateSingleStoryboard, forceUpdateTimeline } = useSmartTimelineUpdate();

  // 更新选择状态的函数 - 统一管理选择状态的更新
  const updateSelectionState = useCallback(
    (updates: Partial<SelectionState> & { _isRecursiveCall?: boolean } = {}) => {
      // 防止无限递归的标记
      const isRecursiveCall = updates._isRecursiveCall;
      const cleanUpdates = { ...updates } as Partial<SelectionState>;
      delete (cleanUpdates as any)._isRecursiveCall;

      setSelectionState(prev => ({
        ...prev,
        ...cleanUpdates,
      }));

      // 防止递归调用时重复执行副作用
      if (isRecursiveCall) {
        return;
      }

      // 如果选中的分镜发生变化，更新相关状态
      if (updates.selectedStoryboard && updates.selectedStoryboard !== selectionState.selectedStoryboard) {
        const storyboard = updates.selectedStoryboard;
        const video = storyboard.stCurVideo;

        // 根据当前视频状态设置相关参数
        if (video) {
          // 设置视频提示词
          setVideoTipsInput(video.strVideoTips || '');

          // 根据模型类型设置运镜和提示词显示
          const currentModelId = video.strVideoModelId || '';
          const currentModel = basicConfig?.vecViMod?.find(model => model.strId === currentModelId);
          setIsMovementType(currentModel?.eType === eVideoModelType.EM_VIDEO_MODEL_TYPE_MOVEMENT);
          setShowPrompt(!!currentModel?.iNeedTips);
        }

        // 更新历史视频
        if (storyboard.vecVideo && storyboard.vecVideo.length > 0) {
          setHistoryVideo(storyboard.vecVideo);
        } else {
          setHistoryVideo([]);
        }

        // 处理时间轴联动
        const videoTrack = timelineData.find(track => track.id === '3-video');
        if (videoTrack && videoTrack.actions) {
          const videoAction = videoTrack.actions.find(action => action.id === storyboard.strId);

          if (videoAction) {
            // 如果找到对应的视频action，更新activeTimelineAction
            setActiveAction(videoAction);

            // 直接更新状态，避免递归调用
            setSelectionState(prev => ({
              ...prev,
              activeTimelineAction: videoAction,
            }));

            // 将时间轨道定位到该视频开始位置
            if (avCanvas && tlState.current) {
              const previewTime = videoAction.start * 1e6 + 1; // 转换为微秒
              avCanvas.previewFrame(previewTime);
              tlState.current.setTime(videoAction.start);
            }
          }
        }
      }
    },
    [
      selectionState,
      setVideoTipsInput,
      basicConfig,
      setIsMovementType,
      setShowPrompt,
      setHistoryVideo,
      timelineData,
      setActiveAction,
      avCanvas,
      tlState,
    ]
  );

  // 键盘导航功能
  useStoryboardNavigation({
    allStoryboards,
    selectionState,
    updateSelectionState,
    setExpandedScenes,
    storyboardRefs,
  });

  // 视频生成功能
  const {
    isVideoGenerationModalOpen,
    videoGenerationMode,
    pendingVideoGeneration,
    regenerateVideoHandler,
    regenerateVideoDialog,
    handleVideoGenerationConfirm,
    handleVideoGenerationModalClose,
  } = useVideoGeneration({
    scriptId,
    scriptInfo,
    selectionState,
    selectedMedia,
    videoTipsInput,
    updateSelectionState,
    updateSingleStoryboard,
    regenerateVideo,
    startPolling,
    scriptInfoRef,
  });

  const handleRegeneratePicture = async (strId?: string) => {
    const { selectedStoryboard } = selectionState;

    if (!selectedStoryboard && !strId) {
      notifications.show({
        title: '提示',
        message: '请先选择要重绘的分镜',
        color: 'orange',
      });
      return;
    }

    try {
      if (isMounted.current) {
        setButtonLoading(true);
      }

      const targetStoryboard = strId
        ? allStoryboards.find(storyboard => storyboard.strId === strId)
        : selectedStoryboard;

      const response = await req.post('/lens_script/edit_storyboard', {
        strScriptId: scriptId,
        vectorEditField: [emEditType.EM_EDIT_STORYBOARD_TYPE_REGEN_PIC],
        stBoardInfo: targetStoryboard ? [targetStoryboard] : [],
      });

      if (response.data.error_code !== 0) {
        notifications.show({
          title: '生成图片失败',
          message: response.data.error_msg,
          color: 'red',
        });
        setButtonLoading(false);
        return;
      }

      // 确保组件仍然挂载
      if (isMounted.current) {
        notifications.show({
          title: '生成图片成功',
          message: response.data.data.strTips,
          color: 'green',
        });
        setIsModelChanged(false);

        // 创建一个安全的轮询回调
        const safePollingCallback = () => {
          if (isMounted.current) {
            setButtonLoading(false);
          }
        };

        // 使用安全的回调函数
        startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_STORYBOARD, safePollingCallback, undefined, 'picture');
      }
    } catch (error) {
      // 确保组件仍然挂载
      if (isMounted.current) {
        notifications.show({
          title: '重绘画面失败',
          message: error instanceof Error ? error.message : '重绘画面失败，请稍后重试',
          color: 'red',
        });
        setButtonLoading(false);
      }
    }
  };

  // 分镜相关操作
  const handleStoryboardOperation: StoryboardOperationHandler = async (operationType, storyboard, stMaterial) => {
    try {
      // 适配处理参数，确保与API请求结构一致
      const storyboardArray = Array.isArray(storyboard) ? storyboard : [storyboard];

      const response = await req.post('/lens_script/edit_storyboard', {
        strScriptId: scriptId,
        vectorEditField: [operationType],
        stBoardInfo: storyboardArray,
        stMaterial,
      });

      if (response.data.error_code !== 0 || response.data.data.iRes !== 0) {
        notifications.show({
          title: '修改分镜失败',
          message: response.data.error_msg || response.data.data.strTips,
          color: 'red',
        });
        return;
      }

      // 刷新数据
      fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, () => {
        console.log('[fetchScriptInfoOnce success]:');
      });

      notifications.show({
        message: '操作成功',
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '操作失败，请稍后重试',
        color: 'red',
      });
    }
  };

  // 时间轴业务逻辑
  const { handleTimelineOffsetChange, handleTimelineDurationChange } = useTimelineBusinessLogic();

  // 获取最新脚本信息
  const fetchScriptInfoOnce = (mask: emScriptGetMask, callback?: () => void) => {
    startPolling(
      mask,
      () => {
        // 执行原始回调
        if (callback) callback();

        // 检查分镜图是否全部加载完成
        if (allStoryboards.length) {
          const allImagesReady = allStoryboards.every(
            storyboard =>
              storyboard.stCurPic &&
              storyboard.stCurPic.strPicUrl &&
              storyboard.stCurPic.eStatus === emStatus.EM_STATUS_SUCC
          );

          // 如果分镜图已经准备好，强制重新加载时间轴
          if (allImagesReady && avCanvas) {
            console.log('[分镜图全部准备完成，强制刷新时间轴]');

            // 重置，以更新时间轴
            setPrevScriptInfo(null);
          }
        }
      },
      undefined,
      'picture'
    );
  };

  // 播放暂停处理函数
  const handlePlayPause = () => {
    if (avCanvas == null || tlState.current == null) return;
    if (playing) {
      avCanvas.pause();
    } else {
      // 获取当前时间并播放
      const currentTime = tlState.current.getTime() * 1e6;

      // 检查是否到达了最后一个视频的结尾
      const videoTrack = timelineData.find(track => track.id === '3-video');
      if (videoTrack && videoTrack.actions.length > 0) {
        const lastVideo = videoTrack.actions[videoTrack.actions.length - 1];
        const lastVideoEndTime = lastVideo.end * 1e6;
        console.log('[currentTime,lastVideoEndTime]:', currentTime, lastVideoEndTime, lastVideo, videoTrack.actions);

        // 如果当前时间超过了最后一个视频的结束时间，从头开始播放
        if (currentTime >= lastVideoEndTime) {
          avCanvas.play({ start: 0 });
        } else {
          avCanvas.play({ start: currentTime });
        }
      } else {
        avCanvas.play({ start: currentTime });
      }
    }
  };

  // 预览时间位置的处理函数
  const handlePreviewTime = (time: number) => {
    if (avCanvas) {
      // 将时间转换为微秒并设置预览帧
      const timeInMicroseconds = time * 1e6;
      avCanvas.previewFrame(timeInMicroseconds);

      // 更新时间轴控制组件的时间
      if (tlState.current) {
        tlState.current.setTime(time);
      }

      // 查找当前时间点对应的视频
      const videoTrack = timelineData.find(track => track.id === '3-video');
      if (videoTrack) {
        // 查找当前时间所处的视频片段
        const currentVideoAction = videoTrack.actions.find(action => time >= action.start && time <= action.end);

        // 如果找到了对应的视频，设置为选中状态并显示其历史记录
        if (currentVideoAction) {
          // 避免重复设置相同的视频，减少不必要的渲染
          if (
            !selectionState.activeTimelineAction ||
            selectionState.activeTimelineAction.id !== currentVideoAction.id
          ) {
            console.log('[找到视频，设置选中]:', currentVideoAction.id, '时间点:', time);

            // 设置活动的时间轴动作
            setActiveAction(currentVideoAction);

            // 找到对应的分镜信息
            const storyboard = allStoryboards.find(sb => sb.strId === currentVideoAction.id);

            if (storyboard && storyboard.stCurVideo) {
              // 更新选择状态
              updateSelectionState({
                selectedStoryboard: storyboard,
                activeTimelineAction: currentVideoAction,
              });

              // 如果是通过视频轨道或时间轴预览触发的，需要同步左侧分镜选中状态
              const shouldUpdateStoryboard =
                !selectionState.selectedStoryboard || selectionState.selectedStoryboard.strId !== storyboard.strId;

              if (shouldUpdateStoryboard) {
                // 展开对应的场景
                if (storyboard.strSceneId) {
                  setExpandedScenes(prevState => ({
                    ...prevState,
                    [storyboard.strSceneId]: true,
                  }));
                }

                // 滚动到视图
                setTimeout(() => {
                  const storyboardElement = storyboardRefs.current[storyboard.strId];
                  if (storyboardElement) {
                    storyboardElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                  }
                }, 100);
              }
            }
          }
        }
      }
    }
  };

  // 打开歌词编辑器的函数
  const handleOpenLyricEditor = (action: TLActionWithName) => {
    if (action.type === 'subtitle' && scriptInfo?.stSubtitle) {
      const subtitleTrack = scriptInfo.stSubtitle.vecContent.find(track =>
        track.vecContent?.some(item => item.strId === action.id)
      );
      if (subtitleTrack) {
        const subtitleItem = subtitleTrack.vecContent?.find(item => item.strId === action.id);
        if (subtitleItem) {
          setCurrentEditingAction(action);
          setCurrentEditingSubtitleItem(subtitleItem);
          setIsLyricEditorOpen(true);
        }
      }
    }
  };

  // 关闭歌词编辑器的函数
  const handleCloseLyricEditor = () => {
    setIsLyricEditorOpen(false);
    setCurrentEditingAction(null);
    setCurrentEditingSubtitleItem(null);
  };

  // 保存歌词的函数
  const handleSaveLyricEditor = async (newContent: string) => {
    if (!currentEditingAction || !currentEditingSubtitleItem || !scriptInfo?.stSubtitle) {
      notifications.show({
        title: '错误',
        message: '无法保存歌词，缺少必要信息。',
        color: 'red',
      });
      return;
    }

    const subtitleTrack = scriptInfo.stSubtitle.vecContent.find(track =>
      track.vecContent?.some(item => item.strId === currentEditingSubtitleItem.strId)
    );

    if (!subtitleTrack) {
      notifications.show({
        title: '错误',
        message: '找不到歌词所在的轨道。',
        color: 'red',
      });
      return;
    }

    const updatedSubtitleItem: $SubtitleItem = {
      ...currentEditingSubtitleItem,
      strContent: newContent,
    };

    try {
      const response = await req.post('/lens_script/edit_text', {
        strScriptId: scriptId,
        strTrackId: subtitleTrack.strId, // 使用找到的轨道ID
        stInfo: updatedSubtitleItem,
      });

      if (response.data.error_code !== 0 || response.data.data.iRes !== 0) {
        notifications.show({
          title: '保存歌词失败',
          message: response.data.error_msg || response.data.data.strTips || '请稍后重试',
          color: 'red',
        });
      } else {
        notifications.show({
          title: '成功',
          message: '歌词已保存',
          color: 'green',
        });

        // 先更新 scriptInfo 数据
        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, () => {
          // 数据更新完成后，强制刷新字幕轨道
          console.log('[handleSaveLyricEditor] 歌词保存成功，开始强制刷新字幕轨道');

          // 使用 setTimeout 确保 scriptInfo 数据已经更新到 store 中
          setTimeout(() => {
            const { updateSubtitleTrack, forceUpdate } = useTimelineStore.getState();
            const latestScriptInfo = useEditorStore.getState().scriptInfo;

            if (latestScriptInfo?.stSubtitle) {
              console.log('[handleSaveLyricEditor] 强制更新字幕轨道');
              updateSubtitleTrack(latestScriptInfo.stSubtitle)
                .then(() => {
                  console.log('[handleSaveLyricEditor] 字幕轨道更新完成');
                  // 强制触发时间轴重新渲染
                  forceUpdate();
                })
                .catch(error => {
                  console.error('[handleSaveLyricEditor] 字幕轨道更新失败:', error);
                });
            }
          }, 200); // 增加延迟时间，确保数据完全更新
        });

        handleCloseLyricEditor();
      }
    } catch (error) {
      console.error('保存歌词失败:', error);
      notifications.show({
        title: '保存歌词失败',
        message: error instanceof Error ? error.message : '请稍后重试',
        color: 'red',
      });
    }
  };

  // Effects ------------------------------------------------------------

  // Initialize AVCanvas
  useEffect(() => {
    if (cvsWrapEl == null) return;

    // 先销毁旧实例
    if (avCanvas) {
      avCanvas.destroy();
    }

    // 创建新实例
    const element = cvsWrapEl as HTMLElement;
    const cvs = new AVCanvas(element, {
      bgColor: '#000',
      width: VIDEOWIDTH,
      height: VIDEOHEIGHT,
    });

    // 直接设置到 store
    setAVCanvas(cvs);

    // 添加事件监听
    cvs.on('timeupdate', time => {
      if (tlState.current == null) return;
      tlState.current.setTime(time / 1e6);
    });

    cvs.on('playing', () => {
      setPlaying(true);
    });

    cvs.on('paused', () => {
      setPlaying(false);
    });

    return () => {
      cvs.destroy();
      setAVCanvas(null);
    };
  }, [cvsWrapEl, setAVCanvas]);

  // 初始化时，如果没有选中的分镜，自动选择第一个分镜
  useEffect(() => {
    // console.log('[setSelectedVideo scriptInfo]:', scriptInfo, avCanvas, timelineData, isFlowLoading, selectionState);
    if (
      !avCanvas ||
      !timelineData.length ||
      !allStoryboards.length ||
      selectionState.selectedStoryboard ||
      isFlowLoading
    )
      return;

    const videoTrack = timelineData.find(track => track.id === '3-video');
    if (!videoTrack || !videoTrack.actions.length) return;

    // 选中第一个分镜
    const firstStoryboard = allStoryboards[0];
    console.log('[setSelectedVideo  选中第一个分镜]', firstStoryboard);

    updateSelectionState({
      selectedStoryboard: firstStoryboard,
    });
  }, [isFlowLoading, timelineData, avCanvas, allStoryboards]);

  // 分镜数量变化时，强制刷新时间轴
  useEffect(() => {
    if (!isFlowLoading && allStoryboards.length) {
      // 只有当分镜数量真正变化时才更新时间轴
      if (prevStoryboardsLengthRef.current !== allStoryboards.length) {
        console.log(
          '[分镜数量变化时，强制刷新时间轴]',
          '前：',
          prevStoryboardsLengthRef.current,
          '后：',
          allStoryboards.length
        );
        forceUpdateTimeline();
        // 更新记录的分镜数量
        prevStoryboardsLengthRef.current = allStoryboards.length;
      }
    }
  }, [allStoryboards.length, isFlowLoading, forceUpdateTimeline]);

  useEffect(() => {
    setTLState(tlState);
  }, [tlState, setTLState]);

  // 当选中视频变化时，更新右边栏
  useEffect(() => {
    console.log('[VIDEO EDITOR DEBUG] selectedMedia变化触发:', {
      selectedMedia: selectedMedia?.strId,
      mediaType,
      时间戳: new Date().toISOString(),
    });

    if (selectedMedia && mediaType === 'video') {
      const video = selectedMedia as $VideoInfo;
      if (video.strVideoModelId) {
        setIsModelChanged(false);
        setVideoTipsInput(video.strVideoTips || '');

        // 查找包含该视频的分镜
        const storyboardWithVideo = allStoryboards.find(
          sb => sb.vecVideo?.some(v => v.strId === video.strId) || sb.stCurVideo?.strId === video.strId
        );

        if (storyboardWithVideo?.vecVideo) {
          setHistoryVideo(storyboardWithVideo.vecVideo);
        } else {
          setHistoryVideo([]);
        }

        // 根据模型类型决定是否显示运镜和视频提示词
        // 获取当前选中的视频模型信息
        const currentModelId = video.strVideoModelId || '';
        const currentModel = basicConfig?.vecViMod?.find(model => model.strId === currentModelId);
        console.log('[VIDEO EDITOR DEBUG] 视频模型信息:', currentModel);
        setIsMovementType(currentModel?.eType === eVideoModelType.EM_VIDEO_MODEL_TYPE_MOVEMENT);
        setShowPrompt(!!currentModel?.iNeedTips);
      }
    }
  }, [
    selectedMedia?.strId, // 只依赖关键ID，而不是整个对象
    selectedMedia?.strVideoModelId, // 只依赖关键属性
    selectedMedia?.strVideoTips,
    mediaType,
    basicConfig?.vecViMod, // 只依赖需要的部分
    allStoryboards.length, // 只依赖数组长度，避免因轮询导致的引用变化
  ]);

  // 点击action时，变更当前选中的视频或分镜
  useEffect(() => {
    const { activeTimelineAction } = selectionState;
    if (!activeTimelineAction) return;

    console.log('[点击时间轴动作，更新选择状态]', activeTimelineAction, scriptInfo);

    if (activeTimelineAction.type === 'video') {
      // 查找对应的分镜
      const storyboard = allStoryboards.find(sb => sb.strId === activeTimelineAction.id);

      if (storyboard) {
        // 更新选择状态 - 只更新核心状态
        updateSelectionState({
          selectedStoryboard: storyboard,
        });

        // 展开对应的场景
        if (storyboard.strSceneId) {
          setExpandedScenes(prev => ({
            ...prev,
            [storyboard.strSceneId]: true,
          }));
        }

        // 滚动到对应的分镜
        // setTimeout(() => {
        //   const storyboardElement = storyboardRefs.current[storyboard.strId];
        //   if (storyboardElement) {
        //     storyboardElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        //   }
        // }, 100);
      }
    }
  }, [selectionState.activeTimelineAction, allStoryboards]);

  // 当选中分镜或视频变化时，自动切换到相应的Tab
  useEffect(() => {
    const { selectedStoryboard } = selectionState;

    if (selectedStoryboard) {
      // 检查分镜是否已经生成了视频且状态为成功
      const hasSuccessfulVideo =
        selectedStoryboard.stCurVideo &&
        selectedStoryboard.stCurVideo.strVideo &&
        selectedStoryboard.stCurVideo.eStatus === emStatus.EM_STATUS_SUCC;

      // 只有在用户没有手动切换过tab时才自动切换
      if (!userManuallyChangedTab) {
        if (hasSuccessfulVideo) {
          // 如果已经生成了视频，切换到视频绘制Tab
          setActiveTab('video');
        } else {
          // 如果没有生成视频，切换到分镜绘制Tab
          setActiveTab('storyboard');
        }
      }
    }
  }, [
    selectionState.selectedStoryboard?.strId,
    selectionState.selectedStoryboard?.stCurVideo?.eStatus,
    userManuallyChangedTab,
  ]);

  // 当选择不同分镜时，重置手动切换标记
  useEffect(() => {
    setUserManuallyChangedTab(false);
  }, [selectionState.selectedStoryboard?.strId]);

  useEffect(() => {
    if (scenes.length > 0) {
      // 将所有场景设置为展开状态
      const allExpanded = scenes.reduce(
        (acc: Record<string, boolean>, scene: $SceneInfo) => ({ ...acc, [scene.strId]: true }),
        {}
      );
      setExpandedScenes(allExpanded);
    }
  }, [scenes]); // 当场景数据更新时重新设置

  // 监听scriptInfo中分镜状态变化，更新当前选中的分镜
  useEffect(() => {
    scriptInfoRef.current = scriptInfo;
    const { selectedStoryboard } = selectionState;

    console.log('[VIDEO EDITOR DEBUG] scriptInfo变化触发:', {
      分镜数量: allStoryboards.length,
      选中分镜ID: selectedStoryboard?.strId,
      时间戳: new Date().toISOString(),
    });

    if (!scriptInfo || !selectedStoryboard) return;

    // 从最新的scriptInfo中查找当前选中的分镜
    const updatedStoryboard = allStoryboards.find(sb => sb.strId === selectedStoryboard.strId);

    if (updatedStoryboard) {
      // 检查视频状态是否变化
      const oldPicStatus = selectedStoryboard.stCurPic?.eStatus;
      const newPicStatus = updatedStoryboard.stCurPic?.eStatus;
      const oldVideoStatus = selectedStoryboard.stCurVideo?.eStatus;
      const newVideoStatus = updatedStoryboard.stCurVideo?.eStatus;

      // 只有在状态真正发生变化时才打印日志和更新
      const hasStatusChange = oldVideoStatus !== newVideoStatus || oldPicStatus !== newPicStatus;

      if (hasStatusChange) {
        console.log('[VIDEO EDITOR DEBUG] 检测到视频状态变化:', {
          分镜ID: selectedStoryboard.strId,
          视频状态: `${oldVideoStatus} -> ${newVideoStatus}`,
          图片状态: `${oldPicStatus} -> ${newPicStatus}`,
          有变化: true,
        });

        // 更新选择状态
        updateSelectionState({
          selectedStoryboard: updatedStoryboard,
        });
      }
    }
  }, [
    // 只依赖选中分镜的关键属性，避免整个对象引用变化
    selectionState.selectedStoryboard?.strId,
    selectionState.selectedStoryboard?.stCurVideo?.eStatus,
    selectionState.selectedStoryboard?.stCurPic?.eStatus,
    // 查找对应分镜的关键信息
    allStoryboards.find(sb => sb.strId === selectionState.selectedStoryboard?.strId)?.stCurVideo?.eStatus,
    allStoryboards.find(sb => sb.strId === selectionState.selectedStoryboard?.strId)?.stCurPic?.eStatus,
  ]);

  // 组件被卸载时，把prevScriptInfo清除
  useEffect(() => {
    return () => {
      setPrevScriptInfo(null);
    };
  }, [setPrevScriptInfo]);

  // 组件挂载/卸载检测
  useEffect(() => {
    isMounted.current = true;

    // 设置内存监控基准值
    if ((performance as any).memory) {
      (window as any).initialMemory = (performance as any).memory.usedJSHeapSize;
      console.log('[MEMORY DEBUG] 设置内存基准值:', Math.round((window as any).initialMemory / 1024 / 1024) + 'MB');
    }

    // 加载数据 - 组件挂载时获取最新数据
    fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, () => {
      console.log('成功获取脚本信息');
    });
    return () => {
      isMounted.current = false;
    };
  }, []);

  // 监听scriptId变化，当scriptId变化时重置时间轴并强制更新
  useEffect(() => {
    console.log('[VideoEditorIndex] scriptId变化:', scriptId);

    if (scriptId) {
      // 首先重置时间轴存储
      useTimelineStore.getState().resetTimelineStore();

      // 记录当前处理的scriptId，用于防止竞态条件
      const currentScriptId = scriptId;

      // 确保scriptInfo已经加载完成后再更新时间轴
      const waitAndUpdate = async () => {
        // 等待scriptInfo加载
        let retryCount = 0;
        const maxRetries = 10;

        while (retryCount < maxRetries) {
          // 检查当前scriptId是否仍然有效(防止用户快速切换多个作品)
          if (currentScriptId !== useEditorStore.getState().scriptId) {
            console.log('[VideoEditorIndex] scriptId已经改变，取消更新时间轴');
            return;
          }

          // 获取最新的scriptInfo
          const currentScriptInfo = useEditorStore.getState().scriptInfo;

          // 如果scriptInfo已加载且有分镜数据，则更新时间轴
          if (currentScriptInfo?.VecNewStoryboard?.length) {
            console.log('[VideoEditorIndex] scriptInfo已加载，更新时间轴', new Date().toISOString());
            // 使用延迟执行，确保avCanvas已经初始化
            setTimeout(() => {
              if (currentScriptId === useEditorStore.getState().scriptId) {
                forceUpdateTimeline();
              }
            }, 300);
            return;
          }

          // 等待一段时间后重试
          console.log(`[VideoEditorIndex] 等待scriptInfo加载，重试次数: ${retryCount + 1}`);
          await new Promise(resolve => setTimeout(resolve, 500));
          retryCount++;
        }

        console.log('[VideoEditorIndex] 等待scriptInfo超时，放弃更新时间轴');
      };

      // 执行等待和更新
      waitAndUpdate();
    }
  }, [scriptId, forceUpdateTimeline]);

  if (isFailed) {
    return (
      <Center>
        <Button
          variant="filled"
          color="red"
          mt="xl"
          onClick={() => {
            fetchStoryboardProcess();
          }}>
          分镜生成失败了，请点击重试
        </Button>
      </Center>
    );
  }

  return (
    <Box className={classes.videoEditor}>
      <LoadingOverlay visible={isFlowLoading} overlayProps={{ radius: 'sm', blur: 2 }} />
      {/* 上部分 - 三列布局 */}
      <Flex gap="md" className={classes.videoTopWrap}>
        {/* 左边栏 - 场景和分镜 */}
        <Paper className={classes.leftBar}>
          <LeftSide
            updateSelectionState={updateSelectionState}
            generateAllVideos={generateAllVideos}
            selectionState={selectionState}
            // 创建包装函数以匹配 LeftSide 的 prop 类型
            setAddBoardStoryboard={(storyboard: Partial<$StoryboardInfo>) =>
              setAddBoardStoryboard(storyboard as $StoryboardInfo)
            }
            setIsAddBoardModalOpen={setIsAddBoardModalOpen}
            storyboardRefs={storyboardRefs}
            handleRegeneratePicture={(strId?: string) => {
              handleRegeneratePicture(strId);
            }}
            handleStoryboardOperation={handleStoryboardOperation}
            isLoading={isFlowLoading}
            fetchScriptInfoOnce={fetchScriptInfoOnce}
            expandedScenes={expandedScenes}
            setExpandedScenes={setExpandedScenes}
          />
        </Paper>

        {/* 预览区域 */}
        <Paper className={classes.previewArea}>
          <Box style={{ width: '100%', background: '#000', borderRadius: '4px', overflow: 'hidden' }}>
            <div ref={el => setCvsWrapEl(el)} style={{ width: '100%', aspectRatio: '16/9' }}></div>
            <div className={classes.maskCover}></div>
          </Box>
          {/* 只有当前分镜还没生成视频时才展示按钮 */}
          {selectionState.selectedStoryboard &&
            (!selectionState.selectedStoryboard.stCurVideo ||
              !selectionState.selectedStoryboard.stCurVideo.strVideo ||
              selectionState.selectedStoryboard.stCurVideo.eStatus !== emStatus.EM_STATUS_SUCC) && (
              <Button
                variant="awsome"
                size="xs"
                className={classes.regenerateVideoButton}
                loading={selectionState.selectedStoryboard.stCurVideo?.eStatus === emStatus.EM_STATUS_RUNNING}
                onClick={() => {
                  regenerateVideoDialog();
                }}>
                <span className={classes.img2videoIcon}></span>图转视频
              </Button>
            )}
        </Paper>

        {/* 右边栏配置区 - 现在使用Tab切换 */}
        <Paper className={classes.rightBar} p="md">
          <ScrollArea h="100%" offsetScrollbars="y">
            {selectionState.selectedStoryboard ? (
              <Tabs
                className={appcss.mytab}
                value={activeTab}
                onChange={(value: string | null) => {
                  if (value) {
                    setActiveTab(value);
                    setUserManuallyChangedTab(true);
                  }
                }}>
                <Tabs.List>
                  <Tabs.Tab value="storyboard">分镜绘制</Tabs.Tab>
                  <Tabs.Tab value="video">视频绘制</Tabs.Tab>
                </Tabs.List>

                {/* 分镜绘制Tab内容 */}
                <Tabs.Panel value="storyboard" pt="xs">
                  {activeTab === 'storyboard' && (
                    <StoryboardTab
                      key={`storyboard-${selectionState.selectedStoryboard?.strId || 'none'}`}
                      selectionState={selectionState}
                      updateSelectionState={updateSelectionState}
                      allStoryboards={allStoryboards}
                      handleRegeneratePicture={() => {
                        handleRegeneratePicture();
                      }}
                      handleStoryboardOperation={handleStoryboardOperation}
                      fetchScriptInfoOnce={fetchScriptInfoOnce}
                    />
                  )}
                </Tabs.Panel>

                {/* 视频绘制Tab内容 */}
                <Tabs.Panel value="video" pt="xs">
                  {activeTab === 'video' && (
                    <VideoTab
                      key={`video-${selectionState.selectedStoryboard?.strId || 'none'}`}
                      selectionState={selectionState}
                      updateSelectionState={updateSelectionState}
                      historyVideo={historyVideo}
                      showPrompt={showPrompt}
                      setShowPrompt={setShowPrompt}
                      regenerateVideo={regenerateVideoHandler}
                      isModelChanged={isModelChanged}
                      setIsModelChanged={setIsModelChanged}
                      allStoryboards={allStoryboards}
                      setIsMovementType={setIsMovementType}
                      setVideoTipsInput={setVideoTipsInput}
                      videoTipsInput={videoTipsInput}
                      isMovementType={isMovementType}
                      handleStoryboardOperation={handleStoryboardOperation}
                      selectedMedia={selectedMedia}
                      mediaType={mediaType}
                    />
                  )}
                </Tabs.Panel>
              </Tabs>
            ) : (
              <Title order={3} mb="sm" size={'md'}>
                请先选择分镜
              </Title>
            )}
          </ScrollArea>
        </Paper>
      </Flex>

      {/* 下部分 - 时间轴 */}
      <Box className={classes.timelineEditor}>
        <TimelineEditor
          key={forceUpdateKey} // 添加key属性，当forceUpdateKey变化时强制重新渲染
          timelineData={timelineData}
          timelineState={tlState}
          onPreviewTime={handlePreviewTime}
          activeAction={selectionState.activeTimelineAction}
          setActiveAction={setActiveAction}
          playing={playing}
          onPlayPause={handlePlayPause}
          actionSpriteMap={actionSpriteMap}
          onOpenLyricEditor={handleOpenLyricEditor}
          onOffsetChange={handleTimelineOffsetChange}
          onDurationChange={handleTimelineDurationChange}
        />
      </Box>

      {/* 添加分镜弹窗 */}
      {addBoardStoryboard && selectedScene && (
        <AddBoardModal
          opened={isAddBoardModalOpen}
          onClose={() => setIsAddBoardModalOpen(false)}
          storyboard={addBoardStoryboard}
          scene={selectedScene}
          isLoading={isFlowLoading}
          handleStoryboardOperation={handleStoryboardOperation}
        />
      )}

      {/* 歌词编辑弹窗 */}
      {currentEditingSubtitleItem && (
        <LyricEditor
          opened={isLyricEditorOpen}
          onClose={handleCloseLyricEditor}
          action={currentEditingAction}
          subtitleItem={currentEditingSubtitleItem}
          onSave={ctn => {
            void handleSaveLyricEditor(ctn);
          }}
        />
      )}

      {/* 视频生成配置弹窗 */}
      <VideoGenerationModal
        opened={isVideoGenerationModalOpen}
        onClose={handleVideoGenerationModalClose}
        onConfirm={handleVideoGenerationConfirm}
        mode={videoGenerationMode}
        selectedStoryboard={pendingVideoGeneration?.storyboard}
      />
    </Box>
  );
}
