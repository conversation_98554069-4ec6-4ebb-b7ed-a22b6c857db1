@import 'common/styles/variables.less';

.sceneItem {
  &[class] {
    background-color: #262C33;
  }
}

.characterCard {
  position: relative;
  overflow: visible;
  background-color: transparent;
  margin-bottom: 12px;

  :global {
    .mantine-Card-section {
      overflow: hidden;
      height: 205px;
      border-radius: 8px;

      img {
        width: 152px;
        height: 270px;
        flex-shrink: 0;
        aspect-ratio: 152/205;
        object-fit: cover;
        border-radius: 8px;
      }
    }
  }
}

.addButton {
  &[class] {
    background-color: transparent;
    color: #69FFA3;
    border: none;
    padding: 0;
    font-size: 16px;
    font-weight: 500;

    &:hover {
      background-color: transparent;
    }
  }
}

.sceneWrapper {
  position: relative;
  background-color: #29313E;
  width: 662px;

  &[class] {
    border: 1px solid transparent;
    transition: border-color 0.2s ease;

    &:hover {
      border-color: rgba(105, 255, 163, 0.30);
    }
  }

  .addButtonTop,
  .addButtonBottom {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 1;

    :global(.mantine-ActionIcon-root) {
      width: 24px;
      height: 24px;
      padding: 0;

      background: none;
      border: none;


    }
  }

  .addButtonIcon {
    color: #000;
    background: @green-text-color;
    border-radius: 50%;

    &:hover {
      background: rgba(106, 255, 163, 0.8);
    }
  }

  .addButtonTop {
    top: -14px;
  }

  .addButtonBottom {
    bottom: -14px;

  }

  &:hover {

    .addButtonTop,
    .addButtonBottom {
      opacity: 1;
    }
  }
}

.sceneItem {
  position: relative;
  z-index: 0;
}

.container {
  display: flex;
  gap: 16px;
}

.mainContent {
  flex: 1;
}

.sidebar {
  width: 320px;
  min-width: 320px;
  height: 100vh;
  position: sticky;
  top: 0;
  padding: 16px;
  background-color: var(--mantine-color-dark-7);
  border-left: 1px solid var(--mantine-color-dark-4);
}

.characterCard,
.environmentCard {
  margin-bottom: 12px;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }
}

// .regenerateButton {
//   &[class] {
//     background-color: #3C485B;
//     color: #69FFA3;
//     border: none;
//     border-radius: 4px;
//     padding: 4px 12px;
//     height: 32px;
//     font-size: 14px;
//     font-weight: 400;

//     &:hover {
//       background-color: #262C33;
//     }
//   }
// }


.moveSceneButton {
  &[class] {
    background-color: #3C485B;
    color: #FCFEFF;
    border: none;
    border-radius: 4px;
    width: 32px;
    height: 32px;
    padding: 8px;

    &:hover {
      background-color: #262C33;
    }

    &[data-disabled] {
      background-color: #3C485B;
      color: rgba(252, 254, 255, 0.25);
      cursor: not-allowed;

      &:hover {
        background-color: #2C3842;
      }
    }
  }
}

// 场景选项按钮样式
.sceneOptionButton {
  display: flex;
  align-items: center;
  margin-right: 8px;

  .sceneOptionValue {
    display: flex;
    align-items: center;
    background-color: #3C485B;
    border-radius: 8px;
    padding: 6px 15px;
    cursor: pointer;
    height: 36px;
    position: relative;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .sceneOptionLabel {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin-right: 2px;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #767F8C
  }

  .sceneOptionImage {

    border-radius: 4px;
    overflow: hidden;
    margin: 0 10px 0 2px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
    }
  }

  .sceneOptionArrow {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
}

.roleCard {
  width: 195px;
  position: relative;
  cursor: pointer;
}