import React, { useEffect } from 'react';
import { ScrollArea, Grid, Paper, Image } from '@mantine/core';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import { $VideoInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import classes from './videoEditor.module.less';

// 运镜选择组件
export const CameraOptions: React.FC<{ data?: $VideoInfo; onCameraChange: (cameraId: string) => void }> = ({
  data,
  onCameraChange,
}) => {
  const [selectedCamera, setSelectedCamera] = React.useState<string>('');
  const { basicConfig } = useEditorStore();
  // console.log('basicConfig', basicConfig);

  // 根据传入的视频数据设置默认运镜
  useEffect(() => {
    if (data?.strMovementId) {
      setSelectedCamera(data.strMovementId);
    }
  }, [data]);

  return (
    <Grid
      gutter="md"
      classNames={{
        inner: classes.cameraOption, // 添加自定义类名
      }}>
      {basicConfig?.vecMovement?.map(option => (
        <Grid.Col span={3} key={option.strId}>
          <Paper
            style={{
              cursor: 'pointer',
              border: selectedCamera === option.strId ? '1px solid #6AFFA3' : '1px solid transparent',
              borderRadius: '8px',
              position: 'relative',
              width: '72px',
              overflow: 'hidden',
            }}
            onClick={() => {
              setSelectedCamera(option.strId);
              onCameraChange(option.strId);
            }}>
            {/* 显示视频或图片 */}
            {/* {hoveredCamera === option.strId && option.strReferVideo ? ( */}
            <div style={{ position: 'relative', width: '72px', height: '72px' }}>
              <video
                src={option.strReferVideo}
                autoPlay={false}
                onMouseEnter={(e: any) => {
                  void e.target.play().catch((err: any) => {
                    console.log('视频播放失败', err);
                  });
                }}
                onMouseLeave={(e: any) => {
                  e.target.pause();
                  e.target.currentTime = 0;
                }}
                loop
                muted
                poster={option.strReferPic + '?imageMogr2/format/webp|imageMogr2/crop/144x144'}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: '8px 8px 0 0',
                }}
              />
            </div>

            <div
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                width: '100%',
                color: selectedCamera === option.strId ? '#6AFFA3' : 'white',
                height: '24px',
                lineHeight: '24px',
                fontSize: '12px',
                textAlign: 'center',
                background: 'linear-gradient(to bottom,rgba(0,0,0,0),rgba(0,0,0,0.5))',
              }}>
              {option.strDesc}
            </div>
          </Paper>
        </Grid.Col>
      ))}
    </Grid>
  );
};
