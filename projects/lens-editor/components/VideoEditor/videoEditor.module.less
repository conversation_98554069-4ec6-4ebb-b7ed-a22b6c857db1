@import 'common/styles/variables.less';

.videoEditor {
  width: 100%;
  min-width: 1280px;

  height: calc(100vh - 69px); // 预留头部导航栏的空间
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0 auto; // 确保整个内容居中

}

.maskCover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.videoTopWrap {
  max-width: 1780px;
  margin: 0 auto;
  position: relative;
  flex: 1 1 auto; // 让上部分自动填充剩余空间
  min-height: 0; // 允许内容滚动
  width: 100%;
  padding: 0 16px; // 添加左右内边距，与时间轴对齐
}

.previewArea {
  flex: 1;
  min-width: 500px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  background: none;
}

.timelineEditor {
  position: relative;
  display: flex;
  flex: 0 0 auto; // 高度由内容决定，不伸缩
  flex-direction: column;
  z-index: 10;
  background: #29313E;
  padding: 0 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);

}

.btnSlider {
  &[class] {
    color: #fff !important;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 18px;
    line-height: 12px;
  }
}

.customSlider {
  &[class] {
    --slider-thumb-size: 22px;
    --slider-color: #fff;
  }
}

.cameraOption {
  &[class] {
    gap: 0 25px;
  }
}

.actionItem {

  border: 2px solid transparent;
  height: 100%;
  display: flex;
  user-select: none;

  justify-content: center;
  align-items: center;
  width: 100%;
  overflow: hidden;
  position: relative;


  &.actionItemActive {
    border: 2px solid @green-slt-color;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 6px;
      pointer-events: none;
      height: 100%;
      background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAkBAMAAABcc4Z7AAAAD1BMVEUdISkdIiodIisjIy0YIy0Q/rp4AAAABXRSTlNNREMdHZjIZn8AAAAVSURBVAjXYzBUZlBgZKA5YGRwZAEAMVYAvGqjDg0AAAAASUVORK5CYII=') 3px center no-repeat rgba(105, 255, 163, 1);
      cursor: ew-resize;
      background-size: 1px 10px;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &::after {
      right: 0;
    }

    &::before {
      left: 0;
      background-position: 2px center;
    }
  }

  &.video {}

  &.subtitle {
    cursor: grab;
  }

  &.audio {
    background: #191B1D;


    &::before {
      content: '';
      position: absolute;
      top: 10px;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('projects/lens-editor/assets/images/pages/audio_timeline_bg.png') repeat-x 0 center;
      background-size: 110px 26px;
    }
  }
}

/* 左边栏样式 */
.leftBar,
.rightBar {

  min-width: 200px;
  max-width: 320px;

  width: 18.75%;
  height: 100%;
  overflow: hidden;

  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.leftBar {
  padding: 16px 6px 16px 16px;
  display: flex;
  flex-direction: column;

}

.sceneItem {
  border-radius: 8px;
  margin-bottom: 8px;
  overflow: hidden;
}

.sceneHeader {
  padding: 6px 0;
  background: var(--mantine-color-dark-7);
}

.sceneContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 50px;
  position: relative;
  margin: 0 auto;
  padding-bottom: 5px;
}

.imageItem {
  position: relative;
  margin-bottom: 10px;
}

.imageDragging {
  opacity: 0.6;
  background: var(--mantine-color-blue-light);
  border: 2px dashed var(--mantine-color-blue-5);
}

.regenerateVideoButton {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 20;

}


.sceneImage {
  cursor: pointer !important;
  border-radius: 4px;
  overflow: hidden;
  background: var(--mantine-color-dark-8);
  object-fit: cover;
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  border: 1px solid transparent;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.sceneImageSelected {
  border: 1px solid #69FFA3;
}

.addStoryboardButton {
  width: 100%;
  height: 120px;
  border: 1px dashed #69FFA3;
  border-radius: 8px;
  margin-top: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(105, 255, 163, 0.05);

  &:hover {
    background-color: rgba(105, 255, 163, 0.1);
    border-color: #4CD488;
  }
}

.deleteSceneImage {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
  border: none;
}

.addSceneImage {
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  cursor: pointer;
  border-radius: 50%;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.disabledButton {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinnerOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.timelineEditor {
  :global {

    .mantine-AppShell-main,
    .mantine-Container-root {
      padding-left: 0;
      padding-right: 0;
      padding-bottom: 0;
    }

    .timeline-editor {
      height: 183px;
      flex: 1;
      border-radius: 8px;
      margin: 0 0 10px 10px;

      .timeline-editor-edit-area .ReactVirtualized__Grid {
        overflow-y: hidden !important;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 8px !important;
          height: 8px !important;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }

      .timeline-editor-cursor {
        border-color: @green-slt-color !important;
      }

      .timeline-editor-cursor-top path {
        fill: @green-slt-color !important;
      }

      .timeline-editor-action {
        height: 90% !important;
        background: @gray-color;
        border-radius: 4px;
        z-index: 3;
        overflow: hidden;


        .timeline-editor-action-right-stretch::after,
        .timeline-editor-action-left-stretch::after {
          border: none;
        }

      }

      .timeline-editor-edit-row {
        background: none;
      }

      .timeline-editor-edit-row:nth-child(2) {
        height: 36px !important;
      }

      .timeline-editor-edit-row:nth-child(3) {
        height: 44px !important;
        top: 92px !important;
      }

    }
  }
}

.videoGenerated {
  position: absolute;
  bottom: 8px;
  right: 8px;

  height: 20px;
  padding: 0 5px;
  background: rgba(0, 0, 0, 0.5);
  text-align: right;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;


}

.imageIcon {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAoCAMAAACPWYlDAAAAVFBMVEUAAADi4uIvLy8AAAC+vr4bGxsNDQ0wMDDa2tqRkZFVVVU5OTnS0tJ2dnYqKiovLy8wMDAvLy8vLy8yMjIxMTGrq6ufn5+EhIRnZ2dmZmbJyclCQkLGkpn9AAAAHHRSTlOA5o0AzYeDT+CzmQnZpot8c3NYIxK/ua2foNOTY8PsxwAAARlJREFUOMu11etygyAQhuHlEwTxFE8xbe//PosZ6erYwKYzff8/suCJitBke2QbxrkIBVBbCLP1BuobYErKVhpgqAOw6EhYB1vQDJA4YKIRRg4MLN3A83vtlG5T+0BPh4kq9axKznQArdrzQqAj0EKgYk4I3I/4r5F8BG0KZI/18fEaUKuV056O3XlCBonA90UGlg0sMsDn4MwZJPbYxGO4At7jJ+j8dPESV7CqUANeYG99AeDUU/i4QExfAF+Rh/aBx3zqTm/patXqkP79BUr0dQVoUuB+BVqlcoYBbyBVRWcAlcmdgWlUrscJLCqbZiBsBz1KEldiIPvex3ikCV0pXwAzFVYsyg727V/WX36KoXm8IVtvpyL0DQimCpBZibqgAAAAAElFTkSuQmCC') no-repeat center center;
  background-size: 24px 20px;
  width: 24px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sceneMoreMenu {
  background-color: #3C485B;
  color: #fff;

  button {
    text-align: center;
  }


}

:global {
  .mantine-Menu-dropdown {

    .mantine-Menu-item {

      &:hover {
        background-color: #0A0F17;
      }
    }
  }
}

.imglistItem {
  border: 2px solid transparent;
  border-radius: 4px;
  position: relative;

  &.selected {
    border: 2px solid @green-slt-color;
  }

  .previewIcon {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.failMask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 0.4rem;
  cursor: pointer;
  text-align: center;
  padding: 0 5px;
  z-index: 1;
}

.img2videoIcon {
  background: url('projects/lens-editor/assets/images/pages/img2video.png') no-repeat center center;
  background-size: 20px 20px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}