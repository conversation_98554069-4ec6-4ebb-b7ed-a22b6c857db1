import React, { useCallback } from 'react';
import { Stack } from '@mantine/core';
import { IconBrandBandlab, IconBrandYoutube, IconLetterTSmall } from '@tabler/icons-react';
import { CustomTimelineRow } from './types';

interface TrackIconsProps {
  timelineData: CustomTimelineRow[];
}

/**
 * 轨道图标组件，显示不同类型轨道对应的图标
 */
export const TrackIcons = React.memo(({ timelineData }: TrackIconsProps) => {
  const renderTrackIcon = useCallback((id: string, index: number) => {
    if (id.includes('audio')) {
      return <IconBrandBandlab size={20} style={{ margin: '10px 0' }} color="#fff" key={index} />;
    } else if (id.includes('subtitle')) {
      return (
        <IconLetterTSmall
          size={20}
          style={{ border: '2px solid #fff', borderRadius: '4px', margin: '9px 0' }}
          color="#fff"
          key={index}
        />
      );
    } else if (id.includes('video')) {
      return <IconBrandYoutube size={20} style={{ margin: '20px 0' }} color="#fff" key={index} />;
    }
    return null;
  }, []);

  return (
    <Stack gap={0} style={{ paddingTop: '39px' }}>
      {timelineData.map((item, index) => renderTrackIcon(item.id, index))}
    </Stack>
  );
});

TrackIcons.displayName = 'TrackIcons';
