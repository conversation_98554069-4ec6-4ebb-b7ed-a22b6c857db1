// 图片模型和视频模型选择器组件
import React, { useState, useEffect } from 'react';
import { Box, Group, Image, Text, Paper, LoadingOverlay } from '@mantine/core';
import { IconCheck, IconChevronDown } from '@tabler/icons-react';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';

interface ModelSelectProps {
  mvTplId?: string; // 图片模型改成了根据mv模板id从mv模板中获取
  type: 'picture' | 'video';
  value?: string;
  onChange?: (value: string | null) => void;
  disabled?: boolean;
}

// 自定义选项渲染
const ModelOption = ({
  option,
  checked,
  onClick,
}: {
  option: { icon: string; label: string; value: string };
  checked?: boolean;
  onClick: () => void;
}) => {
  return (
    <Group wrap="nowrap" style={{ width: '100%', cursor: 'pointer', padding: '8px 12px' }} onClick={onClick}>
      <Image src={option.icon} alt={option.label} width={32} height={32} radius="sm" />
      <div style={{ flex: 1 }}>{option.label}</div>
      {checked && <IconCheck size={18} stroke={1.5} />}
    </Group>
  );
};

export function ModelSelect({ mvTplId, type, value, onChange, disabled }: ModelSelectProps) {
  const { basicConfig } = useEditorStore();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  // 添加内部状态以确保组件响应外部value的变化
  const [currentValue, setCurrentValue] = useState<string | undefined>(value);

  // 当外部value变化时更新内部状态
  useEffect(() => {
    console.log('value changed in ModelSelect:', value);
    setCurrentValue(value);
  }, [value]);

  // 根据类型和mvTplId选择不同的模型数据
  const modelData = (() => {
    if (type === 'picture') {
      // 如果有指定的MV模板ID，则从该模板中获取画面风格
      if (mvTplId && basicConfig?.vecMvTpl) {
        const selectedMvTpl = basicConfig.vecMvTpl.find(tpl => tpl.strId === mvTplId);
        // 如果找到模板并且有vecPicModel属性，则使用该属性
        if (selectedMvTpl?.vecPicModel) {
          // console.log('[ModelSelect] 从MV模板获取画面风格:', selectedMvTpl.vecPicModel);
          return (
            selectedMvTpl.vecPicModel.map(model => ({
              value: model.strId,
              label: model.strModel,
              icon: model.strReferPic,
            })) || []
          );
        }
      }
      // 如果没有指定MV模板ID或找不到对应模板，则回退到基础配置
      // console.log('[ModelSelect] 从基础配置获取画面风格:', basicConfig?.vecPicMod);
      return (
        basicConfig?.vecPicMod?.map(model => ({
          value: model.strId,
          label: model.strModel,
          icon: model.strReferPic,
        })) || []
      );
    } else {
      // 视频模型仍然从基础配置获取
      return (
        basicConfig?.vecViMod?.map(model => ({
          value: model.strId,
          label: model.strModel,
          icon: model.strReferPic,
        })) || []
      );
    }
  })();

  // 获取当前选中的模型，使用内部状态而不是直接使用prop
  const selectedModel = modelData.find(model => model.value === currentValue);
  // console.log('[selectedModel]2:', isDropdownOpen, selectedModel, modelData, currentValue);

  return (
    <Box style={{ position: 'relative' }}>
      {/* 选中项展示区域 */}
      <LoadingOverlay visible={disabled} overlayProps={{ radius: 'sm', blur: 2 }} loaderProps={{ type: 'dots' }} />
      <Box
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        style={{
          cursor: 'pointer',
          padding: '8px 12px',
          borderRadius: '4px',
          backgroundColor: 'var(--mantine-color-dark-6)',
        }}>
        <Group wrap="nowrap" gap="xs" justify="space-between">
          <Group wrap="nowrap" gap="xs">
            {selectedModel ? (
              <>
                <Image src={selectedModel.icon} alt={selectedModel.label} width={32} height={32} radius="sm" />
                <Text size="sm">{selectedModel.label}</Text>
              </>
            ) : (
              <Text size="sm" c="dimmed">
                {type === 'picture' ? '请选择图片模型' : '请选择视频模型'}
              </Text>
            )}
          </Group>
          <IconChevronDown
            size={18}
            style={{
              transform: isDropdownOpen ? 'rotate(180deg)' : 'none',
              transition: 'transform 0.2s ease',
            }}
          />
        </Group>
      </Box>

      {/* 下拉选项列表 */}
      {isDropdownOpen && (
        <Paper
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            marginTop: '4px',
            zIndex: 1000,
            backgroundColor: 'var(--mantine-color-dark-6)',
            border: '1px solid var(--mantine-color-dark-4)',
            borderRadius: '4px',
            maxHeight: '300px',
            overflowY: 'auto',
          }}
          shadow="md">
          {modelData.map(model => (
            <ModelOption
              key={model.value}
              option={model}
              checked={model.value === currentValue}
              onClick={() => {
                // 更新内部状态的同时通知父组件
                setCurrentValue(model.value);
                onChange?.(model.value);
                setIsDropdownOpen(false);
              }}
            />
          ))}
        </Paper>
      )}
    </Box>
  );
}
