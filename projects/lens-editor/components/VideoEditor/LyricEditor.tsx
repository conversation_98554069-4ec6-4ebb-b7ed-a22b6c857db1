import React, { useState, useEffect } from 'react';
import { Modal, Button, Group, Textarea, Box, Text } from '@mantine/core';
import { TLActionWithName } from './types';
import { $SubtitleItem } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';

interface LyricEditorProps {
  opened: boolean;
  onClose: () => void;
  action: TLActionWithName | null;
  subtitleItem: $SubtitleItem | null;
  onSave: (content: string) => void;
}

export function LyricEditor({ opened, onClose, action, subtitleItem, onSave }: LyricEditorProps) {
  const [content, setContent] = useState('');

  // 当字幕项变化时更新内容
  useEffect(() => {
    if (subtitleItem) {
      setContent(subtitleItem.strContent || '');
    }
  }, [subtitleItem]);

  const handleSave = () => {
    console.log('[handleSave]', content);
    onSave(content);
    onClose();
  };

  return (
    <Modal opened={opened} onClose={onClose} title="编辑歌词" size="md" centered>
      <Box>
        <Textarea
          label=""
          placeholder="请输入歌词内容"
          value={content}
          onChange={e => setContent(e.currentTarget.value)}
          minRows={4}
          maxRows={8}
          autosize
          mb="md"
        />

        {action && (
          <Group mb="md">
            <Text size="sm">开始时间: {action.start.toFixed(2)}s</Text>
            <Text size="sm">结束时间: {action.end.toFixed(2)}s</Text>
            <Text size="sm">时长: {(action.end - action.start).toFixed(2)}s</Text>
          </Group>
        )}

        <Group justify="flex-end" mt="md">
          <Button variant="outline" onClick={onClose} size="xs">
            取消
          </Button>
          <Button onClick={handleSave} size="xs">
            完成编辑
          </Button>
        </Group>
      </Box>
    </Modal>
  );
}
