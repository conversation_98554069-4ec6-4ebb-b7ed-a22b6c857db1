import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Timeline } from '@xzdarcy/react-timeline-editor';
import { Group, LoadingOverlay } from '@mantine/core';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import { CustomTimelineRow, TLActionWithName, TimelineEditorProps } from './types';
import { ActionRender } from './TLActionRender';
import { formatTime } from 'projects/lens-editor/utils/index';
import { CustomScale } from './CustomScale';
import { TimelineControls } from './TimelineControls';
import { TrackIcons } from './TrackIcons';
import { useTimelineHandlers } from '../../hooks/useTimelineHandlers';
import { notifications } from '@mantine/notifications';

/**
 * 时间线编辑器组件 负责视频/音频/字幕等轨道的时间线编辑
 */
export const TimelineEditor = React.memo((props: TimelineEditorProps) => {
  const { timelineData, timelineState, activeAction, playing, onPlayPause } = props;

  const { scriptInfo } = useEditorStore();
  const [scale, setScale] = useState(10);
  const [currentTime, setCurrentTime] = useState(0);
  const [viewportWidth, setViewportWidth] = useState(window.innerWidth);

  useEffect(() => {
    // 处理窗口大小变化的函数
    const handleResize = () => {
      setViewportWidth(window.innerWidth);
    };

    // 监听 resize 事件
    window.addEventListener('resize', handleResize);

    // 在组件卸载时清理事件监听器
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 根据脚本时长设置初始缩放比例
  useEffect(() => {
    // 根据脚本时长设置初始缩放比例，每6秒对应1倍scale，为了避免计算误差导致缩放不够，用ceil舍入
    // 获取当前网页可视区域宽度
    const s = Math.ceil((scriptInfo?.iTimeRange || 0) / (viewportWidth * 14));
    // 缩放倍率
    setScale(s);
  }, [scriptInfo?.iTimeRange, viewportWidth]);

  // Update current time when timeline changes
  useEffect(() => {
    if (!timelineState.current) return;

    const updateCurrentTime = () => {
      setCurrentTime(timelineState.current?.getTime() ? timelineState.current.getTime() * 1000 : 0);
    };

    updateCurrentTime();

    // 使用较低的更新频率，200ms 足够平滑但减少重渲染
    const intervalId = setInterval(updateCurrentTime, 200);

    return () => clearInterval(intervalId);
  }, [timelineState.current, playing]);

  // 处理缩放按钮点击
  const handleZoomOut = useCallback(() => setScale(prev => Math.max(1, prev - 1)), []);
  const handleZoomIn = useCallback(() => setScale(prev => Math.min(60, prev + 1)), []);

  // 获取时间线处理函数
  const handlers = useTimelineHandlers(props);

  // 渲染动作
  const getActionRender = useCallback(
    (action: TLActionWithName) => {
      try {
        if (!activeAction) return null;
        return <ActionRender action={action} scriptInfo={scriptInfo} activeAction={activeAction} />;
      } catch (error) {
        console.error('渲染动作失败:', error);
        // 返回一个基础的备用渲染
        return (
          <div
            style={{
              width: '100%',
              height: '100%',
              background: '#333743',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '10px',
            }}>
            {action.id || '加载错误'}
          </div>
        );
      }
    },
    [scriptInfo, activeAction]
  );

  // 格式化当前时间和总时间
  const formattedTime = useMemo(
    () => ({
      current: formatTime(currentTime),
      total: formatTime(scriptInfo?.iTimeRange || 0),
    }),
    [currentTime, scriptInfo?.iTimeRange]
  );

  // 是否显示加载中
  const showLoading = !timelineData.length;

  return (
    <>
      <TimelineControls
        scale={scale}
        setScale={setScale}
        currentTime={formattedTime.current}
        totalTime={formattedTime.total}
        playing={playing}
        onPlayPause={onPlayPause}
        handleZoomIn={handleZoomIn}
        handleZoomOut={handleZoomOut}
      />

      <Group gap={0} align="flex-start" mt="8px" style={{ position: 'relative', borderTop: '1px solid #333743' }}>
        <LoadingOverlay
          visible={showLoading}
          overlayProps={{ radius: 'sm', blur: 2 }}
          loaderProps={{ type: 'dots', color: '#ccc', size: 'sm' }}
        />
        {!showLoading && (
          <>
            <TrackIcons timelineData={timelineData} />
            <Timeline
              ref={v => {
                if (v == null) return;
                timelineState.current = v;
              }}
              onChange={d => {
                console.log('[Timeline onChange]:', d);
              }}
              scale={scale}
              rowHeight={60}
              scaleWidth={50}
              editorData={timelineData}
              effects={{}}
              getScaleRender={scale => <CustomScale scale={scale} />}
              scaleSplitCount={10}
              onClickTimeArea={handlers.handleClickTimeArea}
              onCursorDragEnd={handlers.handleCursorDragEnd}
              onActionResizeStart={({ row, action }: { row: CustomTimelineRow; action: TLActionWithName }) => {
                if (row.id.includes('video') && action.videoStatus !== 2) {
                  notifications.show({
                    title: '提示',
                    message: '未生成视频时不可调整分镜时长，请先生成视频',
                    color: 'orange',
                  });
                }
                if (activeAction?.id !== action.id) {
                  notifications.show({
                    title: '提示',
                    message: '只能拖动当前选中的分镜',
                    color: 'orange',
                  });
                }
              }}
              onActionResizing={handlers.handleActionResizing}
              onActionResizeEnd={handlers.handleActionResizeEnd}
              onActionMoving={handlers.handleActionMoving}
              onActionMoveEnd={handlers.handleActionMoveEnd}
              onClickActionOnly={handlers.handleClickAction}
              getActionRender={getActionRender}
              autoScroll
            />
          </>
        )}
      </Group>
    </>
  );
});

TimelineEditor.displayName = 'TimelineEditor';

// 自定义比较函数，只在真正有变化时才重新渲染
const arePropsEqual = (prevProps: TimelineEditorProps, nextProps: TimelineEditorProps) => {
  // 比较时间轴数据 - 使用浅比较，因为每次更新都会创建新的数组引用
  if (prevProps.timelineData.length !== nextProps.timelineData.length) {
    return false;
  }

  // 比较每个轨道的actions数量和ID
  for (let i = 0; i < prevProps.timelineData.length; i++) {
    const prevTrack = prevProps.timelineData[i];
    const nextTrack = nextProps.timelineData[i];

    if (prevTrack.id !== nextTrack.id || prevTrack.actions.length !== nextTrack.actions.length) {
      return false;
    }

    // 比较actions的关键属性
    for (let j = 0; j < prevTrack.actions.length; j++) {
      const prevAction = prevTrack.actions[j];
      const nextAction = nextTrack.actions[j];

      if (
        prevAction.id !== nextAction.id ||
        prevAction.start !== nextAction.start ||
        prevAction.end !== nextAction.end ||
        prevAction.status !== (nextAction as any).status
      ) {
        return false;
      }
    }
  }

  // 比较其他props
  return (
    prevProps.activeAction?.id === nextProps.activeAction?.id &&
    prevProps.playing === nextProps.playing &&
    prevProps.timelineState === nextProps.timelineState &&
    prevProps.actionSpriteMap === nextProps.actionSpriteMap
  );
};

// 应用优化的memo
export const OptimizedTimelineEditor = React.memo(TimelineEditor, arePropsEqual);
