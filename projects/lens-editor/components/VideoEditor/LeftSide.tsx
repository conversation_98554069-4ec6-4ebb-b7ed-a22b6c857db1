import React, { useMemo, useState } from 'react';
import {
  Box,
  Text,
  ActionIcon,
  ScrollArea,
  Flex,
  Menu,
  Button,
  Overlay,
  Image,
  Group,
  LoadingOverlay,
} from '@mantine/core';

import { IconDots } from '@tabler/icons-react';
import {
  emStatus,
  emResolution,
  $SceneInfo,
  $StoryboardInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import {
  emEditType,
  emScriptGetMask,
  $WebScriptInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';
import appcss from 'projects/lens-editor/App.less';
import classes from './videoEditor.module.less';
import useConfirm from '../ConfirmationContext';
import { AddIcon, CloseIcon } from '../../assets/IconSvg';
import cx from 'clsx';
import { defaultImg } from 'common/utils';
import { StoryboardCollapseIcon, StoryboardExpandIcon } from './MoveSceneIcon';
import { useEditorStore, selectAllStoryboards } from 'projects/lens-editor/store/editorStore';
import { notifications } from '@mantine/notifications';
import req from 'common/request';
import { newEmptyStoryboard } from 'projects/lens-editor/constants/config';
import { SelectionState, StoryboardOperationHandler } from './types';
import { AddSceneModal } from './AddSceneModal';

// 定义 LeftSide 组件的 Props 类型
interface LeftSideProps {
  updateSelectionState: (newState: Partial<SelectionState>) => void;
  generateAllVideos: () => void;
  selectionState: SelectionState;
  setAddBoardStoryboard: (storyboard: Partial<$StoryboardInfo>) => void;
  setIsAddBoardModalOpen: (isOpen: boolean) => void;
  storyboardRefs: React.MutableRefObject<Record<string, HTMLDivElement | null>>;
  handleRegeneratePicture: (strId?: string) => void;
  handleStoryboardOperation: StoryboardOperationHandler;
  isLoading: boolean;
  fetchScriptInfoOnce: (mask: emScriptGetMask, onSuccess?: () => void) => void;
  expandedScenes: Record<string, boolean>;
  setExpandedScenes: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
}

// 渲染左侧场景和分镜部分
const LeftSide: React.FC<LeftSideProps> = ({
  updateSelectionState,

  generateAllVideos,
  selectionState,
  setAddBoardStoryboard,
  setIsAddBoardModalOpen,
  storyboardRefs,
  handleRegeneratePicture,
  handleStoryboardOperation,
  isLoading,
  fetchScriptInfoOnce,
  expandedScenes,
  setExpandedScenes,
}) => {
  const { scriptInfo, isButtonLoading, setScriptInfo, scriptId, hasVideoGenerating } = useEditorStore();
  const confirm = useConfirm();
  const scenes = scriptInfo?.vecNewScene || [];
  const allStoryboards = useEditorStore(selectAllStoryboards);

  // 新增状态管理
  const [isAddSceneModalOpen, setIsAddSceneModalOpen] = useState(false);
  const [targetSceneIndex, setTargetSceneIndex] = useState<number | null>(null);
  const [isCreatingScene, setIsCreatingScene] = useState(false);

  const onDragEnd = (result: {
    source: { index: number; droppableId: string };
    destination: { index: number; droppableId: string } | null;
    type: string;
  }) => {
    const { source, destination, type } = result;

    if (!destination) return;

    // 场景层级的拖拽
    if (type === 'SCENE') {
      // 获取要移动的场景
      const sourceScene = { ...scenes[source.index] };

      // 创建新的场景数组并重新排序
      const reorderedScenes = Array.from(scenes);
      reorderedScenes.splice(source.index, 1);
      reorderedScenes.splice(destination.index, 0, sourceScene);

      // 更新所有场景的索引，从1开始
      const updatedScenes = reorderedScenes.map((scene, index) => ({
        ...scene,
        iIndex: index + 1, // 从1开始
      }));

      // 更新本地状态
      setScriptInfo({
        ...scriptInfo,
        vecNewScene: updatedScenes,
      } as $WebScriptInfo);

      // 调用后端API保存场景顺序
      req
        .post('/lens_script/edit_scene', {
          strScriptId: scriptId,
          vecNewScene: [
            {
              ...sourceScene,
              iIndex: destination.index + 1, // 从1开始
            },
          ],
          vectorEditField: [emEditType.EM_EDIT_SCEN_TYPE_CHANGE_ORDER], // 修改场景顺序
        })
        .then(res => {
          if (res.data.error_code !== 0) {
            notifications.show({
              title: '更新场景顺序失败',
              message: res.data.error_msg,
              color: 'red',
            });
            return;
          }
          notifications.show({
            message: '更新场景顺序成功',
            color: 'green',
          });
        })
        .catch(error => {
          notifications.show({
            title: '更新场景顺序失败',
            message: error instanceof Error ? error.message : '更新场景顺序失败，请稍后重试',
            color: 'red',
          });
        });
      return;
    }

    // 分镜层级的拖拽
    if (type === 'STORYBOARD') {
      const sourceSceneId = source.droppableId;
      const destSceneId = destination.droppableId;

      // 如果在同一场景内移动
      if (sourceSceneId === destSceneId) {
        // 获取当前场景的所有分镜
        const sceneStoryboards = getStoryboardsBySceneId(sourceSceneId);

        // 获取要移动的分镜
        const sourceStoryboard = { ...sceneStoryboards[source.index] };

        if (!sourceStoryboard) return;

        console.log('移动前 - 源分镜:', sourceStoryboard.strId, 'iIndex:', sourceStoryboard.iIndex);

        // 创建新的分镜数组并重新排序
        const reorderedStoryboards = Array.from(sceneStoryboards);
        reorderedStoryboards.splice(source.index, 1);
        reorderedStoryboards.splice(destination.index, 0, sourceStoryboard);

        // 更新所有分镜的索引，从1开始
        const updatedSceneStoryboards = reorderedStoryboards.map((sb, index) => ({
          ...sb,
          iIndex: index + 1, // 从1开始
        }));

        // 更新移动的分镜对象，设置正确的场景ID和索引
        const updatedSourceStoryboard = {
          ...sourceStoryboard,
          strSceneId: destSceneId,
          iIndex: destination.index + 1, // 从1开始
        };

        console.log('移动后 - 源分镜:', updatedSourceStoryboard.strId, 'iIndex:', updatedSourceStoryboard.iIndex);

        // 获取其他场景的分镜
        const otherScenesStoryboards = allStoryboards.filter(sb => sb.strSceneId !== sourceSceneId);

        // 合并当前场景更新后的分镜和其他场景的分镜
        const updatedAllStoryboards = [...otherScenesStoryboards, ...updatedSceneStoryboards];

        // 更新本地状态
        setScriptInfo({
          ...scriptInfo,
          VecNewStoryboard: updatedAllStoryboards,
        } as $WebScriptInfo);

        // 调用 API 保存顺序
        handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_CHANGE_ORDER, updatedSourceStoryboard);
      } else {
        // 跨场景移动
        // 获取源场景和目标场景的所有分镜
        const sourceSceneStoryboards = getStoryboardsBySceneId(sourceSceneId);
        const destSceneStoryboards = getStoryboardsBySceneId(destSceneId);

        // 找到要移动的分镜
        const movedStoryboard = { ...sourceSceneStoryboards[source.index] };

        if (!movedStoryboard) return;

        // 从源场景移除分镜并重新计算索引（从1开始）
        const updatedSourceStoryboards = sourceSceneStoryboards
          .filter(s => s.strId !== movedStoryboard.strId)
          .map((s, index) => ({ ...s, iIndex: index + 1 })); // 从1开始

        // 创建要移动到目标场景的分镜对象，更新场景ID和索引
        const movedStoryboardWithNewScene = {
          ...movedStoryboard,
          strSceneId: destSceneId,
          iIndex: destination.index + 1, // 从1开始
        };

        // 在目标场景添加分镜
        const updatedDestStoryboards = [
          ...destSceneStoryboards.slice(0, destination.index),
          movedStoryboardWithNewScene,
          ...destSceneStoryboards.slice(destination.index),
        ].map((s, index) => ({ ...s, iIndex: index + 1 })); // 从1开始

        // 获取其他场景的分镜（不包括源场景和目标场景）
        const otherScenesStoryboards = allStoryboards.filter(
          sb => sb.strSceneId !== sourceSceneId && sb.strSceneId !== destSceneId
        );

        // 合并所有更新后的分镜
        const updatedAllStoryboards = [
          ...otherScenesStoryboards,
          ...updatedSourceStoryboards,
          ...updatedDestStoryboards,
        ];

        // 更新本地状态
        setScriptInfo({
          ...scriptInfo,
          VecNewStoryboard: updatedAllStoryboards,
        } as $WebScriptInfo);

        // 调用 API 保存顺序
        handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_CHANGE_ORDER, movedStoryboardWithNewScene);
      }
    }
  };

  // 按场景ID组织分镜数据
  const getStoryboardsBySceneId = (sceneId: string) => {
    return allStoryboards
      .filter((storyboard: $StoryboardInfo) => storyboard.strSceneId === sceneId)
      .sort((a: $StoryboardInfo, b: $StoryboardInfo) => (a.iIndex || 0) - (b.iIndex || 0));
  };
  // 新增添加场景功能 - 修改为打开弹窗
  const openAddSceneModal = (sceneIndex: number) => {
    setTargetSceneIndex(sceneIndex);
    setIsAddSceneModalOpen(true);
  };

  // + 新增：处理弹窗确认后的场景创建逻辑
  const handleConfirmAddScene = async (title: string) => {
    if (targetSceneIndex === null) {
      notifications.show({
        title: '错误',
        message: '未指定场景创建位置，请重试',
        color: 'red',
      });
      return;
    }
    setIsCreatingScene(true);

    const newScene: Partial<$SceneInfo> = {
      iIndex: targetSceneIndex + 1 + 1, // 插入到目标索引之后。 iIndex是从1开始，所以多加1
      strSceneTitle: title,
    };

    try {
      const res = await req.post('/lens_script/edit_scene', {
        strScriptId: scriptId,
        vectorEditField: [emEditType.EM_EDIT_SCEN_TYPE_ADD],
        vecScene: [newScene],
      });

      if (res.data.error_code !== 0) {
        notifications.show({
          title: '添加场景失败',
          message: res.data.error_msg,
          color: 'red',
        });
      } else {
        notifications.show({
          message: '添加场景成功',
          color: 'green',
        });
        fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL); // 刷新数据
        setIsAddSceneModalOpen(false); // 关闭弹窗
      }
    } catch (error) {
      notifications.show({
        title: '添加场景失败',
        message: error instanceof Error ? error.message : '添加场景失败，请稍后重试',
        color: 'red',
      });
    } finally {
      setIsCreatingScene(false);
      setTargetSceneIndex(null); // 重置
    }
  };

  // 删除场景函数
  const handleDeleteScene = (sceneId: string) => {
    confirm({
      title: '删除场景',
      description: '确定要删除此场景吗？场景下的所有分镜也将被删除。',
      onConfirm: () => {
        req
          .post('/lens_script/edit_scene', {
            strScriptId: scriptId,
            vecScene: [
              {
                strId: sceneId,
              },
            ],
            vectorEditField: [emEditType.EM_EDIT_SCEN_TYPE_DELETE], // 删除场景
          })
          .then(res => {
            if (res.data.error_code !== 0) {
              notifications.show({
                title: '删除场景失败',
                message: res.data.error_msg,
                color: 'red',
              });
              return;
            }
            notifications.show({
              message: '删除场景成功',
              color: 'green',
            });

            // 更新本地状态
            const updatedScenes = scenes.filter(scene => scene.strId !== sceneId);
            setScriptInfo({
              ...scriptInfo,
              vecNewScene: updatedScenes,
            } as $WebScriptInfo);

            // 刷新数据
            fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, () => {
              console.log('[fetchScriptInfoOnce success]:');
            });
          })
          .catch(error => {
            notifications.show({
              title: '删除场景失败',
              message: error instanceof Error ? error.message : '删除场景失败，请稍后重试',
              color: 'red',
            });
          });
      },
    });
  };

  // 播放场景
  const handlePlayScene = (sceneId: string) => {
    console.log('[handlePlayScene]:', sceneId);
    // TODO ：播放场景
  };

  // 处理分镜点击事件
  const handleStoryboardClick = (storyboard: $StoryboardInfo, scene: $SceneInfo) => {
    // 使用统一的选择状态更新函数
    updateSelectionState({
      selectedStoryboard: storyboard,
    });
  };

  // 切换场景的展开/收起状态
  const toggleExpand = (sceneId: string) => {
    setExpandedScenes(prev => ({
      ...prev,
      [sceneId]: !prev[sceneId],
    }));
  };

  const remainingStoryboardsCount =
    allStoryboards.filter(
      storyboard =>
        !storyboard.stCurVideo ||
        !storyboard.stCurVideo.strVideo ||
        storyboard.stCurVideo.eStatus !== emStatus.EM_STATUS_SUCC
    )?.length || 0;

  return (
    <>
      {remainingStoryboardsCount > 0 && (
        <Box
          mb={10}
          style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginRight: 10 }}>
          <Text size="sm">剩余{remainingStoryboardsCount}个分镜视频</Text>
          <span
            className={appcss.txtBtn}
            onClick={() => generateAllVideos()}
            style={{
              cursor: hasVideoGenerating() ? 'not-allowed' : 'pointer',
              opacity: hasVideoGenerating() ? 0.6 : 1,
            }}>
            {hasVideoGenerating() ? '生成中...' : '全部生成视频'}
          </span>
        </Box>
      )}
      <ScrollArea h="100%" offsetScrollbars="y">
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="scenes" type="SCENE">
            {provided => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                {scenes.map((scene: $SceneInfo, sceneIndex: number) => (
                  <Draggable key={scene.strId} draggableId={scene.strId} index={sceneIndex}>
                    {(provided, snapshot) => {
                      return (
                        <div ref={provided.innerRef} {...provided.draggableProps} className={classes.sceneItem}>
                          {/* 场景头部 */}
                          <div className={classes.sceneHeader} {...provided.dragHandleProps}>
                            <Group justify="space-between" wrap="nowrap" gap={0}>
                              <Group wrap="nowrap" gap={0}>
                                <Text>
                                  {`场景 ${scene.iIndex !== undefined ? scene.iIndex : sceneIndex + 1} `}
                                  {scene.strSceneTitle}
                                </Text>

                                <ActionIcon onClick={() => toggleExpand(scene.strId)} variant="transparent">
                                  {expandedScenes[scene.strId] ? <StoryboardExpandIcon /> : <StoryboardCollapseIcon />}
                                </ActionIcon>
                              </Group>

                              <Menu shadow="md" width={120} position="bottom-start">
                                <Menu.Target>
                                  <ActionIcon variant="transparent">
                                    <IconDots color="#7E8083" size={18} />
                                  </ActionIcon>
                                </Menu.Target>

                                <Menu.Dropdown className={classes.sceneMoreMenu}>
                                  <Menu.Item onClick={() => openAddSceneModal(sceneIndex)}>新建场景</Menu.Item>
                                  <Menu.Item onClick={() => handleDeleteScene(scene.strId)}>删除场景</Menu.Item>
                                  {/* <Menu.Item onClick={() => handlePlayScene(scene.strId)}>播放场景</Menu.Item> */}
                                </Menu.Dropdown>
                              </Menu>
                            </Group>
                          </div>

                          {/* 分镜区域 */}
                          {expandedScenes[scene.strId] && (
                            <Droppable droppableId={scene.strId} type="STORYBOARD" direction="vertical">
                              {provided => (
                                <div
                                  {...provided.droppableProps}
                                  ref={provided.innerRef}
                                  className={classes.sceneContent}>
                                  {getStoryboardsBySceneId(scene.strId)?.length > 0 ? (
                                    <>
                                      {getStoryboardsBySceneId(scene.strId).map(
                                        (storyboard: $StoryboardInfo, storyboardIndex: number) => (
                                          <Draggable
                                            key={storyboard.strId}
                                            draggableId={storyboard.strId}
                                            index={storyboardIndex}>
                                            {(provided, snapshot) => (
                                              <div
                                                ref={el => {
                                                  provided.innerRef(el);
                                                  // 同时保存到我们的ref map中
                                                  storyboardRefs.current[storyboard.strId] = el;
                                                }}
                                                {...provided.draggableProps}
                                                {...provided.dragHandleProps}
                                                className={cx(classes.imageItem, {
                                                  [classes.imageDragging]: snapshot.isDragging,
                                                  [classes.imageSelected]:
                                                    selectionState.selectedStoryboard?.strId === storyboard.strId,
                                                })}
                                                onClick={() => handleStoryboardClick(storyboard, scene)}>
                                                <LoadingOverlay
                                                  zIndex={10}
                                                  visible={
                                                    storyboard.eStatus === 1 || storyboard.stCurPic?.eStatus === 1
                                                  }
                                                  overlayProps={{ radius: 'sm', blur: 2 }}
                                                />
                                                {storyboard?.eStatus === 3 ? (
                                                  <div>
                                                    <Text size="sm" c="red" ta="center">
                                                      分镜生成失败, 请重新生成
                                                    </Text>
                                                  </div>
                                                ) : storyboard.stCurPic?.eStatus === 3 ? (
                                                  <Box
                                                    style={{
                                                      position: 'relative',
                                                    }}>
                                                    <div
                                                      className={
                                                        classes.sceneImage +
                                                        (selectionState.selectedStoryboard?.strId === storyboard.strId
                                                          ? ' ' + classes.sceneImageSelected
                                                          : '')
                                                      }>
                                                      <Text c="red">画面绘制失败</Text>

                                                      <Button
                                                        variant="primary"
                                                        onClick={() => {
                                                          handleRegeneratePicture(storyboard.strId);
                                                        }}
                                                        mt={10}
                                                        radius="8px">
                                                        重绘画面
                                                      </Button>
                                                    </div>
                                                  </Box>
                                                ) : (
                                                  <Box style={{ position: 'relative' }}>
                                                    {/* 条件渲染：如果分镜有视频则显示video，否则显示图片 */}
                                                    {storyboard.stCurVideo?.strVideo ? (
                                                      <video
                                                        src={storyboard.stCurVideo.strVideo}
                                                        autoPlay={false}
                                                        loop
                                                        muted
                                                        playsInline
                                                        poster={
                                                          (storyboard.stCurPic?.strPicUrl || defaultImg) +
                                                          '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p'
                                                        }
                                                        onMouseEnter={(e: any) => {
                                                          void e.target.play().catch((err: any) => {
                                                            console.log('视频播放失败', err);
                                                          });
                                                        }}
                                                        onMouseLeave={(e: any) => {
                                                          void e.target.pause();
                                                          e.target.currentTime = 0;
                                                        }}
                                                        className={
                                                          classes.sceneImage +
                                                          (selectionState.selectedStoryboard?.strId === storyboard.strId
                                                            ? ' ' + classes.sceneImageSelected
                                                            : '')
                                                        }
                                                      />
                                                    ) : (
                                                      <Image
                                                        src={
                                                          storyboard.stCurPic?.strPicUrl
                                                            ? storyboard.stCurPic?.strPicUrl +
                                                              '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p'
                                                            : defaultImg
                                                        }
                                                        className={`${classes.sceneImage} ${
                                                          selectionState.selectedStoryboard?.strId === storyboard.strId
                                                            ? classes.sceneImageSelected
                                                            : ''
                                                        }`}
                                                        alt={`分镜 ${storyboardIndex + 1}`}
                                                      />
                                                    )}

                                                    {selectionState.selectedStoryboard?.strId === storyboard.strId && (
                                                      <>
                                                        <ActionIcon
                                                          size="xs"
                                                          variant="transparent"
                                                          radius="xl"
                                                          className={classes.deleteSceneImage}
                                                          onClick={e => {
                                                            e.stopPropagation();
                                                            confirm({
                                                              title: '提示',
                                                              description: '确定删除该分镜吗？',
                                                              onConfirm: () => {
                                                                handleStoryboardOperation(
                                                                  emEditType.EM_EDIT_STORYBOARD_TYPE_DELETE,
                                                                  storyboard
                                                                );
                                                              },
                                                            });
                                                          }}>
                                                          <CloseIcon />
                                                        </ActionIcon>

                                                        {/* 添加分镜 */}
                                                        <Box
                                                          className={cx(classes.addSceneImage, {
                                                            [classes.disabledButton]: isButtonLoading,
                                                          })}
                                                          onClick={e => {
                                                            e.stopPropagation();
                                                            console.log('[isButtonLoading]:', isButtonLoading);

                                                            if (!isButtonLoading) {
                                                              setIsAddBoardModalOpen(true);
                                                              setAddBoardStoryboard({
                                                                ...newEmptyStoryboard,
                                                                strSceneId: scene.strId,
                                                              });
                                                              // 更新选择状态
                                                              updateSelectionState({
                                                                selectedStoryboard: {
                                                                  ...newEmptyStoryboard,
                                                                  strSceneId: scene.strId,
                                                                } as $StoryboardInfo,
                                                              });
                                                            }
                                                          }}>
                                                          <div
                                                            style={{
                                                              position: 'relative',
                                                              width: '24px',
                                                              height: '24px',
                                                            }}>
                                                            <AddIcon />
                                                            {isButtonLoading && (
                                                              <div className={classes.spinnerOverlay}>
                                                                <div className={classes.spinner}></div>
                                                              </div>
                                                            )}
                                                          </div>
                                                        </Box>
                                                      </>
                                                    )}
                                                    {storyboard.stCurVideo?.strVideo &&
                                                    storyboard.stCurVideo?.eStatus === emStatus.EM_STATUS_SUCC ? (
                                                      <Text className={classes.videoGenerated}>
                                                        <svg
                                                          width="8"
                                                          height="9"
                                                          viewBox="0 0 8 9"
                                                          fill="none"
                                                          xmlns="http://www.w3.org/2000/svg">
                                                          <path
                                                            d="M7.5 3.63397C8.16667 4.01888 8.16667 4.98113 7.5 5.36603L2.25 8.39711C1.58333 8.78201 0.750001 8.30089 0.750001 7.53109L0.750001 1.46891C0.750001 0.699111 1.58333 0.217985 2.25 0.602886L7.5 3.63397Z"
                                                            fill="white"
                                                          />
                                                        </svg>
                                                        {storyboard.iTimeRange
                                                          ? `${Math.floor(storyboard.iTimeRange / 60000)
                                                              .toString()
                                                              .padStart(
                                                                2,
                                                                '0'
                                                              )}:${(Math.floor(storyboard.iTimeRange / 1000) % 60).toString().padStart(2, '0')}`
                                                          : '00:00'}
                                                      </Text>
                                                    ) : (
                                                      <div className={classes.imageIcon}></div>
                                                    )}
                                                  </Box>
                                                )}
                                              </div>
                                            )}
                                          </Draggable>
                                        )
                                      )}
                                    </>
                                  ) : (
                                    <Box
                                      className={classes.addStoryboardButton}
                                      onClick={() => {
                                        if (!isLoading) {
                                          setAddBoardStoryboard({ ...newEmptyStoryboard, strSceneId: scene.strId });
                                          // 更新选择状态
                                          updateSelectionState({
                                            selectedStoryboard: {
                                              ...newEmptyStoryboard,
                                              strSceneId: scene.strId,
                                            } as $StoryboardInfo,
                                          });
                                          setIsAddBoardModalOpen(true);
                                        }
                                      }}>
                                      <LoadingOverlay
                                        zIndex={10}
                                        visible={isLoading}
                                        overlayProps={{ radius: 'sm', blur: 2 }}
                                      />
                                      <Flex direction="column" align="center" justify="center" h="100%">
                                        <AddIcon />
                                        <Text size="sm" mt="xs">
                                          添加分镜
                                        </Text>
                                      </Flex>
                                    </Box>
                                  )}

                                  {provided.placeholder}
                                </div>
                              )}
                            </Droppable>
                          )}
                        </div>
                      );
                    }}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </ScrollArea>
      <AddSceneModal
        opened={isAddSceneModalOpen}
        onClose={() => {
          setIsAddSceneModalOpen(false);
          setTargetSceneIndex(null); // 关闭时重置
        }}
        onSubmit={handleConfirmAddScene}
        isLoading={isCreatingScene}
      />
    </>
  );
};

export default LeftSide;
