import { TimelineAction, TimelineRow, TimelineState } from '@xzdarcy/react-timeline-editor';
import { VisibleSprite } from '@webav/av-cliper';
import React from 'react';
import {
  emStatus,
  $StoryboardInfo,
  $SceneInfo,
  $VideoInfo,
  $SubtitleItem,
  $AudioItem,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { $MaterialItem, emEditType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';

// 扩展TimelineAction类型，添加额外属性
export type TLActionWithName = TimelineAction & {
  name?: string;
  type?: string;
  playDuration?: number; // 实际播放时长 单位毫秒 整数
  originalStartTs?: number; // 原始开始时间 单位毫秒 整数
  originalEndTs?: number; // 原始结束时间 单位毫秒  整数
  originalDuration?: number; // 视频本身的固有时长 单位毫秒 整数 ，这个数据不可变，是视频数据本身的固定属性
  newStartTs?: number; // 视频播放的开始时间，新开始时间（跟originalStartTs相对） 单位毫秒 整数
  newEndTs?: number; // 视频播放结束时间 新结束时间 单位毫秒 整数  视频播放结束时间-视频播放的开始时间=playDuration
  status?: emStatus; // 视频生成状态
  videoStatus?: number; // 视频生成状态：0未开始，1生成中，2成功，3失败
  videoUrl?: string; // 视频URL
  isPlaceholder?: boolean; // 是否是占位图
};

// 扩展TimelineRow类型以支持name属性
export interface CustomTimelineRow extends TimelineRow {
  name?: string;
  actions: TLActionWithName[];
}

// 新增：选择状态接口 - 用于管理当前选中的分镜和对应的视频/音频等元素
export interface SelectionState {
  // 当前选中的分镜
  selectedStoryboard: $StoryboardInfo | null;
  // 当前选中的时间轴动作
  activeTimelineAction: TLActionWithName | null;
}

// TimelineEditor组件的props类型定义
export interface TimelineEditorProps {
  timelineData: CustomTimelineRow[];
  timelineState: React.MutableRefObject<TimelineState | undefined>;
  onPreviewTime: (time: number) => void;
  onOffsetChange: (action: TLActionWithName) => void;
  onDurationChange: (args: { action: TLActionWithName; start: number; end: number }) => void;
  onOpenLyricEditor: (action: TLActionWithName) => void;
  onDeleteAction?: (action: TLActionWithName) => void;
  onSplitAction?: (action: TLActionWithName) => void;
  activeAction: TLActionWithName | null;
  setActiveAction: (action: TLActionWithName | null) => void;
  actionSpriteMap: WeakMap<TimelineAction, VisibleSprite>;
  playing: boolean;
  onPlayPause: () => void;
}

// 修改: 将参数类型定义为元组，方便函数签名使用
export type StoryboardOperationParams = [
  operationType: emEditType,
  storyboard: $StoryboardInfo | $StoryboardInfo[],
  stMaterial?: $MaterialItem | null,
];

// 定义函数类型
export type StoryboardOperationHandler = (...args: StoryboardOperationParams) => Promise<void> | void;
