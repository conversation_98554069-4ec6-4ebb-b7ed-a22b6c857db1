import React, { useState } from 'react';
import { SimpleGrid, BackgroundImage, ActionIcon, Button, Group, Text, Box } from '@mantine/core';
import { IconEdit, IconPlus, IconX } from '@tabler/icons-react';
import {
  $RoleInfo,
  $StoryboardInfo,
  emStatus,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import subjectStyles from '../Subject/index.module.less'; // Assuming styles are general enough or adjust path
import EditRole from '../Subject/EditRole'; // 引入 EditRole 组件
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import useConfirm from '../ConfirmationContext';
import { notifications } from '@mantine/notifications';
import { emScriptGetMask } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';

interface RoleManagerProps {
  storyboard?: $StoryboardInfo;
  rolesToDisplay: $RoleInfo[];
  selectedRoleIds?: string[];
  title?: React.ReactNode;
  canAdd?: boolean;
  canDel?: boolean;
  canEdit?: boolean;
  canSelect?: boolean;
  onRoleClick?: (role: $RoleInfo, isCurrentlySelected: boolean) => void;
  onRoleListChange?: () => void; // 新增回调，用于通知父组件角色列表可能已更新
  gridCols?: number;
  emptyText?: string;
  showRoleSelectTab?: boolean; // 是否显示角色列表（仅当前脚本选中使用了的角色，不是全部角色）选择tab
}

export const RoleManager: React.FC<RoleManagerProps> = ({
  storyboard,
  rolesToDisplay,
  selectedRoleIds = [],
  title = '角色',
  canAdd = true,
  canDel = false,
  canEdit = true,
  canSelect = false,
  onRoleClick,
  onRoleListChange, // 新增
  gridCols = 3,
  emptyText = '暂无角色',
  showRoleSelectTab = false,
}) => {
  const { updateStoryboardRoles, fetchScriptInfoOnce } = useEditorStore.getState();
  const [showRoleEditor, setShowRoleEditor] = useState(false);
  const [currentEditingRole, setCurrentEditingRole] = useState<$RoleInfo | null>(null);
  const confirm = useConfirm();
  const handleInternalAddClick = () => {
    setCurrentEditingRole(null);
    setShowRoleEditor(true);
  };

  const handleInternalEditClick = (role: $RoleInfo) => {
    setCurrentEditingRole(role);
    setShowRoleEditor(true);
  };

  const handleEditRoleClose = () => {
    setShowRoleEditor(false);
    setCurrentEditingRole(null);
    if (onRoleListChange) {
      onRoleListChange(); // 通知父组件角色列表可能已更新
    }
  };

  return (
    <>
      <Box mt="md" mb="md">
        {(title || canAdd) && (
          <Group justify="space-between" mb="xs">
            {title && (typeof title === 'string' ? <Text fw={500}>{title}</Text> : title)}
            {canAdd && (
              <Button
                variant="lite"
                size="xs"
                onClick={() => {
                  handleInternalAddClick();
                }}>
                <IconPlus stroke={1.5} size={14} /> 添加
              </Button>
            )}
          </Group>
        )}

        {rolesToDisplay.length > 0 ? (
          <SimpleGrid cols={gridCols} spacing="sm" verticalSpacing="sm">
            {rolesToDisplay.map((role, index) => {
              const isSelected = selectedRoleIds.includes(role.strId);

              return (
                <BackgroundImage
                  key={role.strId || index}
                  src={role.strUrl + '?imageMogr2/format/webp|imageMogr2/crop/180x180/gravity/north'}
                  onClick={e => {
                    if (canSelect && onRoleClick) {
                      e.stopPropagation();
                      onRoleClick(role, isSelected);
                    }
                  }}
                  className={`${subjectStyles.roleCard} ${canSelect && isSelected ? subjectStyles.roleCardSelected : ''}`}
                  style={{ cursor: canSelect && onRoleClick ? 'pointer' : 'default' }}>
                  {canEdit && (
                    <ActionIcon
                      className={subjectStyles.roleEditBtn}
                      size="sm"
                      disabled={role.eStatus === emStatus.EM_STATUS_RUNNING}
                      onClick={e => {
                        e.stopPropagation();
                        handleInternalEditClick(role);
                      }}>
                      <IconEdit size={12} />
                    </ActionIcon>
                  )}
                  {canDel && storyboard && (
                    <ActionIcon
                      className={subjectStyles.roleDeleteBtn}
                      size="sm"
                      onClick={e => {
                        e.stopPropagation();
                        confirm({
                          title: '解除分镜绑定角色',
                          description: '确定要把该角色从当前分镜中移除吗？',
                          onConfirm: () => {
                            updateStoryboardRoles(storyboard, role, false)
                              .catch(error => {
                                console.error('解除分镜绑定角色失败:', error);
                                notifications.show({
                                  title: '解除分镜绑定角色失败',
                                  message: '请稍后重试',
                                  color: 'red',
                                });
                              })
                              .finally(() => {
                                fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL);
                              });
                          },
                        });
                      }}>
                      <IconX size={12} />
                    </ActionIcon>
                  )}
                </BackgroundImage>
              );
            })}
          </SimpleGrid>
        ) : (
          <Text c="dimmed" size="sm">
            {emptyText}
          </Text>
        )}
      </Box>

      {showRoleEditor && (
        <EditRole
          initialData={currentEditingRole}
          storyboard={storyboard}
          onClose={handleEditRoleClose}
          showRoleSelectTab={showRoleSelectTab}
        />
      )}
    </>
  );
};
