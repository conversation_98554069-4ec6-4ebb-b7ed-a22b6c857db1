import React, { useCallback, useRef } from 'react';
import { Stack, Text, LoadingOverlay, Title, Image, Group, Button, Box, Textarea } from '@mantine/core';
import {
  $StoryboardInfo,
  emStatus,
  eVideoModelType,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { $VideoInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { SelectionState, StoryboardOperationHandler } from './types';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import { ModelSelect } from './modalSelect';
import { defaultImg } from 'common/utils';
import { $WebScriptInfo, emEditType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { CameraOptions } from './cameraOptions';
import appcss from 'projects/lens-editor/App.less';

import MaterialLibraryModal from './MaterialLibraryModal';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus } from '@tabler/icons-react';

const MemoizedModelSelect = React.memo(ModelSelect);

interface VideoTabProps {
  selectionState: SelectionState;
  updateSelectionState: (newState: Partial<SelectionState>) => void;
  historyVideo: $VideoInfo[];
  showPrompt: boolean;
  regenerateVideo: () => void;
  isModelChanged: boolean;
  setIsModelChanged: (value: boolean) => void;
  allStoryboards: $StoryboardInfo[];
  setShowPrompt?: (value: boolean) => void;
  videoTipsInput: string;
  setIsMovementType: (value: boolean) => void;
  setVideoTipsInput: (value: string) => void;
  isMovementType: boolean;
  handleStoryboardOperation: StoryboardOperationHandler;
  selectedMedia: $VideoInfo | null;
  mediaType: string | null;
}

// 修改渲染视频Tab内容函数，确保在视频状态变化时正确显示
export const VideoTab = ({
  selectionState,
  updateSelectionState,
  historyVideo,
  showPrompt,
  regenerateVideo,
  isModelChanged,
  setIsModelChanged,
  allStoryboards,
  setShowPrompt,
  videoTipsInput,
  setIsMovementType,
  setVideoTipsInput,
  isMovementType,
  handleStoryboardOperation,
  selectedMedia,
  mediaType,
}: VideoTabProps) => {
  const { scriptInfo, setScriptInfo, basicConfig, scriptId } = useEditorStore();
  const { selectedStoryboard } = selectionState;
  const [materialLibraryOpened, { open: openMaterialLibrary, close: closeMaterialLibrary }] = useDisclosure(false);

  // 添加防重复引用
  const updateInProgressRef = useRef(false);

  const handleModelChange = useCallback(
    (value: string | null) => {
      if (!value) return; // 提前返回，不处理null值

      const { selectedStoryboard } = selectionState;
      if (!selectedStoryboard || !selectedMedia) return;

      console.log('handleModelChange', value);

      // 创建正确的更新结构
      const updatedVideo = {
        ...selectedMedia,
        strVideoModelId: value,
      };

      const updatedStoryboard = {
        ...selectedStoryboard,
        stCurVideo: updatedVideo,
      };

      setIsModelChanged(true);

      // 更新全局状态
      const updatedStoryboards = allStoryboards.map(sb =>
        sb.strId === selectedStoryboard.strId ? updatedStoryboard : sb
      );

      // 更新选中的分镜
      updateSelectionState({
        selectedStoryboard: updatedStoryboard,
      });

      const currentModelId = updatedVideo.strVideoModelId || '';
      const currentModel = basicConfig?.vecViMod?.find(model => model.strId === currentModelId);
      setIsMovementType(currentModel?.eType === eVideoModelType.EM_VIDEO_MODEL_TYPE_MOVEMENT);
      if (setShowPrompt) {
        setShowPrompt(!!currentModel?.iNeedTips);
      }

      // 更新全局状态
      setScriptInfo({
        ...scriptInfo,
        VecNewStoryboard: updatedStoryboards,
      } as $WebScriptInfo);
    },
    [
      selectionState,
      allStoryboards,
      scriptInfo,
      scriptId,
      setScriptInfo,
      basicConfig,
      updateSelectionState,
      selectedMedia,
      setIsModelChanged,
      setIsMovementType,
      setShowPrompt,
    ]
  );

  if (!selectedStoryboard) {
    return <Text>未找到相关分镜信息</Text>;
  }

  // 点击历史记录切换视频
  const handleHistoryVideoClick = (historyItem: $VideoInfo) => {
    if (!selectedStoryboard || !scriptId) return;

    // 防止重复处理
    if (updateInProgressRef.current) return;
    updateInProgressRef.current = true;

    // 确保selectedMedia非空并且有strId
    const storyboard = allStoryboards.find(sb => sb.strId === selectedStoryboard.strId);
    console.log('[storyboard]:', selectedMedia, storyboard, historyItem, allStoryboards);

    // 如果找不到对应的分镜，重置标志并返回
    if (!storyboard) {
      updateInProgressRef.current = false;
      return;
    }

    // 更新分镜的当前视频为历史视频
    const updatedStoryboard = {
      ...storyboard,
      stCurVideo: historyItem,
    };

    // 更新选中的分镜
    updateSelectionState({
      selectedStoryboard: updatedStoryboard,
    });

    handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_VIDEO, {
      ...storyboard,
      stCurVideo: historyItem,
    });
    updateInProgressRef.current = false;
  };

  // 修改当前选中视频的运镜方式
  const handleCameraChange = (cameraId: string) => {
    console.log('[cameraId]:', cameraId);
    const { selectedStoryboard } = selectionState;

    // 确保已选中分镜且有脚本ID
    if (!selectedStoryboard || !selectedMedia || !scriptId) {
      console.log('没有选中视频或脚本ID不存在');
      return;
    }

    // 创建更新后的视频信息（只更新运镜ID）
    const updatedVideo = {
      ...selectedMedia,
      strMovementId: cameraId,
    };

    // 创建更新后的分镜信息
    const updatedStoryboard = {
      ...selectedStoryboard,
      stCurVideo: updatedVideo,
    };

    setIsModelChanged(true);

    // 更新全局状态
    const updatedStoryboards = allStoryboards.map(sb =>
      sb.strId === selectedStoryboard.strId ? updatedStoryboard : sb
    );

    // 更新选中的分镜
    updateSelectionState({
      selectedStoryboard: updatedStoryboard,
    });

    // 更新全局状态
    setScriptInfo({
      ...scriptInfo,
      VecNewStoryboard: updatedStoryboards,
    } as $WebScriptInfo);
  };

  // 抽取视频控制面板渲染为单独函数，避免重复代码
  const renderVideoControls = (video: $VideoInfo) => {
    return (
      <>
        <Title order={3} mb="sm" size={'md'}>
          视频模型
        </Title>

        <MemoizedModelSelect
          type="video"
          value={video.strVideoModelId || ''}
          onChange={handleModelChange}
          disabled={video.eStatus === emStatus.EM_STATUS_RUNNING}
        />

        {/* 只有运镜类型才显示运镜选项 */}
        {isMovementType && (
          <>
            <Title order={3} mt="md" mb="sm" size="md">
              运镜方式
            </Title>
            <CameraOptions data={video} onCameraChange={handleCameraChange} />
          </>
        )}

        {showPrompt && (
          <>
            <Title order={3} mt="md" mb="sm" size="md">
              视频描述提示词
            </Title>

            <Textarea
              mt="sm"
              radius="md"
              placeholder="请输入视频描述提示词"
              minRows={4}
              maxRows={8}
              autosize={true}
              value={videoTipsInput}
              onChange={e => {
                setVideoTipsInput(e.currentTarget.value);
                setIsModelChanged(true);
              }}
            />
          </>
        )}

        <Group grow wrap="nowrap" mt="md">
          <Button variant="primary" onClick={regenerateVideo} loading={video.eStatus === emStatus.EM_STATUS_RUNNING}>
            生成视频 {isModelChanged ? '*' : ''}
          </Button>
        </Group>

        <Title
          order={3}
          mt="md"
          size="md"
          style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          历史记录
          <Button
            variant="lite"
            size="xs"
            onClick={() => {
              // 打开素材库选择
              openMaterialLibrary();
            }}>
            <IconPlus stroke={1.5} size={14} /> 从素材库选择
          </Button>
        </Title>

        <Stack gap="md" justify="center" align="center">
          {historyVideo.map(item => (
            <Box
              key={item.strId}
              style={{
                cursor: 'pointer',
                borderRadius: '8px',
                overflow: 'hidden',
                width: 'calc(100% - 16px)',
                maxWidth: '272px',
                height: 'auto',
                aspectRatio: '16/9',
                position: 'relative',
              }}
              className={selectedMedia?.strId === item.strId ? appcss.selectedItem : ''}
              onClick={() => handleHistoryVideoClick(item)}>
              {item.strVideo ? (
                <video
                  src={item.strVideo}
                  autoPlay={false}
                  loop
                  muted
                  playsInline
                  poster={item.strPicUrl + '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p'}
                  onMouseEnter={(e: any) => {
                    void e.target.play().catch((err: any) => {
                      console.log('视频播放失败', err);
                    });
                  }}
                  onMouseLeave={(e: any) => {
                    void e.target.pause();
                    e.target.currentTime = 0;
                  }}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                  }}
                />
              ) : (
                <Image
                  src={(item.strPicUrl || defaultImg) + '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p'}
                  alt="video-editor"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '8px',
                  }}
                />
              )}
            </Box>
          ))}
        </Stack>
        {/* 素材库弹框 */}
        <MaterialLibraryModal
          opened={materialLibraryOpened}
          onClose={closeMaterialLibrary}
          selectedStoryboard={selectedStoryboard}
          handleStoryboardOperation={handleStoryboardOperation}
        />
      </>
    );
  };
  // 从最新的scriptInfo中获取当前分镜的最新状态
  const latestStoryboard =
    scriptInfo?.VecNewStoryboard?.find(sb => sb.strId === selectedStoryboard.strId) || selectedStoryboard;

  const isGeneratingVideo =
    latestStoryboard.stCurVideo && latestStoryboard.stCurVideo.eStatus === emStatus.EM_STATUS_RUNNING; // eStatus为1表示生成中

  // 修改当前选中视频的视频状态相关逻辑
  if (isGeneratingVideo) {
    return (
      <Stack align="center" justify="center" h={300}>
        <LoadingOverlay visible={true} overlayProps={{ radius: 'sm', blur: 2 }} />
        <Text c="dimmed">视频生成中，请稍候...</Text>
      </Stack>
    );
  }

  // 视频已生成，显示正常的视频参数配置界面
  if (!selectedMedia || mediaType !== 'video') {
    // 如果没有选中媒体或媒体类型不是视频，直接使用latestStoryboard的当前视频
    if (latestStoryboard.stCurVideo) {
      // 直接使用latestStoryboard.stCurVideo作为video
      const video = latestStoryboard.stCurVideo;
      return renderVideoControls(video);
    } else {
      return <Text>视频信息加载中...</Text>;
    }
  }

  // 正常情况：有选中的媒体且是视频类型
  return renderVideoControls(selectedMedia);
};
