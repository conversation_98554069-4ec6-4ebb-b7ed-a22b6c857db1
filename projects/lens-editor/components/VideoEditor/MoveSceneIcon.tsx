import React from 'react';

export const MoveBottomIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
    <path
      d="M2 14H14V12.6H2V14ZM11.442 8.25L8.6842 11.1584C8.5131 11.3335 8.2715 11.4473 8 11.4473C7.7285 11.4473 7.487 11.3335 7.3187 11.1566L4.5551 8.2521C4.4109 8.0955 4.319 7.8932 4.319 7.6689C4.319 7.1874 4.7312 6.797 5.2393 6.797C5.2464 6.797 5.2533 6.7979 5.2603 6.7981V6.7926H6.5V2H9.5V6.7926H10.7061V6.7996C10.7243 6.7986 10.7422 6.7968 10.7608 6.7968C11.269 6.7968 11.6811 7.1873 11.6811 7.6689C11.6811 7.8932 11.5891 8.0954 11.442 8.25Z"
      fill="currentColor"
    />
  </svg>
);

export const MoveTopIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    style={{ transform: 'rotate(180deg)' }}>
    <path
      d="M2 14H14V12.6H2V14ZM11.442 8.25L8.6842 11.1584C8.5131 11.3335 8.2715 11.4473 8 11.4473C7.7285 11.4473 7.487 11.3335 7.3187 11.1566L4.5551 8.2521C4.4109 8.0955 4.319 7.8932 4.319 7.6689C4.319 7.1874 4.7312 6.797 5.2393 6.797C5.2464 6.797 5.2533 6.7979 5.2603 6.7981V6.7926H6.5V2H9.5V6.7926H10.7061V6.7996C10.7243 6.7986 10.7422 6.7968 10.7608 6.7968C11.269 6.7968 11.6811 7.1873 11.6811 7.6689C11.6811 7.8932 11.5891 8.0954 11.442 8.25Z"
      fill="currentColor"
    />
  </svg>
);

export const MoveUpIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="6"
    height="9"
    viewBox="0 0 6 9"
    fill="none"
    style={{ transform: 'rotate(-90deg)' }}>
    <path
      d="M1.43225 1.28259L4.79052 4.16593C5.12618 4.45412 5.12618 4.97362 4.79052 5.2618L1.43225 8.14514"
      stroke="currentColor"
      strokeWidth="1.08327"
      strokeLinecap="round"
    />
  </svg>
);

export const MoveDownIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="6"
    height="9"
    viewBox="0 0 6 9"
    fill="none"
    style={{ transform: 'rotate(90deg)' }}>
    <path
      d="M1.43225 1.28259L4.79052 4.16593C5.12618 4.45412 5.12618 4.97362 4.79052 5.2618L1.43225 8.14514"
      stroke="currentColor"
      strokeWidth="1.08327"
      strokeLinecap="round"
    />
  </svg>
);

// 新增的分镜展开/收起图标
export const StoryboardExpandIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 14L12 9L17 14" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const StoryboardCollapseIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 10L12 15L17 10" stroke="#fff" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);
