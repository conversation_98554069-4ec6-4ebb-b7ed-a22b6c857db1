import { VisibleSprite, AudioClip } from '@webav/av-cliper';
import { TLActionWithName } from '../types';

// /**
//  * 处理音频数据，转换为时间轴动作
//  * @param scriptInfo 脚本信息
//  * @param avCvs 画布实例
//  * @param actionSpriteMap 动作与Sprite的映射
//  * @returns 音频动作数组
//  */
// export const processAudioData = async (
//   scriptInfo: any,
//   avCvs: any,
//   actionSpriteMap: WeakMap<any, VisibleSprite>
// ): Promise<TLActionWithName[]> => {
//   const audioActions: TLActionWithName[] = [];

//   if (!scriptInfo || !scriptInfo.stAudio || !scriptInfo.stAudio.vecAudio) {
//     return audioActions;
//   }

//   // 加载所有音频轨道
//   for (const track of scriptInfo.stAudio.vecAudio) {
//     if (track.vecContent) {
//       for (const audioItem of track.vecContent) {
//         console.log('[audioItem]:', audioItem);
//         if (audioItem.strAudioUrl) {
//           try {
//             const audioUrl = audioItem.strAudioUrl.replace('http://', 'https://');
//             const audioStream = (await fetch(audioUrl)).body!;

//             // 计算时间范围
//             const trackStartTime = audioItem.iStartTimeMs || 0;
//             const trackEndTime = audioItem.iStopTimeMs || trackStartTime + (audioItem.iAudioDuration || 3000); // 轨道上的结束时间

//             // 音频本身的起始时间
//             const audioStartTime = audioItem.iAudioStartTimeMs || 0; // 音频内容的开始时间
//             const audioEndTime = audioItem.iAudioStopTimeMs || audioStartTime + (audioItem.iAudioDuration || 3000); // 音频内容的结束时间
//             const audioDuration = audioEndTime - audioStartTime; // 要播放的音频长度

//             // 处理音频剪辑
//             const finalAudioClip = await processAudioClip(audioStream, audioStartTime);

//             const sprite = new VisibleSprite(finalAudioClip);
//             sprite.time.offset = trackStartTime * 1000; // 轨道位置（微秒）
//             sprite.time.duration = audioDuration * 1000; // 播放时长（微秒）

//             // 添加到画布
//             await avCvs.addSprite(sprite);

//             // 创建时间轴动作 - 使用轨道时间确定位置
//             const action = {
//               id: audioItem.strId || Math.random().toString(),
//               start: trackStartTime / 1000, // 轨道开始位置（秒）
//               end: trackEndTime / 1000, // 轨道结束位置（秒）
//               effectId: '',
//               flexible: false,
//               movable: false,
//               type: 'audio',
//             };

//             // 保存映射关系
//             actionSpriteMap.set(action, sprite);

//             // 添加到轨道
//             audioActions.push(action);
//           } catch (error) {
//             console.error('加载音频失败', error);
//           }
//         }
//       }
//     }
//   }

//   return audioActions;
// };

// /**
//  * 处理音频剪辑，根据开始时间对音频进行剪切
//  */
// async function processAudioClip(audioStream: ReadableStream, audioStartTime: number): Promise<AudioClip> {
//   // 创建音频元素，指定从audioStartTime开始播放
//   const audioClip = new AudioClip(audioStream, {
//     loop: false,
//     volume: 1.0,
//   });

//   // 使用split方法创建一个从audioStartTime开始的新AudioClip
//   let finalAudioClip: any = audioClip;
//   if (audioStartTime > 0) {
//     try {
//       // split方法返回两个剪辑：[0到splitTime, splitTime到结束]
//       // 我们只需要第二个剪辑部分
//       const splitClips = await audioClip.split(audioStartTime * 1000); // 转换为微秒
//       if (splitClips && splitClips.length === 2) {
//         finalAudioClip = splitClips[1]; // 使用从audioStartTime开始的部分
//       }
//     } catch (error) {
//       console.error('分割音频失败', error);
//       // 如果split失败，继续使用原始音频
//     }
//   }

//   return finalAudioClip;
// }

/**
 * 优化版音频处理函数，利用缓存减少重复请求
 * @param scriptInfo 脚本信息
 * @param avCvs 画布实例
 * @param timelineData 现有时间轴数据
 * @param actionSpriteMap 动作与Sprite的映射
 * @param audioCache 音频缓存
 * @returns 音频动作数组
 */
export const processOptimizedAudioData = async (
  scriptInfo: any,
  avCvs: any,
  timelineData: any[],
  actionSpriteMap: WeakMap<any, VisibleSprite>,
  audioCache: Map<string, AudioClip>
): Promise<TLActionWithName[]> => {
  console.log('[processOptimizedAudioData - 使用缓存]:', scriptInfo?.stAudio);

  const audioActions: TLActionWithName[] = [];

  if (!scriptInfo || !scriptInfo.stAudio || !scriptInfo.stAudio.vecAudio) {
    return audioActions;
  }

  // 收集当前要处理的所有音频ID和URL
  const currentAudioIds = new Set<string>();
  const currentAudioUrls = new Set<string>();

  // 遍历所有音频轨道和内容，收集ID和URL
  scriptInfo.stAudio.vecAudio.forEach((track: any) => {
    if (track.vecContent) {
      track.vecContent.forEach((audioItem: any) => {
        if (audioItem.strId) {
          currentAudioIds.add(audioItem.strId);
        }
        if (audioItem.strAudioUrl) {
          const audioUrl = audioItem.strAudioUrl.replace('http://', 'https://');
          currentAudioUrls.add(audioUrl);
        }
      });
    }
  });

  // 清理当前处理中音频相关的所有旧sprites
  const actionsToRemove: [TLActionWithName, VisibleSprite][] = [];

  // 遍历旧的TimelineData，找出所有与当前音频ID匹配的action和sprite
  const oldTimelineRows = timelineData || [];
  const audioTrackRow = oldTimelineRows.find((row: any) => row?.id === '1-audio');

  if (audioTrackRow && audioTrackRow.actions) {
    audioTrackRow.actions.forEach((action: any) => {
      if (currentAudioIds.has(action.id)) {
        const sprite = actionSpriteMap.get(action);
        if (sprite) {
          actionsToRemove.push([action, sprite]);
        }
      }
    });
  }

  // 移除找到的旧sprite
  if (actionsToRemove.length > 0) {
    console.log(`[移除旧音频sprite]:`, actionsToRemove.length);
    for (const [action, sprite] of actionsToRemove) {
      try {
        await avCvs.removeSprite(sprite);
        actionSpriteMap.delete(action);
        // 延迟销毁以避免潜在的冲突
        setTimeout(() => {
          try {
            sprite.destroy();
          } catch (e) {
            console.error('销毁音频sprite失败:', e);
          }
        }, 100);
      } catch (e) {
        console.error('移除音频sprite失败:', e);
      }
    }
  }

  // 清理不再需要的缓存音频
  if (audioCache.size > 0) {
    for (const [url, clip] of audioCache.entries()) {
      if (!currentAudioUrls.has(url)) {
        console.log('[清理不再需要的音频缓存]:', url);
        try {
          clip.destroy();
        } catch (e) {
          console.error('销毁音频缓存失败:', e);
        } finally {
          audioCache.delete(url);
        }
      }
    }
  }

  // 加载所有音频轨道
  for (const track of scriptInfo.stAudio.vecAudio) {
    if (track.vecContent) {
      for (const audioItem of track.vecContent) {
        console.log('[处理音频项]:', audioItem);
        if (audioItem.strAudioUrl) {
          try {
            const audioUrl = audioItem.strAudioUrl.replace('http://', 'https://');
            let audioClip;

            // 尝试从缓存获取音频
            if (audioCache.has(audioUrl)) {
              console.log('[从缓存获取音频]:', audioUrl);
              try {
                // 从缓存中克隆音频
                audioClip = await audioCache.get(audioUrl)!.clone();
              } catch (error) {
                console.error('从缓存克隆音频失败，重新获取:', error);
                // 从缓存中移除失败的音频
                try {
                  audioCache.get(audioUrl)?.destroy();
                } catch (e) {
                  console.error('销毁旧音频失败:', e);
                }
                audioCache.delete(audioUrl);

                // 重新获取音频
                const audioStream = (await fetch(audioUrl)).body!;
                audioClip = new AudioClip(audioStream, {
                  loop: false,
                  volume: 1.0,
                });
                // 更新缓存
                audioCache.set(audioUrl, audioClip);
              }
            } else {
              console.log('[首次加载音频]:', audioUrl);
              // 首次加载音频
              const audioStream = (await fetch(audioUrl)).body!;
              audioClip = new AudioClip(audioStream, {
                loop: false,
                volume: 1.0,
              });
              // 添加到缓存
              audioCache.set(audioUrl, audioClip);
            }

            // 计算时间范围
            const trackStartTime = audioItem.iStartTimeMs || 0;
            const trackEndTime = audioItem.iStopTimeMs || trackStartTime + (audioItem.iAudioDuration || 3000); // 轨道上的结束时间

            // 音频本身的起始时间
            const audioStartTime = audioItem.iAudioStartTimeMs || 0; // 音频内容的开始时间
            const audioEndTime = audioItem.iAudioStopTimeMs || audioStartTime + (audioItem.iAudioDuration || 3000); // 音频内容的结束时间
            const audioDuration = audioEndTime - audioStartTime; // 要播放的音频长度

            console.log('[处理音频]:', {
              音频ID: audioItem.strId,
              轨道开始时间: trackStartTime,
              轨道结束时间: trackEndTime,
              音频内容开始时间: audioStartTime,
              音频内容结束时间: audioEndTime,
              播放时长: audioDuration,
              从缓存获取: audioCache.has(audioUrl),
            });

            // 处理音频剪辑 - 注意这里我们能重用缓存的音频但仍然需要处理分割
            let finalAudioClip: any = audioClip;
            if (audioStartTime > 0) {
              try {
                // split方法返回两个剪辑：[0到splitTime, splitTime到结束]
                // 我们只需要第二个剪辑部分
                const splitClips = await audioClip.split(audioStartTime * 1000); // 转换为微秒
                if (splitClips && splitClips.length === 2) {
                  finalAudioClip = splitClips[1]; // 使用从audioStartTime开始的部分
                }
              } catch (error) {
                console.error('分割音频失败', error);
                // 如果split失败，继续使用原始音频
              }
            }

            const sprite = new VisibleSprite(finalAudioClip);
            sprite.time.offset = trackStartTime * 1000; // 轨道位置（微秒）
            sprite.time.duration = audioDuration * 1000; // 播放时长（微秒）

            // 添加到画布
            await avCvs.addSprite(sprite);

            // 创建时间轴动作 - 使用轨道时间确定位置
            const action: TLActionWithName = {
              id: audioItem.strId || Math.random().toString(),
              start: trackStartTime / 1000, // 轨道开始位置（秒）
              end: trackEndTime / 1000, // 轨道结束位置（秒）
              effectId: '',
              flexible: false,
              movable: false,
              type: 'audio',
              originalStartTs: audioStartTime,
              originalEndTs: audioEndTime,
              originalDuration: audioItem.iAudioDuration || 0,
            };

            // 保存映射关系
            actionSpriteMap.set(action, sprite);

            // 添加到轨道
            audioActions.push(action);
          } catch (error) {
            console.error('加载音频失败', error);
          }
        }
      }
    }
  }

  return audioActions;
};
