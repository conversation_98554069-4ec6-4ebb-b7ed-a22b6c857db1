import { VisibleSprite } from '@webav/av-cliper';
import { TLActionWithName, CustomTimelineRow } from '../types';
import { $WebScriptInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { emSubTitleType } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { getTextStyle } from '../getTextStyle';

/**
 * 处理字幕数据，转换为时间轴动作和轨道
 * @param scriptInfo 脚本信息
 * @param avCvs 画布实例
 * @param actionSpriteMap 动作与Sprite的映射
 * @returns 字幕轨道数组
 */
export const processSubtitleData = async (
  scriptInfo: $WebScriptInfo,
  avCvs: any,
  actionSpriteMap: WeakMap<any, VisibleSprite>
): Promise<CustomTimelineRow[]> => {
  const subtitleTracks: CustomTimelineRow[] = [];

  if (!scriptInfo || !scriptInfo.stSubtitle || !scriptInfo.stSubtitle.vecContent) {
    return subtitleTracks;
  }

  // 获取歌曲开始时间偏移量
  const songStartOffset = scriptInfo.stTopic?.stSongInfo?.iStartMs || 0;
  console.log('歌曲开始时间偏移量:', songStartOffset);

  // 只加载歌词字幕轨道，不加载歌曲名和歌手名。
  // enum emSubTitleType{
  //   EM_SUBTITLE_TYPE_SONGNAME  = 1,
  //   EM_SUBTITLE_TYPE_SINGER  = 2,
  //   EM_SUBTITLE_TYPE_LYRIC  = 3,
  // };

  //   struct SubtitleTrack{
  //       0 optional string strId;                        // 轨道 ID
  //       1 optional vector<SubtitleItem> vecContent;     // 歌词/旁白内容
  //   2 optional emSubTitleType eType;				// 文本轨道类型
  //   };

  const track = scriptInfo.stSubtitle.vecContent.find(track => track.eType === emSubTitleType.EM_SUBTITLE_TYPE_LYRIC);
  if (!track) {
    return subtitleTracks;
  }

  // 为每个字幕轨道创建一个新的轨道
  const trackId = `2-subtitle-${track.strId}`;
  const subtitleActions: TLActionWithName[] = [];

  // 处理该轨道的所有字幕
  for (const subtitleItem of track.vecContent) {
    if (subtitleItem.strContent) {
      try {
        const startTime = subtitleItem.iStartTimeMs;
        const endTime = subtitleItem.iStopTimeMs;

        // 只添加在MV时间范围内的字幕
        if (endTime > 0) {
          // 使用传入的函数处理字幕样式
          const sprite = await getTextStyle(subtitleItem);
          // 添加到画布
          await avCvs.addSprite(sprite);

          // 创建时间轴动作
          const action = {
            id: subtitleItem.strId || Math.random().toString(),
            start: startTime / 1000, // 转换为秒
            end: endTime / 1000, // 转换为秒
            effectId: '',
            name: subtitleItem.strContent,
            type: 'subtitle',
          };

          // 保存映射关系
          actionSpriteMap.set(action, sprite);

          // 添加到轨道
          subtitleActions.push(action);
        }
      } catch (error) {
        console.error('加载字幕失败', error);
      }
    }
  }

  // 创建完整的字幕轨道
  subtitleTracks.push({
    id: trackId,
    actions: subtitleActions,
    name: `歌词`,
  });

  console.log('[subtitleTracks]:', subtitleTracks);

  return subtitleTracks;
};
