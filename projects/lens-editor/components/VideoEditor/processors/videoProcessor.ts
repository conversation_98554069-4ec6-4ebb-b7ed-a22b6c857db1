import { ImgClip, VisibleSprite } from '@webav/av-cliper';
import { MP4Clip } from '@webav/av-cliper';
import { CustomTimelineRow, TLActionWithName } from '../types';
import { defaultImg } from 'common/utils';
import { HARDWARE_ACCELERATION, VIDEOHEIGHT, VIDEOWIDTH } from 'projects/lens-editor/constants';
import { $StoryboardInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { useEditorStore, selectAllStoryboards } from 'projects/lens-editor/store/editorStore';
import { processVideoClip } from 'projects/lens-editor/utils/videoUtils';
import { useTimelineStore } from 'projects/lens-editor/store/timelineStore';
import { sortActionsByTime, validateActionsOrder } from 'projects/lens-editor/utils/index';

/**
 * 根据分镜实际情况直接渲染视频或分镜图
 */
export const processVideoData = async (
  storyboards: $StoryboardInfo[],
  avCvs: any,
  timelineData: CustomTimelineRow[],
  actionSpriteMap: WeakMap<any, VisibleSprite>,
  videoCache: Map<string, any>,
  defaultImgCache: Uint8Array | null = null,
  setDefaultImgCache?: (imgData: Uint8Array) => void,
  defaultImgClipCache?: ImgClip | null,
  setDefaultImgClipCache?: (imgClip: ImgClip) => void,
  onVideoLoaded?: (action: TLActionWithName, sprite: VisibleSprite) => void
): Promise<TLActionWithName[]> => {
  const t1 = Date.now();
  console.log('[processVideoData 统一处理视频和分镜图]:', storyboards);
  // const { scriptInfo } = useEditorStore.getState();

  if (!storyboards?.length || !avCvs) {
    return [];
  }

  // 判断是否为单个分镜更新（而非全量重新加载）
  const isSingleStoryboardUpdate = storyboards.length === 1;

  if (isSingleStoryboardUpdate) {
    console.log('[processVideoData] 单个分镜更新，只清理对应的sprite');

    // 单个分镜更新：只清理对应的sprite和映射
    const targetStoryboardId = storyboards[0].strId;
    const oldTimelineRows = timelineData || [];
    const videoTrackRow = oldTimelineRows.find((row: any) => row?.id === '3-video');

    if (videoTrackRow && videoTrackRow.actions) {
      const targetAction = videoTrackRow.actions.find((action: any) => action.id === targetStoryboardId);
      if (targetAction) {
        const targetSprite = actionSpriteMap.get(targetAction);
        if (targetSprite) {
          try {
            await avCvs.removeSprite(targetSprite);
            actionSpriteMap.delete(targetAction);
            setTimeout(() => {
              try {
                targetSprite.destroy();
              } catch (e) {
                console.error('销毁sprite失败:', e);
              }
            }, 100);
            console.log('[processVideoData] 已清理目标分镜的sprite:', targetStoryboardId);
          } catch (e) {
            console.error('移除目标sprite失败:', e);
          }
        }
      }
    }
  } else {
    // 全量重新加载：清理所有sprite和映射
    try {
      console.log('[processVideoData] 全量重新加载，清理所有sprite');
      const wasPlaying = avCvs.playing;
      if (wasPlaying) {
        avCvs.pause();
      }

      const sprites = avCvs.sprites || [];
      for (const sprite of sprites) {
        try {
          await avCvs.removeSprite(sprite);
          setTimeout(() => {
            try {
              sprite.destroy();
            } catch (e) {
              console.error('销毁sprite失败:', e);
            }
          }, 100);
        } catch (e) {
          console.error('移除sprite失败:', e);
        }
      }

      // 清理actionSpriteMap
      const oldTimelineRows = timelineData || [];
      const videoTrackRow = oldTimelineRows.find((row: any) => row?.id === '3-video');
      if (videoTrackRow && videoTrackRow.actions) {
        videoTrackRow.actions.forEach((action: any) => {
          actionSpriteMap.delete(action);
        });
      }
    } catch (e) {
      console.error('清理画布失败:', e);
    }
  }

  // 准备默认图片
  let localImgClipCache: ImgClip | null = defaultImgClipCache || null;
  if (!localImgClipCache) {
    try {
      let imgData: Uint8Array | null = null;
      imgData =
        defaultImgCache ||
        (await (async () => {
          console.log('[首次加载默认图片数据]');
          try {
            const response = await fetch(defaultImg);
            if (!response.ok) {
              throw new Error(`获取默认图片失败: ${response.status} ${response.statusText}`);
            }
            const arrayBuffer = await response.arrayBuffer();
            if (!arrayBuffer || arrayBuffer.byteLength === 0) {
              throw new Error('获取到的图片数据为空');
            }
            const data = new Uint8Array(arrayBuffer);
            if (setDefaultImgCache) {
              setDefaultImgCache(data);
            }
            return data;
          } catch (fetchError) {
            console.error('[获取默认图片失败]:', fetchError);
            return new Uint8Array([
              0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 0x00,
              0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1f, 0x15, 0xc4, 0x89, 0x00,
              0x00, 0x00, 0x0d, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9c, 0x63, 0x60, 0x00, 0x02, 0x00, 0x00, 0x05, 0x00,
              0x01, 0x5a, 0xfa, 0x48, 0x65, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82,
            ]);
          }
        })());

      const blob = new Blob([imgData], { type: 'image/png' });
      const stream = blob.stream();
      localImgClipCache = new ImgClip(stream);
      await new Promise<void>(resolve => setTimeout(resolve, 100));

      if (setDefaultImgClipCache) {
        setDefaultImgClipCache(localImgClipCache);
      }
      console.log('[创建并缓存了新的默认图片ImgClip]');
    } catch (error) {
      console.error('[创建ImgClip失败]:', error);
      return [];
    }
  }

  // 分镜图片缓存
  const storyboardImgClipCache = new Map<string, ImgClip>();
  const videoActions: TLActionWithName[] = [];

  // 并发控制
  const MAX_CONCURRENT = 3;
  const pendingTasks: Array<() => Promise<void>> = [];
  let processing = 0;

  const processNext = () => {
    if (pendingTasks.length === 0 || processing >= MAX_CONCURRENT) return;

    processing++;
    const task = pendingTasks.shift()!;

    task().finally(() => {
      processing--;
      processNext();
    });

    // 尝试处理更多任务
    processNext();
  };

  // 计算每个分镜的开始时间
  const calculateStoryboardTimes = (storyboards: $StoryboardInfo[]) => {
    const times: { [key: string]: { startTime: number; duration: number } } = {};
    let cumulativeTime = 0;

    if (isSingleStoryboardUpdate) {
      // 单个分镜更新：需要计算它在整个时间轴上的位置
      const allStoryboards = selectAllStoryboards(useEditorStore.getState());
      const storyboardIndex = allStoryboards.findIndex(sb => sb.strId === storyboards[0].strId);

      // 遍历之前的所有分镜，累加它们的时长
      for (let i = 0; i < storyboardIndex; i++) {
        const sb = allStoryboards[i];
        let duration = 0;

        // 如果有视频，则使用视频的时长
        if (sb.stCurVideo && sb.stCurVideo.iEndTs && sb.stCurVideo.iStartTs) {
          duration = sb.stCurVideo.iEndTs - sb.stCurVideo.iStartTs;
        } else {
          // 否则使用分镜的时长
          duration = sb.iTimeRange || 4000;
        }

        cumulativeTime += duration;
      }

      // 计算当前分镜的时长
      const currentStoryboard = storyboards[0];
      let playDuration = 0;
      if (currentStoryboard.stCurVideo?.iTimeRange) {
        playDuration = currentStoryboard.stCurVideo.iEndTs - currentStoryboard.stCurVideo.iStartTs;
      } else {
        playDuration = currentStoryboard.iTimeRange || 4000;
      }

      times[currentStoryboard.strId] = {
        startTime: cumulativeTime,
        duration: playDuration,
      };

      console.log('[单个分镜时间计算]:', {
        分镜ID: currentStoryboard.strId,
        索引: storyboardIndex,
        起始时间: cumulativeTime,
        时长: playDuration,
      });
    } else {
      // 全量加载：按顺序计算所有分镜的时间
      console.log('[全量加载时间计算]:', storyboards.length, '个分镜');

      for (const storyboard of storyboards) {
        let playDuration = 0;
        if (storyboard.stCurVideo?.iTimeRange) {
          playDuration = storyboard.stCurVideo.iEndTs - storyboard.stCurVideo.iStartTs;
        } else {
          playDuration = storyboard.iTimeRange || 4000;
        }

        times[storyboard.strId] = {
          startTime: cumulativeTime,
          duration: playDuration,
        };

        cumulativeTime += playDuration;

        console.log('[分镜时间]:', {
          ID: storyboard.strId,
          开始: times[storyboard.strId].startTime,
          时长: playDuration,
          累计: cumulativeTime,
        });
      }
    }

    return times;
  };

  const storyboardTimes = calculateStoryboardTimes(storyboards);

  // 为每个分镜创建处理任务
  for (const storyboard of storyboards) {
    const { startTime, duration: playDuration } = storyboardTimes[storyboard.strId];
    const endTime = startTime + playDuration;
    const originalDuration = storyboard.stCurVideo?.iTimeRange || 4000;

    // 检查是否有可用的视频
    const hasVideo = storyboard.stCurVideo && storyboard.stCurVideo.strVideo && storyboard.stCurVideo.eStatus === 2;

    // 检查是否有可用的分镜图
    const hasPicture = storyboard.stCurPic && storyboard.stCurPic.strPicUrl && storyboard.stCurPic.eStatus === 2;

    // 创建处理任务
    const task = async () => {
      try {
        let sprite: VisibleSprite;

        if (hasVideo) {
          // 处理视频
          // console.log('[加载视频]:', storyboard.stCurVideo.strVideo);
          sprite = await createVideoSprite(storyboard, videoCache, startTime, playDuration);
        } else if (hasPicture) {
          // 处理分镜图
          // console.log('[加载分镜图]:', storyboard.stCurPic.strPicUrl);
          sprite = await createImageSprite(storyboard, storyboardImgClipCache, startTime, playDuration);
        } else {
          // 使用默认图片
          // console.log('[使用默认图片]');
          sprite = await createDefaultSprite(localImgClipCache!, startTime, playDuration);
        }
        console.log('[task]:', avCvs);

        // 添加到画布
        await avCvs.addSprite(sprite);

        // 创建时间轴动作
        const action: TLActionWithName = {
          id: storyboard.strId || Math.random().toString(),
          start: startTime / 1000,
          end: endTime / 1000,
          effectId: '',
          name: '',
          movable: false,
          type: 'video',
          status: hasVideo ? storyboard.stCurVideo.eStatus : hasPicture ? storyboard.stCurPic.eStatus : 2,
          videoUrl: storyboard.stCurVideo?.strVideo || '',
          isPlaceholder: !hasVideo,
          videoStatus: storyboard.stCurVideo?.eStatus || 0,
          playDuration,
          originalStartTs: storyboard.stCurVideo.iStartTs,
          originalEndTs: storyboard.stCurVideo.iEndTs || storyboard.iTimeRange,
          originalDuration,
        };

        // 保存映射关系
        actionSpriteMap.set(action, sprite);
        videoActions.push(action);

        // 回调通知
        if (onVideoLoaded) {
          onVideoLoaded(action, sprite);
        }

        // console.log(`[创建素材完成] ${hasVideo ? '视频' : hasPicture ? '分镜图' : '默认图'}: ${action.id}`, action);
      } catch (error) {
        console.error('处理分镜失败:', error, storyboard.strId);
      }
    };

    pendingTasks.push(task);
  }

  // 开始处理任务队列
  processNext();

  // 等待所有任务完成
  while (pendingTasks.length > 0 || processing > 0) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // 确保结果按时间顺序排列
  const sortedVideoActions = sortActionsByTime(videoActions);

  // 验证排序结果
  if (!validateActionsOrder(sortedVideoActions, 'processVideoData')) {
    console.error('[processVideoData] 排序后仍有问题，这不应该发生');
  } else {
    console.log('[processVideoData] Actions 已正确排序:', sortedVideoActions.length, '个');
  }

  console.log('[processVideoData 统一处理视频和分镜图] 完成', Date.now() - t1);

  return sortedVideoActions;
};

// 创建视频精灵的辅助函数
async function createVideoSprite(
  storyboard: any,
  videoCache: Map<string, any>,
  startTime: number,
  playDuration: number
): Promise<VisibleSprite> {
  const videoUrl = storyboard.stCurVideo.strVideo.replace('http://', 'https://');
  const timelineStore = useTimelineStore.getState();

  // 使用统一的缓存获取视频方法
  let videoClip;
  try {
    videoClip = await timelineStore.getVideoClip(videoUrl);
  } catch (error) {
    console.error('获取视频剪辑失败，尝试备用方式:', error);

    // 备用获取方式，防止意外情况
    const response = await fetch(videoUrl);
    if (!response.ok) {
      throw new Error(`获取视频失败: ${response.status} ${response.statusText}`);
    }
    videoClip = new MP4Clip(response.body!, { __unsafe_hardwareAcceleration__: HARDWARE_ACCELERATION });
    await videoClip.ready;
  }

  // 处理视频剪辑
  const finalVideoClip = await processVideoClip(
    videoClip,
    storyboard.stCurVideo.iStartTs,
    storyboard.stCurVideo.iEndTs,
    storyboard.stCurVideo.iTimeRange
  );

  const sprite = new VisibleSprite(finalVideoClip);
  sprite.time.offset = startTime * 1000; // 转换为微秒
  sprite.time.duration = playDuration * 1000; // 转换为微秒
  sprite.zIndex = 10;

  return sprite;
}

// 创建图片精灵的辅助函数
async function createImageSprite(
  storyboard: any,
  storyboardImgClipCache: Map<string, ImgClip>,
  startTime: number,
  playDuration: number
): Promise<VisibleSprite> {
  let imgClip: ImgClip;

  if (storyboardImgClipCache.has(storyboard.strId)) {
    imgClip = await storyboardImgClipCache.get(storyboard.strId)!.clone();
    console.log('[从缓存获取分镜图片]:', storyboard.strId);
  } else {
    const imageUrl =
      storyboard.stCurPic.strPicUrl.replace('http://', 'https://') +
      '?imageMogr2/format/webp|imageMogr2/crop/' +
      VIDEOWIDTH +
      'x' +
      VIDEOHEIGHT;

    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`获取分镜图片失败: ${response.status} ${response.statusText}`);
    }

    const arrayBuffer = await response.arrayBuffer();
    if (!arrayBuffer || arrayBuffer.byteLength === 0) {
      throw new Error('获取到的图片数据为空');
    }

    const blob = new Blob([arrayBuffer], { type: 'image/jpeg' });
    const stream = blob.stream();
    const newImgClip = new ImgClip(stream);
    await newImgClip.ready;

    storyboardImgClipCache.set(storyboard.strId, newImgClip);
    imgClip = await newImgClip.clone();
  }

  const sprite = new VisibleSprite(imgClip);
  sprite.time.duration = playDuration * 1000; // 转换为微秒
  sprite.time.offset = startTime * 1000; // 转换为微秒

  return sprite;
}

// 创建默认图片精灵的辅助函数
async function createDefaultSprite(
  defaultImgClip: ImgClip,
  startTime: number,
  playDuration: number
): Promise<VisibleSprite> {
  const imgClip = await defaultImgClip.clone();
  const sprite = new VisibleSprite(imgClip);
  sprite.time.duration = playDuration * 1000; // 转换为微秒
  sprite.time.offset = startTime * 1000; // 转换为微秒
  return sprite;
}
