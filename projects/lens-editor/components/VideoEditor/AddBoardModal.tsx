import React, { useEffect, useState, useCallback } from 'react';
import {
  Modal,
  Button,
  Group,
  Box,
  Text,
  LoadingOverlay,
  SimpleGrid,
  ActionIcon,
  Image,
  ScrollArea,
  Tooltip,
} from '@mantine/core';
import {
  $PicInfo,
  $RoleInfo,
  $SceneInfo,
  $VideoInfo,
  $StoryboardInfo,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import {
  $GetMaterialListRsp,
  $MaterialItem,
  emEditType,
  emScriptGetMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { ModelSelect } from './modalSelect';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import req from 'common/request';
import { notifications } from '@mantine/notifications';
import { RoleManager } from './RoleManager';
import { useDisclosure } from '@mantine/hooks';
import { IconExclamationCircle, IconEye } from '@tabler/icons-react';

import videoStyles from './videoEditor.module.less';
import { StoryboardOperationHandler } from './types';
import AIInputField from '../common/AIInputField';

// 为ModelSelect创建一个React.memo版本
const MemoizedModelSelect = React.memo(ModelSelect);

interface AddBoardModalProps {
  opened: boolean;
  onClose: () => void;
  storyboard: $StoryboardInfo; // 当前分镜（克隆用）
  scene: $SceneInfo; // 当前场景
  isLoading: boolean; // 是否正在加载
  handleStoryboardOperation: StoryboardOperationHandler;
}

//   // 分镜信息
//   struct StoryboardInfo{
//     0  optional string            strId;         // 分镜 ID
//     1  optional string            strSceneId;    // 场景ID
//     2  optional string            strStory;      // 分镜故事
//     3  optional emStatus          eStatus;       // 分镜状态
//     4  optional PicInfo           stCurPic;      // 当前使用的照片
//     5  optional vector<PicInfo>   vecPic;        // 历史图片
//     6  optional VideoInfo         stCurVideo;    // 当前使用的视频
//     7  optional vector<VideoInfo> vecVideo;      // 历史生成的视频
//     8  optional vector<string>    vecRoleId;     // 关联的角色 ID
//     9  optional string            strEnvId;      // 环境 ID
//     10 optional int               iStartTime;     // 开始时间
//     11 optional int               iTimeRange;    // 场景时长(ms)
//     12 optional string           strShotType;   // 景别(中景、远景等)
//     13 optional int               iIndex;        // 分镜序号
//     14 optional int              iEndTime;      // 结束时间(ms)
// };

const AddBoardModal: React.FC<AddBoardModalProps> = ({
  opened,
  onClose,
  storyboard,
  scene,
  isLoading,
  handleStoryboardOperation,
}) => {
  const { scriptInfo, scriptId, fetchScriptInfoOnce } = useEditorStore();
  // 表单状态
  const [storyText, setStoryText] = useState('');
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>([]);
  const [modelId, setModelId] = useState(scriptInfo?.stTopic?.stPicModel?.strId || '');
  const [isGeneratingAiStory, setIsGeneratingAiStory] = useState(false);
  const [roleList, setRoleList] = useState<$RoleInfo[]>([]);
  const [selectedMaterial, setSelectedMaterial] = useState<$MaterialItem | null>(null); // 当前选中的素材，单选

  const [previewImageOpened, { open: openPreviewImage, close: closePreviewImage }] = useDisclosure(false);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);

  // 根据分镜文案查询相似图片
  const [similarMaterial, setSimilarMaterial] = useState<$MaterialItem[]>([]);
  // 从脚本中获取角色列表
  const updateRoleList = useCallback(() => {
    if (scriptInfo?.vecRoleInfo) {
      setRoleList(scriptInfo.vecRoleInfo.filter(role => role.iUse === 1));
    }
  }, [scriptInfo]);

  useEffect(() => {
    updateRoleList();
  }, [updateRoleList]);

  const handleRoleListUpdatedByManager = useCallback(() => {
    console.log('Role list potentially updated by RoleManager, fetching latest role info.');
    if (scriptId) {
      fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ROLE, () => {
        // 成功获取后，依赖 scriptInfo 的 useEffect 会自动更新 roleList
        console.log('Successfully fetched script info for roles after update.');
      });
    }
  }, [scriptId, fetchScriptInfoOnce]);

  // 添加表单错误状态
  const [errors, setErrors] = useState<{
    storyText?: string;
    modelId?: string;
  }>({});

  // 重置表单
  const resetForm = () => {
    setStoryText('');
    setSelectedRoleIds([]);
    setModelId('');
    setErrors({});
  };

  const getSimilarMaterial = () => {
    //   // CMD_GET_MATERIAL_LIST
    //   struct GetMaterialListReq{
    //     0 optional string strScriptId;                              // 脚本ID
    //     1 optional string strPicTips;                               // 图片提示词
    // };
    // struct MaterialItem{
    //     0  optional proto_lens_script_comm::VideoInfo stVideoInfo;      // 历史生成的视频
    //     1  optional proto_lens_script_comm::PicInfo stPicInfo;          // 历史生成的视频
    //     2 optional string				materialID;		            	// 对应的素材id。需要在使用素材库图片生成对应视频时，传入
    // };
    // struct GetMaterialListRsp{
    //     0 optional vector<MaterialItem> vecInfo;
    // };
    if (!storyText) {
      notifications.show({
        title: '请输入分镜文案描述',
        message: '请输入分镜文案描述',
        color: 'red',
      });

      return;
    }
    req
      .post('/lens_script/get_material_list', {
        strScriptId: scriptId,
        strPicTips: storyText,
      })
      .then((response: { data: { error_code: number; data: $GetMaterialListRsp; error_msg: string } }) => {
        console.log('[similar images]:', response);
        if (response.data.error_code === 0 && response.data.data.vecInfo) {
          setSimilarMaterial(response.data.data.vecInfo);
        } else {
          notifications.show({
            title: '获取相似图片失败',
            message: response.data.error_msg,
            color: 'red',
          });
        }
      })
      .catch(error => {
        notifications.show({
          title: '获取相似图片失败',
          message: error.message,
          color: 'red',
        });
      });
  };
  // 选择素材
  const handleSelectMaterial = (material: $MaterialItem) => {
    console.log('[use material]:', material);

    if (selectedMaterial?.materialID === material.materialID) {
      setSelectedMaterial(null);
    } else {
      setSelectedMaterial(material);
    }
  };
  // 新增：处理图片点击，打开预览 Modal
  const handleImageClickForPreview = (imageUrl: string) => {
    // 移除图片URL中的缩略图参数，以显示原图
    const originalImageUrl = imageUrl.split('?')[0];
    setImagePreviewUrl(originalImageUrl);
    openPreviewImage();
  };
  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: {
      storyText?: string;
      modelId?: string;
    } = {};

    // 验证分镜文案
    if (!storyText.trim()) {
      newErrors.storyText = '请输入分镜文案描述';
    }

    // 验证模型选择
    if (!modelId) {
      newErrors.modelId = '请选择图片模型';
    }

    setErrors(newErrors);

    // 如果没有错误，返回true
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = () => {
    // 验证表单
    if (!validateForm()) {
      return; // 如果验证失败，停止提交
    }

    // 准备新分镜数据
    const newStoryboard: $StoryboardInfo = {
      ...storyboard,
      strId: '', // 新分镜ID为空，后端会生成
      strSceneId: scene.strId, // 使用当前场景ID
      iIndex: (storyboard.iIndex || 0) + 1, // 新分镜序号
      strStory: storyText, // 分镜文案
      eStatus: 0, // 初始状态
      stCurPic: {
        // 初始化空图片信息
        stUseModelId: modelId, // 用户选择的模型ID
      } as $PicInfo,
      vecPic: [],
      stCurVideo: {} as $VideoInfo,
      vecVideo: [],
      strShotType: '',
      iStartTime: 0,
      iTimeRange: 0,
      iEndTime: 0, // 添加结束时间字段
      vecRoleId: selectedRoleIds, // 存储角色ID列表
    };

    // 提交操作
    handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_ADD, newStoryboard, selectedMaterial);
    console.log('handleSubmit', emEditType.EM_EDIT_STORYBOARD_TYPE_ADD, newStoryboard);
    // 关闭弹窗并重置表单
    onClose();
    resetForm();
  };

  // 处理角色选择
  const handleToggleRoleSelection = (role: $RoleInfo, isCurrentlySelected: boolean) => {
    console.log('[isSelected]:', isCurrentlySelected);
    if (!isCurrentlySelected) {
      // If not selected, add it
      setSelectedRoleIds(prev => [...prev, role.strId]);
    } else {
      // If selected, remove it
      setSelectedRoleIds(prev => prev.filter(id => id !== role.strId));
    }
  };
  console.log('[similarMaterial]:', similarMaterial);

  return (
    <Modal opened={opened} onClose={onClose} title="添加分镜" size="xl" centered>
      <LoadingOverlay visible={isLoading} />
      <Group align="flex-start" wrap="nowrap">
        {/* 图片模型选择 */}
        <Box style={{ minWidth: '320px' }}>
          <Text fw={500} mb="xs">
            图片模型{' '}
            <Text component="span" c="red">
              *
            </Text>
          </Text>
          <MemoizedModelSelect
            type="picture"
            mvTplId={scriptInfo?.stTopic?.stMVTemplate?.strId || ''}
            value={modelId}
            onChange={value => {
              setModelId(value || '');
              if (errors.modelId && value) {
                setErrors(prev => ({ ...prev, modelId: undefined }));
              }
            }}
          />
          {errors.modelId && (
            <Text size="xs" c="red" mt="xs">
              {errors.modelId}
            </Text>
          )}{' '}
          {/* 角色选择 - 使用 RoleManager 组件 */}
          <RoleManager
            rolesToDisplay={roleList}
            selectedRoleIds={selectedRoleIds}
            canAdd
            canEdit
            canSelect
            onRoleClick={handleToggleRoleSelection}
            onRoleListChange={handleRoleListUpdatedByManager}
            emptyText="该作品暂未添加角色，请前往'主题确定'步骤添加角色"
          />
        </Box>

        {/* 分镜文案描述 */}
        <Box style={{ flexGrow: 1 }}>
          {/* 使用AIInputField组件替换原有的输入框和AI文案按钮 */}
          <AIInputField
            type="textarea"
            value={storyText}
            onChange={setStoryText}
            apiPath="/lens_script/edit_storyboard"
            apiField={emEditType.EM_EDIT_STORYBOARD_TYPE_GEN_STORY}
            apiParams={{
              strScriptId: scriptId,
              stBoardInfo: [
                {
                  strSceneId: scene.strId,
                  iIndex: storyboard.iIndex + 1,
                  vecRoleId: selectedRoleIds,
                },
              ],
            }}
            disabled={isGeneratingAiStory}
            textareaProps={{
              placeholder: '请输入分镜文案描述',
              label: '分镜文案描述',
              styles: theme => ({ label: { marginBottom: theme.spacing.xs } }),
              minRows: 4,
              maxRows: 6,
              autosize: true,
              required: true,
              size: 'md',
              error: errors.storyText,
            }}
          />

          <Group gap="xs" mt="xs">
            <Button size="xs" onClick={getSimilarMaterial}>
              查询素材库
            </Button>
            <Tooltip
              w={260}
              multiline
              label="如果选了素材库，则会直接使用选定的素材库素材; 如果未选素材，则会根据分镜文案描述生成。">
              <IconExclamationCircle size={20} />
            </Tooltip>

            {similarMaterial.length > 0 && (
              <ScrollArea h={270}>
                <SimpleGrid cols={2} spacing="sm" verticalSpacing="sm">
                  {similarMaterial.map((image, index) => (
                    <Box
                      key={index}
                      data-id={image.stPicInfo.strId}
                      className={`${videoStyles.imglistItem} ${
                        image.materialID === selectedMaterial?.materialID ? videoStyles.selected : ''
                      }`}>
                      <Image
                        onClick={() => {
                          handleSelectMaterial(image);
                        }}
                        style={{ borderRadius: '4px', cursor: 'pointer' }}
                        src={image.stPicInfo.strPicUrl + '?imageMogr2/format/webp|imageMogr2/thumbnail/!30p'}
                      />
                      <ActionIcon
                        className={videoStyles.previewIcon}
                        onClick={() => {
                          handleImageClickForPreview(image.stPicInfo.strPicUrl);
                        }}>
                        <IconEye size={16} />
                      </ActionIcon>
                    </Box>
                  ))}
                </SimpleGrid>
              </ScrollArea>
            )}
          </Group>
        </Box>
      </Group>
      {/* 操作按钮 */}
      <Group justify="flex-end" mt="xl">
        <Button variant="outline" onClick={onClose} disabled={isLoading}>
          取消
        </Button>
        <Button onClick={handleSubmit} loading={isLoading}>
          确认创建
        </Button>
      </Group>
      {/* 新增：图片预览 Modal */}

      <Modal opened={previewImageOpened} onClose={closePreviewImage} size="80%" centered>
        {imagePreviewUrl && <Image w={'90%'} mx="auto" src={imagePreviewUrl} />}
      </Modal>
    </Modal>
  );
};

export default AddBoardModal;
