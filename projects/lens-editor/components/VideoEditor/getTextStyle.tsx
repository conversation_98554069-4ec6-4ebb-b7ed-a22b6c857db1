import { ImgClip, VisibleSprite, renderTxt2ImgBitmap } from '@webav/av-cliper';
import { $SubtitleItem } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { VIDEOHEIGHT, VIDEOWIDTH } from 'projects/lens-editor/constants';

export const getTextStyle = async (subtitleItem: $SubtitleItem) => {
  const startTime = subtitleItem.iStartTimeMs;
  const endTime = subtitleItem.iStopTimeMs;

  const textImg = await renderTxt2ImgBitmap(
    subtitleItem.strContent,
    `font-size: ${subtitleItem.stParam.fontsize}px; color: ${subtitleItem.stParam.color}; -webkit-text-stroke: ${subtitleItem.stParam.strokeWidth}px ${subtitleItem.stParam.strokeColor}; width:95%; max-width:95%; display:block; white-space:normal; word-break:break-all; word-wrap:break-word; overflow-wrap:break-word; text-align:center;line-height:1`
  );
  // console.log('[textImg]:', textImg);

  const sprite = new VisibleSprite(new ImgClip(textImg));
  sprite.time.offset = startTime * 1000; // 转换为微秒
  sprite.time.duration = (endTime - startTime) * 1000; // 转换为微秒
  sprite.zIndex = 100;

  // 使用setAnimation实现淡入淡出效果
  const crossfadeDuration = subtitleItem.stParam.crossfadeDuration || 0.2; // 默认0.2秒
  const totalDuration = (endTime - startTime) * 1000; // 总时长(微秒)

  // 计算淡入淡出所占百分比
  const fadeInPercent = ((crossfadeDuration * 1000000) / totalDuration) * 100;
  const fadeOutPercent = 100 - fadeInPercent;

  // 设置淡入淡出动画
  sprite.setAnimation(
    {
      '0%': { opacity: 0 }, // 开始时完全透明
      [`${fadeInPercent}%`]: { opacity: 1 }, // 淡入完成时完全不透明
      [`${fadeOutPercent}%`]: { opacity: 1 }, // 开始淡出前保持完全不透明
      '100%': { opacity: 0 }, // 结束时完全透明
    },
    {
      duration: totalDuration, // 动画持续整个字幕显示时间
      iterCount: 1, // 只执行一次
    }
  );

  let x = subtitleItem.stParam.position.split(',')[0];
  let y = subtitleItem.stParam.position.split(',')[1];
  if (x === 'center') {
    x = (VIDEOWIDTH / 2 - textImg.width / 2).toString();
  } else if (x === 'left') {
    x = '0';
  } else if (x === 'right') {
    x = (VIDEOWIDTH - textImg.width).toString();
  } else {
    x = (parseFloat(x) * VIDEOWIDTH).toString();
  }
  sprite.rect.x = parseFloat(x);
  if (y === 'center') {
    y = (VIDEOHEIGHT / 2 - textImg.height / 2).toString();
  } else if (y === 'top') {
    y = '0';
  } else if (y === 'bottom') {
    y = (VIDEOHEIGHT - textImg.height).toString();
  } else {
    y = (parseFloat(y) * VIDEOHEIGHT).toString();
  }
  sprite.rect.y = parseFloat(y);
  return sprite;
};
