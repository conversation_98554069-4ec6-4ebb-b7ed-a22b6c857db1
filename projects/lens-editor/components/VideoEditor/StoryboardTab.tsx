import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Stack, Title, Text, Box, Image, Group, Button, LoadingOverlay } from '@mantine/core';
import { emStatus, emResolution } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import {
  emEditType,
  $WebScriptInfo,
  emScriptGetMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { $PicInfo, $StoryboardInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import { defaultImg } from 'common/utils';
import { useEditorStore } from 'projects/lens-editor/store/editorStore';
import { ModelSelect } from './modalSelect';
import { SelectionState, StoryboardOperationHandler } from './types';
import { RoleManager } from './RoleManager';
import { IconPlus } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import MaterialLibraryModal from './MaterialLibraryModal';
import { useDisclosure } from '@mantine/hooks';
import AIInputField from '../common/AIInputField';

const MemoizedModelSelect = React.memo(ModelSelect);

interface StoryboardTabProps {
  selectionState: SelectionState;
  updateSelectionState: (newState: Partial<SelectionState>) => void;
  allStoryboards: $StoryboardInfo[];
  handleRegeneratePicture: (strId?: string) => void;
  handleStoryboardOperation: StoryboardOperationHandler;
  fetchScriptInfoOnce: (mask: emScriptGetMask, onSuccess?: () => void) => void;
}

// 渲染分镜绘制Tab内容
const StoryboardTabInner = ({
  selectionState,
  updateSelectionState,
  allStoryboards,
  handleRegeneratePicture,
  handleStoryboardOperation,
  fetchScriptInfoOnce,
}: StoryboardTabProps) => {
  const { selectedStoryboard } = selectionState;
  const { scriptInfo, setScriptInfo, scriptId, changeStoryboardHistoryPicture } = useEditorStore();
  const [isModelChanged, setIsModelChanged] = useState(false);
  const [materialLibraryOpened, { open: openMaterialLibrary, close: closeMaterialLibrary }] = useDisclosure(false);

  // 添加防抖引用
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 防抖的状态更新函数 - 移除scriptInfo依赖避免无限循环
  const debouncedUpdateStates = useCallback(
    (updatedStoryboard: $StoryboardInfo) => {
      // 清除之前的定时器
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }

      // 立即更新本地状态，提供即时反馈
      updateSelectionState({
        selectedStoryboard: updatedStoryboard,
      });

      // 设置新的定时器，延迟更新全局状态
      updateTimeoutRef.current = setTimeout(() => {
        const currentScriptInfo = useEditorStore.getState().scriptInfo;
        if (!currentScriptInfo) return;

        const updatedStoryboards = (currentScriptInfo.VecNewStoryboard || []).map(sb =>
          sb.strId === updatedStoryboard.strId ? updatedStoryboard : sb
        );

        setScriptInfo({
          ...currentScriptInfo,
          VecNewStoryboard: updatedStoryboards,
        } as $WebScriptInfo);
      }, 300); // 增加防抖延迟到300ms
    },
    [updateSelectionState, setScriptInfo]
  );

  // 简化的handleStoryChange，使用防抖更新
  const handleStoryChange = (newStory: string) => {
    console.log('[handleStoryChange]:', newStory);

    if (!selectedStoryboard) {
      return;
    }

    // 创建更新后的分镜对象
    const updatedStoryboard = {
      ...selectedStoryboard,
      strStory: newStory,
    };

    // 使用防抖更新
    debouncedUpdateStates(updatedStoryboard);
  };

  // 清理定时器
  React.useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // 使用 useMemo 缓存计算结果，避免不必要的重复计算
  const currentRoles = useMemo(() => {
    if (!selectedStoryboard?.vecRoleId || !scriptInfo?.vecRoleInfo) {
      return [];
    }
    return scriptInfo.vecRoleInfo.filter(role => selectedStoryboard.vecRoleId?.includes(role.strId));
  }, [selectedStoryboard?.vecRoleId, scriptInfo?.vecRoleInfo]);

  // 使用 useMemo 缓存历史图片数据，确保引用稳定
  const historyPictures = useMemo(() => {
    if (!selectedStoryboard?.vecPic) {
      return [];
    }
    // 确保返回一个新数组，避免引用问题
    return [...selectedStoryboard.vecPic];
  }, [selectedStoryboard?.vecPic]);

  console.log('[selectedStoryboard]:', selectedStoryboard);
  console.log('[historyPictures length]:', historyPictures.length);
  console.log('[historyPictures]:', historyPictures);

  if (!selectedStoryboard) {
    return <Text>未找到相关分镜信息</Text>;
  }

  const handleModelChange = (modelId: string | null) => {
    if (modelId) {
      // 更新分镜的图片模型
      const updatedStoryboard = {
        ...selectedStoryboard,
        stCurPic: {
          ...selectedStoryboard.stCurPic,
          stUseModelId: modelId,
        },
      };

      // 标记模型已修改
      setIsModelChanged(true);

      // 更新选择状态
      updateSelectionState({
        selectedStoryboard: updatedStoryboard,
      });

      // 更新全局状态
      const currentScriptInfo = useEditorStore.getState().scriptInfo;
      if (!currentScriptInfo) return;

      const updatedStoryboards = (currentScriptInfo.VecNewStoryboard || []).map(sb =>
        sb.strId === selectedStoryboard.strId ? updatedStoryboard : sb
      );

      setScriptInfo({
        ...currentScriptInfo,
        VecNewStoryboard: updatedStoryboards,
      } as $WebScriptInfo);

      // 调用后端保存
      // handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_MODIFY_PIC_MODEL, updatedStoryboard);
    }
  };

  return (
    <Stack gap="0" style={{ position: 'relative' }}>
      <LoadingOverlay visible={selectedStoryboard.stCurPic?.eStatus === emStatus.EM_STATUS_RUNNING} />
      <Title order={3} mt="sm" mb="xs" size="md">
        分镜文案描述
      </Title>
      <AIInputField
        type="textarea"
        value={selectedStoryboard.strStory}
        onChange={handleStoryChange}
        apiPath="/lens_script/edit_storyboard"
        apiField={emEditType.EM_EDIT_STORYBOARD_TYPE_GEN_STORY}
        apiParams={{
          strScriptId: scriptId,
          stBoardInfo: [
            {
              strId: selectedStoryboard.strId,
              strSceneId: selectedStoryboard.strSceneId,
              iIndex: selectedStoryboard.iIndex,
              vecRoleId: selectedStoryboard.vecRoleId,
            },
          ],
        }}
        textareaProps={{
          placeholder: '分镜故事描述',
          minRows: 4,
          maxRows: 10,
          autosize: true,
        }}
      />

      <Title order={3} mt="md" mb="sm" size="md">
        图片模型
      </Title>
      <Box style={{ borderRadius: '8px' }}>
        <MemoizedModelSelect
          type="picture"
          mvTplId={scriptInfo?.stTopic?.stMVTemplate?.strId || ''}
          value={selectedStoryboard.stCurPic?.stUseModelId || ''}
          onChange={handleModelChange}
          // disabled={selectedStoryboard.stCurPic?.eStatus === emStatus.EM_STATUS_RUNNING}
        />
      </Box>

      {/* 角色管理部分 - 使用 RoleManager 组件 */}
      <RoleManager
        rolesToDisplay={currentRoles}
        canSelect={false}
        canDel={true}
        showRoleSelectTab={true}
        storyboard={selectedStoryboard}
        gridCols={currentRoles.length > 2 ? 3 : currentRoles.length || 1}
        emptyText="该分镜暂未关联角色"
      />

      <Group grow>
        <Button
          variant="primary"
          onClick={() => {
            // 重绘分镜图片
            handleRegeneratePicture();
          }}
          // loading={selectedStoryboard.stCurPic?.eStatus === emStatus.EM_STATUS_RUNNING}
          radius="8px">
          重绘画面 {isModelChanged ? '*' : ''}
        </Button>
      </Group>

      <Title
        order={3}
        mt="md"
        size="md"
        style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
        历史记录
        <Button
          variant="lite"
          size="xs"
          onClick={() => {
            // 打开素材库选择
            openMaterialLibrary();
          }}>
          <IconPlus stroke={1.5} size={14} /> 从素材库选择
        </Button>
      </Title>

      {/* 历史图片渲染区域 - 添加容器key确保正确重新渲染 */}
      <Group gap="md" key={`history-${selectedStoryboard.strId}-${historyPictures.length}`}>
        {historyPictures.length > 0 ? (
          historyPictures.map((pic: $PicInfo, index: number) => (
            <Box key={`${pic.strId}-${index}`} style={{ cursor: 'pointer' }} data-pic-id={pic.strId}>
              <LoadingOverlay visible={!pic.strPicUrl} overlayProps={{ radius: 'sm', blur: 2 }} />
              <Image
                src={pic.strPicUrl + '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p' || defaultImg}
                alt={`历史图片-${index + 1}`}
                style={{
                  borderRadius: '0.5rem',
                  border: selectedStoryboard.stCurPic?.strPicUrl === pic.strPicUrl ? '1px solid #69FFA3' : 'none',
                }}
                onClick={() => {
                  if (!selectedStoryboard || !pic) return;

                  console.log('[点击历史图片] 开始切换:', pic.strId);

                  // 创建更新后的分镜信息
                  const updatedStoryboard = {
                    ...selectedStoryboard,
                    stCurPic: pic,
                  };

                  // 先更新本地状态，提供即时反馈
                  updateSelectionState({
                    selectedStoryboard: updatedStoryboard,
                  });

                  // 更新全局状态
                  const currentScriptInfo = useEditorStore.getState().scriptInfo;
                  if (!currentScriptInfo) return;

                  const updatedStoryboards = (currentScriptInfo.VecNewStoryboard || []).map(sb =>
                    sb.strId === selectedStoryboard.strId ? updatedStoryboard : sb
                  );

                  setScriptInfo({
                    ...currentScriptInfo,
                    VecNewStoryboard: updatedStoryboards,
                  } as $WebScriptInfo);

                  // 保存更改
                  changeStoryboardHistoryPicture(updatedStoryboard)
                    .then(() => {
                      console.log('[点击历史图片] 切换完成，无需刷新全量数据');
                    })
                    .catch(error => {
                      console.error('[点击历史图片] 切换失败:', error);
                      // 错误处理已在 API 函数中完成，这里不需要额外处理
                    });

                  setTimeout(() => {
                    fetchScriptInfoOnce(emScriptGetMask.EM_SCRIPT_GET_TYPE_ALL, () => {
                      console.log('[fetchScriptInfoOnce success]:');
                    });
                  }, 10);
                }}
              />
            </Box>
          ))
        ) : (
          <Text c="dimmed">暂无历史图片</Text>
        )}
      </Group>

      {/* 素材库弹框 */}
      <MaterialLibraryModal
        opened={materialLibraryOpened}
        onClose={closeMaterialLibrary}
        selectedStoryboard={selectedStoryboard}
        handleStoryboardOperation={handleStoryboardOperation}
      />
    </Stack>
  );
};

// 使用 React.memo 进行性能优化，使用默认的浅比较
export const StoryboardTab = React.memo(StoryboardTabInner);
