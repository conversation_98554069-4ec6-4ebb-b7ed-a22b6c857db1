import React from 'react';
import { Group, ActionIcon, Text, But<PERSON>, Slider } from '@mantine/core';
import { IconPlayerPauseFilled, IconPlayerPlayFilled } from '@tabler/icons-react';
import cls from './videoEditor.module.less';

interface TimelineControlsProps {
  scale: number;
  setScale: (scale: number) => void;
  currentTime: string;
  totalTime: string;
  playing: boolean;
  onPlayPause: () => void;
  handleZoomOut: () => void;
  handleZoomIn: () => void;
}

/**
 * 时间线控制组件，包括播放/暂停按钮、时间显示和缩放控制
 */
export const TimelineControls = React.memo(
  ({
    scale,
    setScale,
    currentTime,
    totalTime,
    playing,
    onPlayPause,
    handleZoomOut,
    handleZoomIn,
  }: TimelineControlsProps) => {
    // 自动排列分镜
    // const handleAutoArrange = () => {
    //   console.log('[handleAutoArrange]:');
    //   // 调用 edit_storyboard 的 field = 56， 然后fetchonce
    //   handleStoryboardOperation(emEditType.EM_EDIT_STORYBOARD_TYPE_AUTO_ARRANGE, []);
    // };
    return (
      <Group justify="flex-end" mt="8px" style={{ position: 'relative' }}>
        <div style={{ width: '100%', position: 'absolute', display: 'flex', justifyContent: 'center' }}>
          <ActionIcon variant="filled" color="#6AFFA3" size="sm" radius="xl" onClick={onPlayPause}>
            {playing ? (
              <IconPlayerPauseFilled size={14} color="#0F1114" />
            ) : (
              <IconPlayerPlayFilled size={14} color="#0F1114" />
            )}
          </ActionIcon>
          <Text size="sm" ml={12} style={{ lineHeight: '22px' }} fw={500}>
            {currentTime} | <span style={{ opacity: 0.5 }}>{totalTime}</span>
          </Text>
        </div>

        <div style={{ display: 'flex', width: '160px', alignItems: 'center', zIndex: 1 }}>
          <Button size="xss" variant="filled" color="#333743" onClick={handleZoomOut} mr="xs" className={cls.btnSlider}>
            -
          </Button>
          <Slider
            value={scale}
            size="xs"
            onChange={setScale}
            min={1}
            max={60}
            step={1}
            thumbSize={18}
            style={{ flex: 1 }}
            label={value => `${value}x`}
            color="#fff"
          />
          <Button size="xss" variant="filled" color="#333743" onClick={handleZoomIn} ml="xs" className={cls.btnSlider}>
            +
          </Button>
        </div>
        {/* <Button
          size="xs"
          onClick={() => {
            handleAutoArrange();
          }}>
          自动排列分镜
        </Button> */}
      </Group>
    );
  }
);

TimelineControls.displayName = 'TimelineControls';
