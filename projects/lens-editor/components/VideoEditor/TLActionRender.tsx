// 渲染自定义时间轴action
import { TLActionWithName } from './types';
import React from 'react';
import { LoadingOverlay } from '@mantine/core';
import cls from './videoEditor.module.less';
import { $WebStoryboardInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import { defaultImg } from 'common/utils';
import { emStatus } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';

export const ActionRender = ({
  action,
  scriptInfo,
  activeAction,
}: {
  action: TLActionWithName;
  scriptInfo: any;
  activeAction: TLActionWithName;
}) => {
  // 使用storyboardRef获取最新状态，如果没有则使用scriptInfo查找
  const storyboard = scriptInfo?.VecNewStoryboard?.find((sb: $WebStoryboardInfo) => sb.strId === action.id);

  const thumbnailUrl = storyboard?.stCurPic?.strPicUrl
    ? storyboard?.stCurPic?.strPicUrl + '?imageMogr2/format/webp|imageMogr2/thumbnail/!20p'
    : '';

  // 判断是否为分镜图阶段（视频未生成或生成失败）
  const isPlaceholder = action.isPlaceholder === true;

  // 获取视频和图片状态
  const videoStatus = action.videoStatus || storyboard?.stCurVideo?.eStatus || 0;

  const pictureStatus = storyboard?.stCurPic?.eStatus;
  // console.log(
  //   '[actionRender]:',
  //   action,
  //   storyboard,
  //   scriptInfo?.VecNewStoryboard?.find((sb: $WebStoryboardInfo) => sb.strId === action.id),
  //   pictureStatus,
  //   thumbnailUrl
  // );

  // 视频处理中状态
  const isVideoProcessing = videoStatus === emStatus.EM_STATUS_RUNNING;
  // // 视频生成失败状态
  // const isVideoFailed = videoStatus === emStatus.EM_STATUS_FAIL;
  // // 视频生成成功状态
  // const isVideoSuccess = videoStatus === emStatus.EM_STATUS_SUCC;

  return (
    <div
      className={
        cls.actionItem +
        ` ${action.id === activeAction?.id ? cls.actionItemActive : ''}` +
        ` ${cls[action.type as keyof typeof cls]}`
      }>
      {action.type === 'video' ? (
        <>
          {/* 显示视频处理状态的叠加层 */}
          {isVideoProcessing && (
            <LoadingOverlay
              visible={true}
              overlayProps={{ radius: 'sm', blur: 2 }}
              loaderProps={{ type: 'dots', color: '#69FFA3', size: 'sm' }}
            />
          )}

          {/* 显示缩略图 - 如果有视频则使用图片作为封面，没有视频则显示分镜图片 */}
          {pictureStatus === emStatus.EM_STATUS_FAIL ? (
            <div className={cls.failMask}>画面失败</div>
          ) : videoStatus === emStatus.EM_STATUS_FAIL ? (
            <div className={cls.failMask}>视频失败</div>
          ) : null}
          <img
            src={thumbnailUrl || defaultImg}
            alt={action.id}
            data-status={videoStatus}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              position: 'absolute',
              opacity: isPlaceholder ? 0.7 : 1, // 占位图降低透明度
            }}
          />
        </>
      ) : (
        <div
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'clip',
            width: '100%',
            textAlign: 'center',
            color: 'white',
          }}>
          {action.name}
        </div>
      )}
    </div>
  );
};
