import React, { createContext, ReactNode, useState, useContext } from 'react';
import { Modal, Text, Button, Group } from '@mantine/core';

// 定义确认框参数类型
interface ConfirmOptions {
  title: string;
  description: string;
  confirmLabel?: string;
  cancelLabel?: string;
  confirmColor?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

// 定义 Context 内容
interface ConfirmationContextType {
  confirm: (options: ConfirmOptions) => void;
}

// 创建 Context
const ConfirmationContext = createContext<ConfirmationContextType | null>(null);

// Provider Props
interface ConfirmationProviderProps {
  children: ReactNode;
}

// Provider 组件
export const ConfirmationProvider: React.FC<ConfirmationProviderProps> = ({ children }) => {
  // 控制弹框显示状态
  const [isOpen, setIsOpen] = useState(false);
  // 保存当前确认框配置
  const [confirmOptions, setConfirmOptions] = useState<ConfirmOptions | null>(null);

  // 打开确认框
  const confirm = (options: ConfirmOptions) => {
    setConfirmOptions(options);
    setIsOpen(true);
  };

  // 处理取消操作
  const handleCancel = () => {
    setIsOpen(false);
    if (confirmOptions?.onCancel) {
      confirmOptions.onCancel();
    }
  };

  // 处理确认操作
  const handleConfirm = () => {
    setIsOpen(false);
    if (confirmOptions?.onConfirm) {
      confirmOptions.onConfirm();
    }
  };

  return (
    <ConfirmationContext.Provider value={{ confirm }}>
      {children}

      {/* 确认弹框 */}
      {confirmOptions && (
        <Modal
          opened={isOpen}
          onClose={handleCancel}
          title={confirmOptions.title}
          centered
          overlayProps={{
            backgroundOpacity: 0.55,
            blur: 8,
          }}>
          <Text>{confirmOptions.description}</Text>
          <Group mt="lg" justify="flex-end">
            <Button variant="default" onClick={handleCancel}>
              {confirmOptions.cancelLabel || '取消'}
            </Button>
            <Button onClick={handleConfirm} color={confirmOptions.confirmColor || 'red'}>
              {confirmOptions.confirmLabel || '确定'}
            </Button>
          </Group>
        </Modal>
      )}
    </ConfirmationContext.Provider>
  );
};

// 自定义 Hook
export default function useConfirm() {
  const context = useContext(ConfirmationContext);
  if (!context) {
    throw new Error('useConfirm must be used within a ConfirmationProvider');
  }
  return context.confirm;
}
