import React, { useMemo } from 'react';
import { AppShell, Button, Group, Image, Text } from '@mantine/core';
import logo from 'common/logo.png';
import NavBar from '../NavBar/NavBar';
import { useLocation } from 'react-router-dom';

interface LayoutProps {
  children: React.ReactNode;
  isLoggedIn: boolean;
}

import classes from '../../App.less';

const Layout: React.FC<LayoutProps> = ({ children, isLoggedIn }) => {
  const loaction = useLocation();

  const isNavbar = useMemo(() => {
    const paths = ['/home', '/userinfo'];
    return paths.indexOf(loaction.pathname) >= 0;
  }, [loaction.pathname]);

  return (
    <AppShell
      style={{
        minWidth: '1280px',
        margin: 'auto 0',
      }}
      withBorder={false}
      navbar={isNavbar ? { width: 220, breakpoint: 'sm' } : undefined}>
      {isNavbar ? <NavBar /> : null}
      <a href="#/home" className={classes.logo}>
        <Image src={logo} alt="logo" width={111} height={45} />
      </a>
      <AppShell.Main>{children}</AppShell.Main>
    </AppShell>
  );
};

export default Layout;
