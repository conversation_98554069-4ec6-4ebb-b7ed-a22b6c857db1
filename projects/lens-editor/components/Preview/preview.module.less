.preview {
  display: flex;
  flex-direction: column;

  border-radius: 8px;
}

.previewVideoWrap {
  margin-bottom: 8px;
  align-self: center;
  position: relative;
  overflow: hidden;

}

.downloadBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
  height: 30px;
}

.previewVideo {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: contain
}

.previewCover {

  position: relative;
  margin-bottom: 50px;
}

.coverTitle {
  margin-bottom: 16px;
  color: #fff;
  font-size: 18px;
  line-height: 25px;
}

.coverList {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  list-style: none;
}

.coverItem {
  position: relative;
  width: 202px;
  height: 359px;

  &.horizontal {
    height: 114px;
  }

  margin-right: 16px;
  border-radius: 8px;
  flex-shrink: 0;
  overflow: hidden;
  cursor: pointer;

  .horizontal {
    width: 202px;
    height: 114px;
  }

  &.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    border: 1px solid rgba(105, 255, 163, 1);
  }
}