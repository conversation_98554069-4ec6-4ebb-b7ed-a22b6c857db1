.qrModal {
  :global {
    .mantine-Modal-body {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

.title {

  margin-bottom: 40px;
  color: #fff;
  font-size: 18px;
  line-height: 25px;
}

.qrcode {
  width: 200px;
  height: 200px;
  margin-bottom: 56px;
  border-radius: 8px;
}

.link {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}

.linkText {
  width: 357px;
  height: 48px;
  line-height: 48px;
  margin-right: 24px;
  padding: 0 16px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  background-color: rgba(10, 15, 23, 1);
  border-radius: 8px;
  box-sizing: border-box;
}

// .linkBtn {
//   width: 128px;
//   height: 40px;
//   color: rgba(105, 255, 163, 1);
//   font-size: 16px;
//   background-color: rgba(60, 72, 91, 1);
// }