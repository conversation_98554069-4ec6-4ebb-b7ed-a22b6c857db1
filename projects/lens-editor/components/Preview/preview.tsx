// 预览成片
import React, { useState } from 'react';
import styles from './preview.module.less';
import { Container, Image, AspectRatio, Text, LoadingOverlay, Button, Box } from '@mantine/core';
import classnames from 'classnames';
import { useEditorStore } from '../../store/editorStore';
import req from 'common/request';
import { notifications } from '@mantine/notifications';
import {
  $ModifyVideoCoverRsp,
  emScriptGetMask,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_svr.jce?emit=module';
import {
  emFlow,
  emResolution,
  emStatus,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';

export default function Preview() {
  const { scriptInfo, scriptId, startPolling } = useEditorStore();

  const [isLoading, setIsLoading] = useState(false);
  const [coverImg, setCoverImg] = useState(scriptInfo?.strCoverImg);

  // 修改视频封面
  const handleChangeCover = (storyboardId: string) => {
    // 获取选中的封面图片URL
    const selectedStoryboard = scriptInfo?.VecNewStoryboard?.find(sb => sb.strId === storyboardId);
    const coverUrl = selectedStoryboard?.stCurPic?.strPicUrl;

    if (!scriptId || !coverUrl) {
      console.error('修改封面失败：scriptId或coverUrl不存在', { scriptId, coverUrl });
      return;
    }

    if (isLoading) {
      return;
    }

    setIsLoading(true);

    // 发起修改视频封面请求
    req
      .post('/lens_script/modify_video_cover', {
        strScriptId: scriptId,
        strCoverUrl: coverUrl,
      })
      .then((res: { data: { error_code: number; error_msg: string; data: $ModifyVideoCoverRsp } }) => {
        if (res.data.error_code !== 0 || res.data.data.iRes !== 0) {
          notifications.show({
            title: '修改封面失败',
            message: res.data.error_msg || res.data.data.strTips,
            color: 'red',
          });
          return;
        }

        notifications.show({
          title: '封面修改成功',
          message: '',
        });
        setCoverImg(coverUrl);
      })
      .catch(error => {
        notifications.show({
          title: '修改封面失败',
          message: error?.message,
          color: 'red',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <Container size={1280} px={0} className={styles.preview}>
      <AspectRatio
        className={styles.previewVideoWrap}
        maw={scriptInfo?.stTopic.stSize.stResolution === emResolution.EM_VIDEO_720P_H ? 1280 : 352}
        ratio={scriptInfo?.stTopic.stSize.stResolution === emResolution.EM_VIDEO_720P_H ? 16 / 9 : 9 / 16}>
        <LoadingOverlay
          visible={
            (scriptInfo?.mapFlowStatus as Record<number, number>)[emFlow.EM_FLOW_PREVIEW] === emStatus.EM_STATUS_RUNNING
          }
          overlayProps={{ blur: 8 }}
          loaderProps={{ color: 'var(--green-text-color)', size: 'md' }}
        />
        {(scriptInfo?.mapFlowStatus as Record<number, number>)[emFlow.EM_FLOW_PREVIEW] === emStatus.EM_STATUS_FAIL ? (
          <div className={styles.previewVideo}>
            <Button
              onClick={() => {
                req
                  .post('/lens_script/do_main_process', {
                    strScriptId: scriptId,
                    eType: emFlow.EM_FLOW_PREVIEW,
                  })
                  .then(res => {
                    console.log('[预览成片 res]:', res);
                    if (res.data.error_code === 0 && res.data.data.iRes === 0) {
                      // 确保轮询已启动，获取最新数据
                      startPolling(emScriptGetMask.EM_SCRIPT_GET_TYPE_MERGE);
                    } else {
                      notifications.show({
                        title: '重新合成失败',
                        message: res.data.error_msg,
                        color: 'red',
                        autoClose: 5000,
                      });
                      console.error('重新合成失败:', res.data.msg);
                    }
                  });
              }}>
              合成失败，点击重新合成
            </Button>
          </div>
        ) : (
          <video
            className={styles.previewVideo}
            src={scriptInfo?.strVideoUrl}
            poster={scriptInfo?.strCoverImg + '?imageMogr2/format/webp|imageSlim'}
            controls
            autoPlay
            preload="auto"></video>
        )}
        <Box className={styles.downloadBtn}>
          <a
            href={scriptInfo?.strVideoUrl}
            download={scriptInfo?.strVideoUrl.split('/').pop()}
            target="_blank"
            rel="noopener noreferrer">
            <Button variant="awsome" size="xs">
              下载视频
            </Button>
          </a>
        </Box>
      </AspectRatio>
      <Box className={styles.previewCover}>
        <LoadingOverlay
          visible={isLoading}
          overlayProps={{ blur: 8 }}
          loaderProps={{ color: 'var(--green-text-color)', size: 'md' }}
        />
        <p className={styles.coverTitle}>选择封面图片</p>
        <ul className={styles.coverList}>
          <li
            key={'current'}
            className={classnames(styles.coverItem, styles.active, {
              [styles.horizontal]: scriptInfo?.stTopic.stSize.stResolution === emResolution.EM_VIDEO_720P_H,
            })}>
            <Image src={coverImg + '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p'} alt="" />
          </li>
          {scriptInfo?.VecNewStoryboard?.length ? (
            scriptInfo?.VecNewStoryboard.map(item => (
              <li
                key={item.strId}
                className={classnames(styles.coverItem, {
                  [styles.horizontal]: scriptInfo?.stTopic.stSize.stResolution === emResolution.EM_VIDEO_720P_H,
                })}
                onClick={() => handleChangeCover(item.strId)}>
                <Image src={item.stCurPic.strPicUrl + '?imageMogr2/format/webp|imageMogr2/thumbnail/!40p'} alt="" />
              </li>
            ))
          ) : (
            <li>
              <Text>暂无封面图片</Text>
            </li>
          )}
        </ul>
      </Box>
    </Container>
  );
}
