import React, { useState, useRef, useEffect } from 'react';
import styles from './qrCodeModal.module.less';
import { Button, Popover, Tooltip, Modal, Box, Text } from '@mantine/core';
import { stopPropagation } from '../../utils';

interface QrCodeModalProps {
  link: string;
  onClose: () => void;
}

const QrCodeModal: React.FC<QrCodeModalProps> = ({ link, onClose }) => {
  const timeoutRef = useRef<NodeJS.Timeout>();
  const [tip, setTip] = useState('');

  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, []);

  const handleCopy = async () => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(link);
      } else {
        const textarea = document.createElement('textarea');
        textarea.value = link;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
      }
      setTip('已成功复制到剪贴板');
    } catch (error) {
      setTip('复制失败，请稍后重试');
    } finally {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        setTip('');
      }, 1500);
    }
  };

  const handleTip = (opened: boolean) => {
    if (!opened) {
      clearTimeout(timeoutRef.current);
      setTip('');
    }
  };

  return (
    <Modal
      opened={true}
      onClose={onClose}
      size="920px"
      centered
      className={styles.qrModal}
      overlayProps={{
        backgroundOpacity: 0.7,
        blur: 3,
      }}>
      <Text className={styles.title}>手机扫码，或者复制连接分享给好友和朋友圈</Text>
      <img
        className={styles.qrcode}
        src={'//node.kg.qq.com/qrcode?width=200&margin=2&eclevel=H&logo=1&encode=1&url=' + encodeURIComponent(link)}
        alt="分享二维码"
      />
      <div className={styles.link}>
        <Tooltip withArrow label={link}>
          <p className={styles.linkText}>{link}</p>
        </Tooltip>
        <Popover opened={!!tip} onChange={handleTip} position="top" withArrow>
          <Popover.Target>
            <Button variant="primary" onClick={() => void handleCopy()}>
              复制链接
            </Button>
          </Popover.Target>
          <Popover.Dropdown>{tip}</Popover.Dropdown>
        </Popover>
      </div>
    </Modal>
  );
};

export default QrCodeModal;
