import React from 'react';
import styles from './MyEnv.module.less';
import Section from './Section';
import { Image } from '@mantine/core';
import { FALLBACK_IMAGE } from '../../constants/config';
import { useUserInfoStore } from '../../store/userInfoStore';

const MyEnv: React.FC = () => {
  const { userInfo } = useUserInfoStore();

  return (
    <Section title="我的环境">
      <ul className={styles.cards}>
        {userInfo && userInfo.VecEnv.length > 0 ? (
          userInfo.VecEnv.map(item => (
            <li key={item.strId} className={styles.card}>
              <div className={styles.cardCover}>
                <Image src={item.strUrl} fallbackSrc={FALLBACK_IMAGE} />
                <button className={styles.cardEdit} type="button"></button>
              </div>
              <p className={styles.cardTitle}>{item.strName}</p>
              <p className={styles.cardDesc}>{item.strDesc}</p>
            </li>
          ))
        ) : (
          <p>暂无环境</p>
        )}
      </ul>
    </Section>
  );
};

export default MyEnv;
