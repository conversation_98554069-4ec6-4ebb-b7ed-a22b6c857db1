@import '../../../../common/styles/mixins.less';

.cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.card {
  width: 312px;
}

.cardCover {
  position: relative;
  width: 312px;
  height: 176px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 8px;
}

.cardEdit {
  position: absolute;
  right: 10px;
  top: 10px;
  width: 24px;
  height: 24px;
  background-image: url(../../assets/images/pages/editIcon.png);
  background-size: 100% 100%;
  cursor: pointer;
}

.cardTitle {
  .text-ellipsis();
  max-width: 100%;
  margin-bottom: 4px;
  color: rgba(252, 254, 255, 1);
  font-size: 14px;
  line-height: 20px;
}

.cardDesc {
  .text-ellipsis();
  max-width: 100%;
  color: rgba(252, 254, 255, 0.3);
  font-size: 12px;
  line-height: 17px;
}