import React from 'react';
import styles from './MyMv.module.less';
import Section from './Section';
import { ActionIcon, Box, Image } from '@mantine/core';
import { FALLBACK_IMAGE, FALLBACK_AVATAR } from '../../constants/config';
import { useUserInfoStore } from '../../store/userInfoStore';
import { useNavigate } from 'react-router-dom';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import useConfirm from '../ConfirmationContext';
import req from 'common/request';
import { notifications } from '@mantine/notifications';
import { $WebVideoInfo } from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
// 我的作品
const MyMv: React.FC = () => {
  const navigate = useNavigate();
  const { userInfo, updateUserInfo } = useUserInfoStore();
  const confirm = useConfirm();
  const handleEdit = (video: $WebVideoInfo) => {
    window.location.href = `${window.location.origin}${window.location.pathname}#/editor/${video.strScriptId}`;
    window.location.reload();
    return;
  };

  const handleClick = (video: $WebVideoInfo) => {
    if (video.strVideoUrl) {
      window.open(video.strVideoUrl, '_blank');
    } else {
      handleEdit(video);
    }
  };

  const handleDelete = (video: $WebVideoInfo) => {
    console.log('[handleDelete]:', video);
    confirm({
      title: `确定删除作品《${video.strSongName}》吗？`,
      description: '删除后无法恢复，请确认是否继续',
      onConfirm: () => {
        req
          .post('/lens_script/delete_script', {
            strScriptId: video.strScriptId,
          })
          .then(res => {
            if (res.data.error_code !== 0 || res.data.data.iRes !== 0) {
              notifications.show({
                title: '删除失败',
                message: res.data.error_msg || res.data.data.strTips,
              });
              return;
            }
            notifications.show({
              title: '删除成功',
              message: '',
            });

            // 从列表中移除已删除的作品
            if (userInfo && userInfo.vecVideo) {
              const updatedVecVideo = userInfo.vecVideo.filter(item => item.strScriptId !== video.strScriptId);

              // 更新用户信息
              updateUserInfo({
                ...userInfo,
                vecVideo: updatedVecVideo,
              });
            }

            console.log('[handleDelete confirm]:', res);
          })
          .catch(err => {
            notifications.show({
              title: '网络异常，删除失败',
              message: err?.message,
            });
            console.log('[handleDelete confirm err]:', err);
          });
      },
    });
    return;
  };

  return (
    <Section title="我的创作">
      <ul className={styles.cards}>
        {userInfo && userInfo.vecVideo?.length > 0 ? (
          userInfo.vecVideo.map(item => (
            <li key={item.strScriptId} className={styles.card}>
              <div className={styles.cardCover}>
                <Image onClick={() => handleClick(item)} src={item.strCoverImg} fallbackSrc={FALLBACK_IMAGE} />
                <Box className={styles.cardActions}>
                  <ActionIcon
                    size={24}
                    title="编辑"
                    color="rgba(0,0,0,0.5)"
                    className={styles.cardEdit}
                    onClick={() => handleEdit(item)}>
                    <IconEdit size={16} stroke={1.5} />
                  </ActionIcon>
                </Box>
              </div>
              <div className={styles.titleContainer}>
                <p className={styles.cardTitle}>
                  <span>{item.strSongName}</span>
                  <span className={`${styles.statusTag} ${item.strVideoUrl ? styles.statusTagPublished : ''}`}>
                    {item.strVideoUrl ? '已完成' : '制作中'}
                  </span>
                </p>
                <ActionIcon
                  size={24}
                  title="删除"
                  color="rgba(0,0,0,0.5)"
                  className={styles.cardDelete}
                  onClick={() => handleDelete(item)}>
                  <IconTrash size={16} stroke={1.5} />
                </ActionIcon>
              </div>
              <div className={styles.cardInfo}>
                <div className={styles.cardAvatar}>
                  <Image src={item.stUserInfo.strAvatarUrl} fallbackSrc={FALLBACK_AVATAR} />
                </div>
                <p className={styles.cardNick}>{item.stUserInfo.strNick}</p>
                <div className={styles.likeIcon} />
                <p className={styles.likeNum}>{item.lStar}</p>
              </div>
            </li>
          ))
        ) : (
          <p>暂无作品</p>
        )}
      </ul>
    </Section>
  );
};

export default MyMv;
