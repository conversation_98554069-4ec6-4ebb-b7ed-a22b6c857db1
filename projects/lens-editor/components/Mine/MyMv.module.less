@import '../../../../common/styles/mixins.less';

.cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 16px;
}

.card {
  width: 312px;
}

.cardCover {
  position: relative;
  width: 312px;
  height: 176px;
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 8px;
}

.cardActions {
  position: absolute;
  right: 10px;
  top: 10px;
  display: flex;
  gap: 10px;
}

.titleContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

// .cardEdit,
// .cardDelete {
//   position: absolute;
//   right: 10px;
//   top: 10px;
//   width: 24px;
//   height: 24px;
// }
// .cardDelete{
//   right: 40px;
// }

.cardTitle {
  .text-ellipsis();
  max-width: 100%;
  color: rgba(252, 254, 255, 1);
  font-size: 14px;
  line-height: 20px;
}

.cardInfo {
  display: flex;
  align-items: center;
}

.cardAvatar {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.cardNick {
  .text-ellipsis();
  flex: 1 1;
  color: #fff;
  font-size: 12px;
}

.likeIcon {
  width: 12px;
  height: 11px;
  margin-right: 2px;
  background-image: url(../../assets/images/pages/likeIcon.png);
  background-size: 100% 100%;
}

.likeNum {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  line-height: 17px;
}

.statusTag {
  padding: 2px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 400;
  margin: 10px;
  background-color: rgba(255, 255, 255, 0.2);
}

.statusTagPublished {
  background-color: #5EE6B1;
  color: #0A0F17;
}