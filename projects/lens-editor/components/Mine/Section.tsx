import React from 'react';
import styles from './Section.module.less';
import classnames from 'classnames';

interface SectionProps {
  title: string;
  className?: string;
  children: React.ReactNode;
}

const Section: React.FC<SectionProps> = ({
  title,
  className,
  children
}) => {
  return (
    <section className={classnames(styles.section, className)}>
      <h2 className={styles.title}>{title}</h2>
      {children}
    </section>
  )
}

export default Section;
