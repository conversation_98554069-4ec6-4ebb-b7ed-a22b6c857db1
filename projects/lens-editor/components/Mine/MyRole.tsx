import React from 'react';
import styles from './MyRole.module.less';
import Section from './Section';
import { Image, Button } from '@mantine/core';
import { FALLBACK_IMAGE } from '../../constants/config';
import { useUserInfoStore } from '../../store/userInfoStore';

const MyRole: React.FC = () => {
  const { userInfo } = useUserInfoStore();

  return (
    <Section className={styles.myRole} title="我的角色">
      {userInfo && userInfo.vecRole.length > 0 ? (
        <ul className={styles.cards}>
          {userInfo.vecRole.map(item => (
            <li key={item.strId} className={styles.card}>
              <div className={styles.cardCover}>
                <Image src={item.strUrl} fallbackSrc={FALLBACK_IMAGE} />
                <button type="button" className={styles.cardEdit}></button>
              </div>
              <p className={styles.cardTitle}>{item.strName}</p>
              <p className={styles.cardDesc}>{item.strRoleTips}</p>
            </li>
          ))}
        </ul>
      ) : (
        <p>暂无角色</p>
      )}
    </Section>
  );
};

export default MyRole;
