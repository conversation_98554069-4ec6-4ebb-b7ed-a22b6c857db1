# 确认弹框使用指南

本项目使用基于 Context + Hook 的确认弹框系统，提供了更简洁、更直观的方式来创建确认对话框。

## 基本用法

在任何组件中，只需导入 `useConfirm` hook，然后调用其 `confirm` 方法即可显示确认对话框：

```tsx
import { useConfirm } from '../components/ConfirmationContext';

function YourComponent() {
  const { confirm } = useConfirm();

  const handleDeleteItem = () => {
    confirm({
      title: '删除项目',
      description: '确定要删除该项目吗？此操作无法撤销。',
      onConfirm: () => {
        // 执行删除操作
        deleteItem();
      },
      // 可选：取消时的回调
      onCancel: () => {
        console.log('用户取消了操作');
      }
    });
  };

  return (
    <Button onClick={handleDeleteItem} color="red">
      删除项目
    </Button>
  );
}
```

## 可用选项

`confirm` 方法接受一个配置对象，包含以下选项：

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | - | 对话框标题（必填） |
| description | string | - | 对话框描述文本（必填） |
| confirmLabel | string | "确定" | 确认按钮的文本 |
| cancelLabel | string | "取消" | 取消按钮的文本 |
| confirmColor | string | "red" | 确认按钮的颜色 |
| onConfirm | () => void | - | 点击确认按钮时的回调函数 |
| onCancel | () => void | - | 点击取消按钮时的回调函数 |

## 示例

### 自定义按钮文本和颜色

```tsx
confirm({
  title: '发布内容',
  description: '确定要发布这篇文章吗？',
  confirmLabel: '发布',
  cancelLabel: '稍后再说',
  confirmColor: 'green',
  onConfirm: () => publishArticle()
});
```

### 危险操作确认

```tsx
confirm({
  title: '删除账户',
  description: '此操作将永久删除您的账户和所有相关数据，无法恢复。确定要继续吗？',
  confirmLabel: '永久删除',
  confirmColor: 'red',
  onConfirm: () => deleteAccount()
});
```