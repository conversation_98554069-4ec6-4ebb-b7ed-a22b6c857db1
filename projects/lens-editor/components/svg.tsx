import React from 'react';

const MVIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="16" viewBox="0 0 21 16" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2 0C0.895431 0 0 0.895431 0 2V14C0 15.1046 0.89543 16 2 16H19C20.1046 16 21 15.1046 21 14V2C21 0.895431 20.1046 0 19 0H2ZM13.5 8.86602C14.1667 8.48112 14.1667 7.51887 13.5 7.13397L9 4.5359C8.33333 4.151 7.5 4.63212 7.5 5.40192L7.5 10.5981C7.5 11.3679 8.33333 11.849 9 11.4641L13.5 8.86602Z"
      fill="white"
    />
  </svg>
);

const SearchIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5 11.3506C5 7.48459 8.13401 4.35059 12 4.35059C15.866 4.35059 19 7.48459 19 11.3506C19 15.2166 15.866 18.3506 12 18.3506C8.13401 18.3506 5 15.2166 5 11.3506ZM12 2.35059C7.02944 2.35059 3 6.38002 3 11.3506C3 16.3211 7.02944 20.3506 12 20.3506C14.125 20.3506 16.078 19.6141 17.6177 18.3825L19.7929 20.5577C20.1834 20.9482 20.8166 20.9482 21.2071 20.5577C21.5976 20.1672 21.5976 19.534 21.2071 19.1435L19.0319 16.9683C20.2635 15.4286 21 13.4756 21 11.3506C21 6.38002 16.9706 2.35059 12 2.35059Z"
      fill="white"
    />
  </svg>
);

export { MVIcon, SearchIcon };
