import React, { useState } from 'react';
import { Modal, TextInput, Button, Group, Text, Stack, Box, Notification } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { getCode, login } from '../../../common/request';

// 倒计时组件
const CountdownButton = ({
  onClick,
  disabled,
  children,
}: {
  onClick: () => void;
  disabled?: boolean;
  children: React.ReactNode;
}) => {
  const [counting, setCounting] = useState(false);
  const [seconds, setSeconds] = useState(60);

  const handleClick = () => {
    if (!counting && !disabled) {
      onClick();
      setCounting(true);
      setSeconds(60);

      const timer = setInterval(() => {
        setSeconds(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setCounting(false);
            return 60;
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  return (
    <Button onClick={handleClick} disabled={counting || disabled} variant="filled" color="#3C485B">
      {counting ? `${seconds}秒后重试` : children}
    </Button>
  );
};

interface LoginModalProps {
  opened: boolean;
  onClose: () => void;
  onLoginSuccess: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ opened, onClose, onLoginSuccess }) => {
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  // 验证手机号格式
  const isPhoneValid = /^1[3-9]\d{9}$/.test(phone);

  // 验证验证码格式
  const isCodeValid = /^\d{4,6}$/.test(code);

  // 处理获取验证码
  const handleGetCode = async () => {
    if (!isPhoneValid) {
      setNotification({ message: '请输入正确的手机号码', type: 'error' });
      return;
    }

    try {
      setSendingCode(true);
      await getCode(phone);
      setNotification({ message: '验证码已发送', type: 'success' });
    } catch (error) {
      console.error('获取验证码失败', error);
      setNotification({ message: '获取验证码失败，请重试', type: 'error' });
    } finally {
      setSendingCode(false);
    }
  };

  // 处理登录
  const handleLogin = async () => {
    if (!isPhoneValid || !isCodeValid) {
      setNotification({ message: '请输入正确的手机号和验证码', type: 'error' });
      return;
    }

    try {
      setLoading(true);
      await login(phone, code);
      setNotification({ message: '登录成功', type: 'success' });
      setTimeout(() => {
        onClose();
        onLoginSuccess();
      }, 1000);
    } catch (error) {
      console.error('登录失败', error);
      setNotification({ message: '登录失败，请重试', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // 清除通知
  const clearNotification = () => setNotification(null);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      overlayProps={{
        backgroundOpacity: 0.55,
        blur: 3,
      }}
      zIndex={1000}
      closeOnClickOutside={false}
      title="登录">
      <Stack gap="md">
        {notification && (
          <Notification
            color={notification.type === 'success' ? 'green' : 'red'}
            onClose={clearNotification}
            withCloseButton>
            {notification.message}
          </Notification>
        )}

        <TextInput
          label="手机号"
          placeholder="请输入手机号"
          value={phone}
          data-autofocus
          onChange={e => setPhone(e.target.value)}
          error={phone && !isPhoneValid ? '请输入正确的手机号码' : null}
          required
        />

        <Group grow>
          <TextInput
            label="验证码"
            placeholder="请输入验证码"
            value={code}
            onChange={e => setCode(e.target.value)}
            error={code && !isCodeValid ? '请输入正确的验证码' : null}
            required
          />
          <Box mt={25}>
            <CountdownButton onClick={() => void handleGetCode()} disabled={!isPhoneValid || sendingCode}>
              获取验证码
            </CountdownButton>
          </Box>
        </Group>

        <Group justify="flex-end" mt="md">
          <Button onClick={onClose}>取消</Button>
          <Button
            onClick={() => void handleLogin()}
            color="#3C485B"
            loading={loading}
            variant="primary"
            disabled={!isPhoneValid || !isCodeValid}>
            登录
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default LoginModal;

// 提供一个钩子，方便在应用中使用
export const useLoginModal = () => {
  const [opened, { open, close }] = useDisclosure(false);

  return {
    opened,
    openLoginModal: open,
    closeLoginModal: close,
    LoginModal: ({ onLoginSuccess }: { onLoginSuccess: () => void }) => (
      <LoginModal opened={opened} onClose={close} onLoginSuccess={onLoginSuccess} />
    ),
  };
};
