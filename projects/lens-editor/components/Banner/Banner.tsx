import React from 'react';
import { Button } from '@mantine/core';
import styles from './Banner.module.less';
import bannerImg from '../../assets/images/pages/bannerImg.jpg';
import { Link, useNavigate } from 'react-router-dom';
import { useHomeStore } from '../../store/homeStore';
import { notifications } from '@mantine/notifications';

const Banner = () => {
  const { strTitleDetail, iCanAccess, strRefuseTips } = useHomeStore();
  const navigate = useNavigate();

  const handleClick = (e: React.MouseEvent) => {
    if (!iCanAccess) {
      e.preventDefault();
      notifications.show({
        title: '提示',
        message: strRefuseTips || '暂无权限访问',
        color: 'red',
      });
    } else {
      navigate('/editor/new');
    }
  };

  return (
    <section className={styles.banner}>
      <img className={styles.bannerImg} src={bannerImg} />
      <div className={styles.bannerInfo}>
        <div className={styles.bannerSubTitle}>
          <div className={styles.bannerSubTitleIcon}/>
          <p className={styles.bannerSubTitleText}>
            MV创作
          </p>
        </div>
        <h2 className={styles.bannerTitle}>输入歌曲名，即可一键成片</h2>
        <p className={styles.bannerDesc}>
          {strTitleDetail}
        </p>
        <Button className={styles.bannerBtn} onClick={handleClick}>
          去试试
        </Button>
      </div>
    </section>
  )
};

export default Banner;
