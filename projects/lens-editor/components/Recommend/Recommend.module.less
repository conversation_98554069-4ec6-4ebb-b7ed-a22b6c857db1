@import '../../../../common/styles/mixins.less';

.recommend {}

.recommendTitle {
  margin-bottom: 24px;
  color: #fff;
  font-size: 18px;
  line-height: 25px;
}

.recommendList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.recommendItem {
  position: relative;
  width: 191px;
  height: 340px;
  display: block;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;

  &.horizental {
    width: 635px;
  }
}

.recommendInfo {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 60px;
  padding: 0 10px 12px 12px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: linear-gradient(180deg, rgba(61, 101, 118, 0) 0%, #3D6576 100%);
  flex-shrink: 0;
}

.infoAvatar {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  border-radius: 8px;
  overflow: hidden;
}

.infoNick {
  .text-ellipsis();
  margin-right: 10px;
  color: #fff;
  font-size: 12px;
  line-height: 17px;
  flex: 1;
  flex-shrink: 1;
}

.likeIcon {
  width: 12px;
  height: 11px;
  margin-right: 2px;
  background-image: url(../../assets/images/pages/likeIcon.png);
  background-size: 100% 100%;
}

.likeNum {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  line-height: 17px;
}