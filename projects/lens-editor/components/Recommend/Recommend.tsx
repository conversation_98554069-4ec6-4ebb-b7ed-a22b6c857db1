import React, { useEffect } from 'react';
import styles from './Recommend.module.less';
import { useHomeStore } from '../../store/homeStore';
import { Image, Loader, Center } from '@mantine/core';
import classnames from 'classnames';
import {
  $WebVideoInfo,
  emResolution,
} from 'jce/interact/aigc/proto_lens_script/proto_lens_script_comm.jce?emit=module';
import InfiniteScroll from 'react-infinite-scroll-component';
import { FALLBACK_AVATAR, FALLBACK_IMAGE } from '../../constants/config';

const Recommend = () => {
  const { recommends, total, getList } = useHomeStore();

  const handleItem = (recommend: $WebVideoInfo) => {
    window.open(recommend.strVideoUrl);
  };
  useEffect(() => {
    getList(true);
  }, []);

  const handleNext = () => {
    getList(false);
  };

  return (
    <section className={styles.recommend}>
      <h2 className={styles.recommendTitle}>精彩推荐</h2>
      <InfiniteScroll
        dataLength={recommends.length}
        hasMore={total > recommends.length}
        loader={
          <Center>
            <Loader size="xl" />
          </Center>
        }
        next={handleNext}>
        <ul className={styles.recommendList}>
          {recommends.map(recommend => (
            <li
              key={recommend.strScriptId}
              className={classnames(
                styles.recommendItem,
                recommend.stSize.stResolution === emResolution.EM_VIDEO_720P_H ? styles.horizental : null
              )}
              onClick={() => handleItem(recommend)}>
              <Image src={recommend.strCoverImg} fallbackSrc={FALLBACK_IMAGE} />
              <div className={styles.recommendInfo}>
                <div className={styles.infoAvatar}>
                  <Image src={recommend.stUserInfo.strAvatarUrl} fallbackSrc={FALLBACK_AVATAR} />
                </div>
                <p className={styles.infoNick}>{recommend.stUserInfo.strNick}</p>
                {/* <div className={styles.likeIcon}/> */}
                {/* <p className={styles.likeNum}>{recommend.lStar}</p> */}
              </div>
            </li>
          ))}
        </ul>
      </InfiniteScroll>
    </section>
  );
};

export default Recommend;
