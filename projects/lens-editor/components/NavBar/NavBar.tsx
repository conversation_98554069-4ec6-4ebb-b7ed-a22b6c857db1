import React, { useMemo } from 'react';
import styles from './NavBar.module.less';
import { Link } from 'react-router-dom';
import mvIcon from '../../assets/images/pages/mvIcon.png';
import mineIcon from '../../assets/images/pages/mineIcon.png';
import starIcon from '../../assets/images/pages/starIcon.png';
import { AppShell, ScrollArea } from '@mantine/core';
import { useHomeStore } from '../../store/homeStore';

const HomeNav: React.FC = () => {
  const { strDocsUrl } = useHomeStore();

  const navs = useMemo(() => {
    return [
      {
        icon: mvIcon,
        text: '首页',
        link: '/home',
      },
      {
        icon: mineIcon,
        text: '我的作品',
        link: '/userinfo',
      },
      // {
      //   icon: starIcon,
      //   text: '教程',
      //   link: '/editor/new',
      // }
    ]
  }, []);

  return (
    <AppShell.Navbar className={styles.homeNav}>
      <AppShell.Section grow component={ScrollArea}>
        <div className={styles.navList}>
          {navs.map((nav, index) => (
            <Link key={index} className={styles.navItem} to={nav.link}>
              <img className={styles.navItemIcon} src={nav.icon} />
              <p className={styles.navItemText}>{nav.text}</p>
            </Link>
          ))}
        </div>
      </AppShell.Section>
      <AppShell.Section>
        <Link className={styles.navHelp} to={strDocsUrl}>
          <div className={styles.navHelpIcon} />
          <div className={styles.navHelpText}>帮助 & 反馈</div>
        </Link>
      </AppShell.Section>
    </AppShell.Navbar>
  )
};

export default HomeNav;
