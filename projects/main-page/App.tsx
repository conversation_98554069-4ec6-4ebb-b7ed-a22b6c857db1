import React from 'react';
import { MantineProvider, MantineColorScheme } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { BrowserRouter } from 'react-router-dom';
import { useLocalStorage } from '@mantine/hooks';

// 引入组件自身的样式
import styles from './App.less';

export default function App() {
  const [colorScheme, setColorScheme] = useLocalStorage<MantineColorScheme>({
    key: 'mantine-color-scheme',
    defaultValue: 'light',
  });

  return (
    <MantineProvider
      defaultColorScheme={colorScheme}
      theme={{
        primaryColor: 'blue',
      }}>
      <Notifications />
      <BrowserRouter basename="/tme-build-web/lens/main-page">
        <div className={styles.mainContainer}>
          <h1 className="text-center">欢迎来到主站</h1>
        </div>
      </BrowserRouter>
    </MantineProvider>
  );
}
