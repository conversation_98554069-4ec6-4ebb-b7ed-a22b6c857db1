import { defineConfig, BUILD_TARGET } from '@tencent/tme-build';
import path from 'path';
import { FastJcePlugin } from '@tencent/fast-jce-plugin';
import { RealTimeLogPlugin } from '@tencent/useful-plugins';
import externalsConfig from './scripts/build/externalsConfig';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';

export default defineConfig(() => {
  return {
    cdnHost: '',
    cdnPathname: '/projects',
    devPathname: '/projects',
    entryPathname: '/projects',
    externalsConfig,
    devServer: {
      port: 30000 //本地开发模式默认 30000端口
    },
    plugins: [new FastJcePlugin({ jcePath: 'jce' }), new RealTimeLogPlugin()],
    chainTmeBuild: ({ chain, target, buildConfig }) => {
      if (target === BUILD_TARGET.WEB) {
        chain.devtool('source-map');
        chain.devtool(false);
        chain.stats('normal');
        chain.devServer.compress(true);
        chain.devServer.port(3000);
        chain.optimization.minimize(true);

        // 配置less css module
        chain.module
          .rule('less')
          .test(/\.less$/)
          .type('css/module')
          .use('less')
          .loader('less-loader');

        // 构建指令透传analyze参数，注入BundleAnalyzerPlugin，产物生成分析HTML报告
        // @ts-ignore
        if (buildConfig.command.analyze) {
          chain.plugin('BundleAnalyzerPlugin').use(BundleAnalyzerPlugin, [{ analyzerMode: 'static' }]);
        }

        chain.resolve.alias
          .set('react-native', '@hippy/react-web')
          .set('projects', path.resolve(process.cwd(), 'projects'))
          .set('library', path.resolve(process.cwd(), 'library'))
          .set('common', path.resolve(process.cwd(), 'common'))
          .set('styles', path.resolve(process.cwd(), 'styles'))
          .set('styles_mediastyle', path.resolve(process.cwd(), 'styles_mediastyle'))
          .set('@tencent/kg-base', require.resolve('@tencent/kg-base/lib/index.web.js'))
          .set('@tencent/kg-ajax', require.resolve('@tencent/kg-ajax/lib/index.web.js'))
          .set('@tencent/kg-report', require.resolve('@tencent/kg-report/lib/index.web.js'))
          .set('@tencent/kg-bridge', require.resolve('@tencent/kg-bridge/lib/index.web.js'))
          .set('@tencent/qmkege-ajax', require.resolve('@tencent/qmkege-ajax/lib/index.web.js'))
          .set('@tencent/qmkege-bridge', require.resolve('@tencent/qmkege-bridge/lib/index.web.js'))
          .set('@tencent/tme-interface-tme', require.resolve('@tencent/tme-interface-tme/esm/index.web.js'))
          .set('@tencent/tme-interface-qmkege', require.resolve('@tencent/tme-interface-qmkege/esm/index.web.js'));
      }
      return chain;
    },
  };
});
