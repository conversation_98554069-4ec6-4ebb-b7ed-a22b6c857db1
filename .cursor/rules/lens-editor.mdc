---
description:
globs: projects/lens-editor/**/*
alwaysApply: false
---
---
name: lens-editor
description: MV视频编辑器项目结构和主要组件说明
globs: projects/lens-editor/**/*

# 棱镜编辑器 (Lens-Editor)

Lens-Editor 是 MV 视频编辑工具的核心组件，包含视频编辑、预览和素材选择等功能。

## 目录结构

- `/components`：组件目录
  - `/VideoEditor`：视频编辑核心组件
  - `/Storyboard`：故事板组件，二期已废弃，统一整合至VideoEditor
  - `/Preview`：预览组件
  - `/Scene`：场景相关组件，二期已废弃，统一整合至VideoEditor
  - `/Subject`：主题相关组件
  - `/Layout`：布局组件
  - `/NavBar`：导航栏组件
  - `/common`：公共UI组件
- `/store`：状态管理
  - `editorStore.ts`：编辑器状态管理
  - `timelineStore.ts`：时间轴状态管理
  - `homeStore.ts`：首页状态管理
  - `userInfoStore.ts`：用户信息状态管理
  - `apiActions.ts`：API操作相关
- `/hooks`：自定义Hooks
- `/utils`：工具函数
- `/constants`：常量定义
- `/pages`：页面组件
- `/routes`：路由定义
- `/assets`：静态资源

## 技术栈

- 状态管理：Zustand
- UI组件：Mantine UI
- 拖拽功能：react-beautiful-dnd 和 @hello-pangea/dnd
- 时间轴编辑：@xzdarcy/react-timeline-editor
- 视频处理：@webav/av-canvas 和 @webav/av-cliper

## 主要流程

编辑器允许用户：
1. 选择主题和场景
2. 导入和编辑视频素材
3. 在时间轴上调整视频片段
4. 预览编辑效果
5. 导出最终MV作品
