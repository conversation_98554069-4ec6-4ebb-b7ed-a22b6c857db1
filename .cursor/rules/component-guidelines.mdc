---
description:
globs: **/components/**/*.tsx
alwaysApply: false
---
---
name: component-guidelines
description: 项目组件开发规范和最佳实践
globs: **/components/**/*.tsx

# 组件开发规范

本项目组件开发主要基于 React 函数组件和 Mantine UI 库，遵循以下规范。

## 组件结构

```typescript
// 组件结构示例
import { useState, useEffect } from 'react';
import { Button, Text } from '@mantine/core';
import styles from './ComponentName.less';

// 定义组件 Props 类型
interface ComponentNameProps {
  title: string;
  onAction: () => void;
  // 其他属性...
}

// 使用函数组件
export const ComponentName = ({ title, onAction }: ComponentNameProps) => {
  // 状态管理
  const [state, setState] = useState(initialState);

  // 副作用
  useEffect(() => {
    // 执行副作用...
    return () => {
      // 清理函数...
    };
  }, [dependencies]);

  // 事件处理函数命名规范：使用 handle 前缀
  const handleClick = () => {
    setState(newState);
    onAction();
  };

  // 渲染
  return (
    <div className={styles.container}>
      <Text className={styles.title}>{title}</Text>
      <Button onClick={handleClick}>操作按钮</Button>
    </div>
  );
};
```

## 命名规范

- 组件文件名：PascalCase（首字母大写）
- 组件名称：与文件名一致，使用 PascalCase
- 事件处理函数：使用 `handle` 前缀，如 `handleClick`
- Props 类型：组件名+Props，如 `ButtonProps`
- CSS 类名：使用小写，单词之间用连字符（-）分隔

## UI 组件使用

项目主要使用 Mantine UI 组件库，应遵循以下原则：

1. 优先使用 Mantine 提供的组件，保持 UI 一致性
2. 自定义样式时，使用 Mantine 的 `className` 属性，而不是内联样式
3. 使用 Mantine 的主题系统进行样式定制

```typescript
// Mantine 组件使用示例
import { Button, Text, Group } from '@mantine/core';
import styles from './Example.less';

export const Example = () => {
  return (
    <Group className={styles.container}>
      <Text>示例文本</Text>
      <Button variant="filled">主要按钮</Button>
      <Button variant="outline">次要按钮</Button>
    </Group>
  );
};
```

## 组件封装原则

1. 单一职责：每个组件只负责一个功能点
2. 可复用性：组件设计应考虑复用场景
3. 可测试性：避免复杂逻辑，便于单元测试
4. 性能优化：适当使用 React.memo、useMemo 和 useCallback
