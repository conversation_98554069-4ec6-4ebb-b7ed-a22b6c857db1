---
description: 棱镜项目概述和结构说明
globs:
alwaysApply: false
---
---
name: project-overview
description: 棱镜项目概述和结构说明
globs: **/*

# 棱镜项目概述

棱镜(lens)是一个MV视频制作工具，用户可以通过设定视频素材，生成MV视频。

## 项目架构

- 采用 Monorepo 结构，使用 Yarn 作为包管理工具
- 基于 React 18 + TypeScript 开发
- UI库：主要使用 Mantine UI (@mantine/core)
- 状态管理：使用 Zustand
- 路由：React Router v6
- 请求：使用内部的请求库（基于axios）

## 目录结构

- `/projects`：各个子项目，每个子文件夹是一个独立项目
  - `/projects/lens-editor`：MV编辑器主体，主页目前也在这个项目里
  - `/projects/main-page`：应用主页，暂时没有使用，可忽略该目录
- `/common`：公共组件、样式和工具函数
- `/jce`：前后台接口协议定义（通过脚本生成）
- `/scripts`：构建和工具脚本

## 开发命令

- 启动开发环境：`yarn dev`
- 构建项目：`yarn build`
- 代码检查：`yarn lint`
- 代码修复：`yarn lint:fix`

## 注意
你改完代码，不需要额外执行 yarn dev 命令，因为我已经有yarn dev在执行，并且会监听文件更新，所以你不需要执行yarn dev。
