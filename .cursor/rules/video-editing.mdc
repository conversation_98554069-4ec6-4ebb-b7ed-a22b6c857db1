---
description:
globs:
alwaysApply: true
---
---
name: video-editing
description: 项目视频编辑功能相关说明
globs: **/VideoEditor/**/*.tsx,**/Timeline/**/*.tsx

# 视频编辑功能

本项目的核心功能是视频编辑，主要通过时间轴编辑、素材导入和效果预览来完成MV制作。

## 主要组件

- `VideoEditor`: 视频编辑器核心组件，集成了时间轴、预览和素材管理
- `Timeline`: 时间轴组件，处理视频片段的排序和时长调整
- `Preview`: 预览组件，实时显示编辑效果
- `Storyboard`: 故事板组件，展示视频分镜和结构，二期已废弃，统一整合至VideoEditor

## 关键技术
针对文档，需要用 use context7 或 use deepwiki 等mcp工具查询文档内容。
- 使用 `@xzdarcy/react-timeline-editor` 实现时间轴编辑功能，相关文档:https://zdarcy.com/editor-demo/editor-basic
- 使用 `@webav/av-canvas` 和 `@webav/av-cliper` 处理视频渲染和剪辑，av-cliper文档：https://webav-tech.github.io/WebAV/_api/av-cliper/modules.html, av-canvas文档：本项目工程目录下 /webav_avcanvas.md
- 使用拖拽库 (`react-beautiful-dnd`/`@hello-pangea/dnd`) 实现素材拖拽排序

## 视频处理流程

1. **素材导入**: 用户上传视频素材或选择提供的素材
2. **时间轴编辑**: 在时间轴上安排视频片段顺序和时长
3. **效果应用**: 添加转场、特效、滤镜等
4. **预览**: 实时预览编辑效果
5. **导出**: 生成最终的MV作品

## 状态管理

视频编辑状态主要通过以下Store管理：

- `editorStore`: 管理编辑器整体状态
- `timelineStore`: 专门处理时间轴状态与操作

## 示例实现

```typescript
// 时间轴组件示例
import { TimelineEditor } from '@xzdarcy/react-timeline-editor';
import { useEditorStore, useTimelineStore } from '@/store';

export const Timeline = () => {
  const { tracks, currentTime } = useTimelineStore(state => ({
    tracks: state.tracks,
    currentTime: state.currentTime
  }));

  const { updateTrack, setCurrentTime } = useTimelineStore(state => ({
    updateTrack: state.updateTrack,
    setCurrentTime: state.setCurrentTime
  }));

  const handleTrackChange = (newTracks) => {
    updateTrack(newTracks);
  };

  return (
    <TimelineEditor
      tracks={tracks}
      currentTime={currentTime}
      onChange={handleTrackChange}
      onTimeChange={setCurrentTime}
      // 其他配置...
    />
  );
};
```
