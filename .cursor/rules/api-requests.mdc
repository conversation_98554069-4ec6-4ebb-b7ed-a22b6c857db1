---
description:
globs: projects/lens-editor/**/*.tsx
alwaysApply: false
---
---
name: api-requests
description: 项目网络请求和API接口使用规范
globs: **/request.ts,**/jce/**/*.d.ts

# 网络请求和API接口规范

本项目使用封装的 axios 实例和 JCE 接口定义进行网络请求。

## 请求实例

项目使用 `common/request.ts` 中封装的 axios 实例进行网络请求，该实例已配置：

- 基础URL
- 凭证管理 (withCredentials)
- 超时设置
- 响应拦截器（处理登录态失效等情况）

```typescript
// 使用方式
import req from '@/common/request';

// GET 请求
const getData = async (params) => {
  try {
    const response = await req.get('/endpoint', { params });
    return response.data;
  } catch (error) {
    console.error('获取数据失败', error);
    throw error;
  }
};

// POST 请求
const postData = async (data) => {
  try {
    const response = await req.post('/endpoint', data);
    return response.data;
  } catch (error) {
    console.error('提交数据失败', error);
    throw error;
  }
};
```

## JCE 接口定义

JCE (Java Communication Environment) 接口定义文件位于 `/jce` 目录下，由脚本自动生成，包含：

- `.jce` 文件：接口原始定义
- `.d.ts` 文件：TypeScript 类型声明

处理流程：
1. 通过 `yarn jce` 命令从 git 仓库拉取最新的 JCE 文件
2. 脚本自动生成对应的 TypeScript 类型声明
3. 在前端代码中引用这些类型

## 登录状态管理

`common/request.ts` 中包含登录态管理的逻辑：

- 响应拦截器检测 401/403 错误
- 401 错误时刷新页面
- 403 错误时触发登录弹窗
- 提供登录事件监听器注册机制

## 最佳实践

1. API 请求函数应集中管理，推荐放在专门的目录或文件中
2. 使用 TypeScript 类型确保请求参数和响应数据的类型安全
3. 妥善处理请求错误和加载状态
