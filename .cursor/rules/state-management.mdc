---
description:
globs: projects/lens-editor/**/*.tsx
alwaysApply: false
---
---
name: state-management
description: 项目状态管理模式和最佳实践
globs: **/store/*.ts

# 状态管理指南

本项目主要使用 Zustand 进行状态管理，以下是相关使用规范和示例。

## Zustand 使用规范

1. 每个模块使用单独的 store
2. store 应遵循单一职责原则
3. 复杂状态逻辑应在 store 中实现，不在组件中处理
4. 使用 selector 优化组件渲染性能

## Store 设计结构

```typescript
// 典型的 store 结构
import { create } from 'zustand';

interface StoreState {
  // 状态数据
  data: Type;
  loading: boolean;
  error: Error | null;

  // 操作方法
  fetchData: () => Promise<void>;
  updateData: (newData: Type) => void;
  resetStore: () => void;
}

export const useStore = create<StoreState>((set, get) => ({
  // 初始状态
  data: initialData,
  loading: false,
  error: null,

  // 方法实现
  fetchData: async () => {
    set({ loading: true, error: null });
    try {
      const data = await apiCall();
      set({ data, loading: false });
    } catch (error) {
      set({ error, loading: false });
    }
  },

  updateData: (newData) => set({ data: newData }),

  resetStore: () => set({ data: initialData, loading: false, error: null }),
}));
```

## 在组件中使用

```typescript
// 推荐使用方式
function Component() {
  // 只订阅需要的状态，使用 selector 优化性能
  const data = useStore(state => state.data);
  const { fetchData, updateData } = useStore(state => ({
    fetchData: state.fetchData,
    updateData: state.updateData
  }));

  // ...组件逻辑
}
```

## 主要 Store 文件

- `editorStore.ts`: 编辑器核心状态管理
- `timelineStore.ts`: 时间轴状态管理
- `homeStore.ts`: 首页状态管理
- `userInfoStore.ts`: 用户信息管理
