module.exports = {
  extends: ["stylelint-config-standard"],
  rules: {
    // 自定义规则
    "string-quotes": "single", // 使用单引号
    "declaration-block-trailing-semicolon": "always", // 要求或禁止声明块的末尾有分号
    "number-leading-zero": "always", // 要求或禁止小于 1 的小数的前导 0
    "length-zero-no-unit": true, // 禁止零长度的单位
    "color-hex-case": "lower", // 指定十六进制颜色大小写
    "color-hex-length": "short", // 指定十六进制颜色是否使用缩写
    "selector-class-pattern": null, // 不限制类名的命名规则
    "property-no-vendor-prefix": null, // 允许使用浏览器前缀
    "value-no-vendor-prefix": null, // 允许使用浏览器前缀
    "selector-pseudo-class-no-unknown": [
      true,
      {
        ignorePseudoClasses: ["global"],
      },
    ],
    "at-rule-no-unknown": [
      true,
      {
        ignoreAtRules: [
          "extend",
          "at-root",
          "debug",
          "warn",
          "error",
          "if",
          "else",
          "for",
          "each",
          "while",
          "mixin",
          "include",
          "content",
          "return",
          "function",
          "tailwind",
          "apply",
          "responsive",
          "variants",
          "screen",
        ],
      },
    ],
  },
};
