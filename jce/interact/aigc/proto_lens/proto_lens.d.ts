/* eslint-disable */
/* tslint:disable */

export declare namespace proto_lens {
  export const LENS_MOD_ID = 298018025;

  export const KeyCommmonConfList = "lensCommmonConfList";

  export const KeyTranAccessToken = "lensTranAccessToken";

  export const KeyLoginToken = "lensLoginToken";

  export const KeyIncrUID = "lensIncrUID";

  export const KeyUserInfo = "lensUserInfo";

  export const KeyPhone2UID = "lensPhone2UID";

  export const KeyEmail2UID = "lensEmail2UID";

  export const KeyOpenID2UID = "lensOpenID2UID";

  export enum OptType {
    "OptTypeAdd"= 1,
    "OptTypeUpdate"= 2,
    "OptTypeDel"= 3,
  }

  export interface $CommonConf {
    id: number;
    name: string;
    remark: string;
    value: string;
  }
  export const CommonConf: $CommonConf;

  export interface $CommonConfList {
    confs: Array<proto_lens.$CommonConf>;
    nextId: number;
  }
  export const CommonConfList: $CommonConfList;

  export interface $UserInfo {
    UID: number;
    phone: string;
    email: string;
    openID: string;
    name: string;
    ename: string;
    createTS: number;
    updateTS: number;
    strNick: string;
    strAvatarUrl: string;
  }
  export const UserInfo: $UserInfo;

}

export const LENS_MOD_ID = 298018025;

export const KeyCommmonConfList = "lensCommmonConfList";

export const KeyTranAccessToken = "lensTranAccessToken";

export const KeyLoginToken = "lensLoginToken";

export const KeyIncrUID = "lensIncrUID";

export const KeyUserInfo = "lensUserInfo";

export const KeyPhone2UID = "lensPhone2UID";

export const KeyEmail2UID = "lensEmail2UID";

export const KeyOpenID2UID = "lensOpenID2UID";

export enum OptType {
  "OptTypeAdd"= 1,
  "OptTypeUpdate"= 2,
  "OptTypeDel"= 3,
}

export interface $CommonConf {
  id: number;
  name: string;
  remark: string;
  value: string;
}
export const CommonConf: $CommonConf;

export interface $CommonConfList {
  confs: Array<proto_lens.$CommonConf>;
  nextId: number;
}
export const CommonConfList: $CommonConfList;

export interface $UserInfo {
  UID: number;
  phone: string;
  email: string;
  openID: string;
  name: string;
  ename: string;
  createTS: number;
  updateTS: number;
  strNick: string;
  strAvatarUrl: string;
}
export const UserInfo: $UserInfo;