module proto_lens
{
    const int LENS_MOD_ID = 298018025;
    
    // 存储信息
    const string KeyCommmonConfList = "lensCommmonConfList"; // 通用配置列表
    const string KeyTranAccessToken = "lensTranAccessToken"; // 翻译access token
    const string KeyLoginToken = "lensLoginToken"; // 用户token
    const string KeyIncrUID = "lensIncrUID"; // 用户ID，递增
    const string KeyUserInfo = "lensUserInfo"; // 用户信息
    const string KeyPhone2UID = "lensPhone2UID"; // 手机号到用户ID的映射
    const string KeyEmail2UID = "lensEmail2UID"; // 邮箱到用户ID的映射
    const string KeyOpenID2UID = "lensOpenID2UID"; // 微信开放平台openID到用户ID的映射

    // 操作类型
    enum OptType
    {
        OptTypeAdd    = 1, // 添加
        OptTypeUpdate = 2, // 修改
        OptTypeDel    = 3, // 删除
    };

    // 通用配置
    struct CommonConf
    {
        0 optional int id; // 配置ID
        1 optional string name; // 配置名
        2 optional string remark; // 备注
        3 optional string value;  // 配置值
    };

    // 通用配置列表
    struct CommonConfList
    {
        0 optional vector<CommonConf> confs; // 配置列表
        1 optional int nextId; // 下一个 id
    };

    // 用户信息
    struct UserInfo
    {
        0 optional int UID; // 用户ID
        1 optional string phone; // 手机号
        2 optional string email; // 邮箱
        3 optional string openID; // 微信开放平台openID
        4 optional string name; // 用户名
        5 optional string ename; // 英文名
        6 optional long createTS; // 创建时间
        7 optional long updateTS; // 更新时间
        8 optional string strNick; // 用户昵称
        9 optional string strAvatarUrl; // 用户头像
    };
};
