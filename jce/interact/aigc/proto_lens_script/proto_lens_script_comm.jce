#include "../proto_lens/proto_lens.jce"


module proto_lens_script_comm
{
    // UserInfo 用户个人信息key，后缀是：用户uid
    const string strKeyUserInfoPrefix = "LENS_USER_INFO";
    // ScriptInfo 脚本信息，后缀是：脚本 ID
    const string strKeyScriptPrefix = "SCRIPT_INFO";
	// RoleInfo 角色信息，后缀是：角色 ID
	const string strKeyRolePrefix = "ROLE_INFO";
    // 可用的流程列表
    const string strKeyProcessList = "LENS_MV_PROCESS_LIST";
	// 可用的流程列表改版后
	const string strKeyProcessListV2 = "LENS_MV_PROCESS_LIST_V2";
    // 流程列表排序：
    const string strKeyProcessSort = "LENS_MV_PROCESS_LIST_SORT";
    // 图片风格list

    const string strPicStyleList = "LENS_MV_PIC_STYLE_LIST";
	// mv类型list
	const string strMvTypeList = "LENS_MV_TYPE_LIST_KEY";
    // 智能图片和智能视频模型
	const string SmartPicMode = "SmartPicMode";
	const string SmartVideoMode = "SmartVideoMode";
	// 画面风格
	const string strKeyPictureStyleList = "PICTURE_STYLE_LIST";
	// 品质列表
	const string strKeyQualityList = "QUALITY_LIST_KEY";


	enum EmGender{
        EM_GENDER_FEMALE    =    1, // 女性
        EM_GENDER_MALE      =    2, // 男性
    };
    enum EmRoleType{
        EM_ROLE_TYPE_HUMAN      = 1, // 人类
        EM_ROLE_TYPE_NON_HUMAN  = 2, // 非人类
    };
    enum EmGenType{
        EM_GEN_TYPE_BY_AI = 1,      // AI生成
        EM_GEN_TYPE_BY_SELF = 2,    // 自己创作的
		EM_GEN_TYPE_BY_AI_REGEN = 3, // AI重新生成
    };

    // 运行状态
    enum emStatus{
        EM_STATUS_INIT       = 0,        // 初始状态
        EM_STATUS_RUNNING    = 1,        // 运行中
        EM_STATUS_SUCC       = 2,        // 生成成功
        EM_STATUS_FAIL       = 3,        // 生成失败
		EM_STATUS_NEED_AUDIT  = 4,        // 需要审核或者预览
    };

	enum EmRegion{
		EM_REGION_EAST_ASIA			= 0,	// 东亚
		EM_REGION_EUROPE_AMERICA	= 1,	// 欧美
	};

	struct Region{
		0 optional EmRegion eRegion;
		1 optional string strRegionName;
	};

    // 角色信息
    struct RoleInfo{
        0 optional string strId;                    // 角色 ID
        1 optional string strName;                  // 角色名称
        2 optional string strUrl;                   // 角色图片
        3 optional string strGen;                   // 性别
        4 optional string strAge;                   // 年纪，允许写中年老年这种
        5 optional EmRoleType eRoleType;            // 角色类型	-- 二期废弃的字段
        6 optional string strHair;                  // 发型描述
        7 optional string strCloth;                 // 衣服描述
        8 optional string strDesc;                  // 角色描述
        9 optional string strFaceReferPic;          // 面部参考图
        10 optional string strDressRefrePic;         // 服饰参考图
        11 optional EmGenType eGenType;              // 生成类型
        12 optional vector<string> vecUseScript;    // 关联的脚本ID
        13 optional emStatus eStatus;               // 角色状态
        14 optional string strRoleTips;             // 角色提示词
        15 optional int iIndex;                     // 角色序号
		16 optional int iUseStoryboardCnt;			// 有多少个分镜使用
		17 optional int iUse;						// 是否使用
		18 optional EmRegion eRegion;				// 地区
    };

    // 环境信息
    struct EnvInfo{
        0 optional string strId;                    // 环境 ID
        1 optional string strName;                  // 环境名称
        2 optional string strUrl;                   // 环境图片
        3 optional string strDesc;                  // 环境描述
        4 optional EmGenType eGenType;              // 生成类型
        5 optional vector<string> vecUseScript;     // 关联的脚本ID
        6 optional emStatus eStatus;                // 环境状态
        7 optional string strEnvTips;               // 环境提示词
        8 optional int iIndex;                      // 环境序号
    };

	enum emResolution
	{
		EM_VIDEO_720P_H = 0,			// 720p横屏, 1280x720
		EM_VIDEO_720P_V = 1,			// 720p竖屏, 720x1280
	};

	// 视频尺寸
	struct VideoSize{
		0 optional long   lWidth;   // 宽
		1 optional long   lHeight;  // 高
		2 optional string sizeName; // 外显名称
		3 optional emResolution stResolution; // 分辨率
	};

    // 视频信息
    struct WebVideoInfo{
        0 optional string strScriptId;          // 脚本 ID
        1 optional string strVideoUrl;          // 视频 Url
        2 optional string strCoverImg;          // 封面图片
        3 optional string strPublicTime;        // 发布时间
        4 optional proto_lens::UserInfo stUserInfo;         // 用户信息
        5 optional long lStar;                  // 点赞数 -- 二期做
        6 optional string strSongName;          // 歌曲名称
        7 optional string strTopic;             // 脚本主题
		8 optional VideoSize stSize;            // 视频大小
    };

    // 用户的个人信息(包含脚本、角色、环境等)
    struct UserScriptInfo{
        0 optional int iUid;                            // 用户 UID
        1 optional vector<WebVideoInfo>  VecVideo;      // 视频信息
        2 optional vector<RoleInfo> vecRole;            // 角色信息
        3 optional vector<EnvInfo>  vecEnv;             // 环境信息
		4 optional map<string, int> mapMvTypeUseCnt;	// 每个MV 类型的使用次数
    };

    enum emDataType{
        EM_DATA_TYPE_INT = 1,
        EM_DATA_TYPE_STRING = 2,
    };
    struct OPDataItem{
        0 optional int iPos;                    // 位置
        1 optional string strValueField;        // 字段名称
        2 optional emDataType eType;         // 字段类型
    };
    struct OutPutData{
        0 optional vector<OPDataItem> vecItem;  // 输出函数
    };
    struct ExecFunc{
        0 optional string strExecFunc;                  // 执行函数
        1 optional map<string, string> mapArgs;         // 执行函数需要的参数
        2 optional string strDesc;                      // 执行函数说明
    };

    // 主流程类型
    enum emSubProcessType{
        EM_SUB_PROCESS_TYPE_TOPIC_DESC                   = 1,        // 生成主题
        EM_SUB_PROCESS_TYPE_SCENE_ROLE_ENV_DESC          = 2,        // 生成场景内容
        EM_SUB_PROCESS_TYPE_SCENE_ROLE_PIC               = 3,        // 生成角色
        EM_SUB_PROCESS_TYPE_SCENE_ENV_PIC                = 4,        // 生成环境
        EM_SUB_PROCESS_TYPE_STORYBOARD_DESC              = 5,        // 生成分镜内容
        EM_SUB_PROCESS_TYPE_STORYBOARD_IMAGE             = 6,        // 生成分镜图片
        EM_SUB_PROCESS_TYPE_VIDEO                        = 7,        // 生成视频
        EM_SUB_PROCESS_TYPE_MERGE                        = 8,        // 合并视频
		EM_SUB_PROCESS_TYPE_CHOOSE_MVTYPE                = 9,        // 选择 MV 类型
    };

    // 子流程配置
    struct SubProcessDo{
        0 optional string strSubProcessName;            // 子流程名称
        1 optional emSubProcessType eSubType;           // 子流程类型
        2 optional vector<ExecFunc> vecMainExecFunc;    // 主流程执行函数
        3 optional vector<ExecFunc> vecSingleExecFunc;  // 用户单独生成场景时候的执行函数（这里可能会和主流程有差异，）
        4 optional emStatus eStatus;                    // 子流程执行状态
    };

    // MV流程
    enum emFlow{
        EM_FLOW_DETERMIN_TOPIC     = 1,        // 主题确定
        EM_FLOW_MV_SCRIPT          = 2,        // 视频脚本
        EM_FLOW_STORYBOARD         = 3,        // 分镜画面
        EM_FLOW_VIDEO              = 4,        // 生成视频
        EM_FLOW_PREVIEW            = 5,        // 视频预览
    };

	struct MVQuality{
		0 optional string strId;        // 品质 ID
		1 optional string strName;      // 品质名称
		2 optional string strDesc;		// 品质说明
		3 optional int iCanUse;			// 是否可以使用
		4 optional string strVideoModelId;	// 视频使用模型
		5 optional string strMaterialUseRate;	// 素材使用率
		6 optional int iMaxUseCnt;						// 最大使用次数 字段配置在题材中生效
		7 optional vector<string> vecWhitelist;			// 白名单 字段配置在题材中生效
	};

	struct QualityList{
		0 optional vector<MVQuality> vecInfo;	// 品质列表
	};

	// 图片模型
	struct PictureModel{
		0 optional string strId;            // 模型 ID
		1 optional string strModel;         // 模型名称
		2 optional string strReferPic;      // 参考图
		3 optional string strTips;          // 说明
		4 optional vector<MVQuality> vecInfo; // 适用的品质列表
	};

	enum  eVideoModelType{
		EM_VIDEO_MODEL_TYPE_MOVEMENT = 1,	// 运镜
		EM_VIDEO_MODEL_TYPE_AI_GEN = 2,		// AI生成
	};

	// strKeyPictureStyleList
	enum emStyleChooseType{
		EM_STYLE_CHOOSE_TYPE_DEFAULE = 0,
		EM_STYLE_CHOOSE_TYPE_HUMAN = 1
	};

	struct ChooseItem{
		0 optional string strKey;		// key
		1 optional string strModelId;	// 图片模型
	};
	struct StyleModelItem{
		0 optional emStyleChooseType eType;		// 选择类型
		1 optional vector<ChooseItem> vecChoose;
	};
	struct PicStyle{
		0 optional string strConfId;						// 配置 ID
		1 optional string strName;							// 画面风格名称
		2 optional string strShowPic;						// 展示图片
		3 optional string strGuideTips;						// 引导提示词
		4 optional StyleModelItem stModel;					// 模型列表
		5 optional string strAdaptDesc;						// 适用说明
		6 optional string strRoleGuide;						// 角色引导词
		7 optional string strImgGuide;						// 图片引导词
		8 optional string strVideoGuide;					// 视频引导词
		9 optional string strNotAdaptDesc;					// 不适用说明
        10 optional string strMaterialStyleTag;             // 对应素材库的style_tag
		11 optional string strThemeKey;						// 题材key
		12 optional int iUseLips;							// 是否使用口型
	};
	struct PicStyleList{
		0 optional vector<PicStyle> vecInfo;
	};


	// 视频模型
	struct VideoModel{
		0 optional string strId;            // 模型 ID
		1 optional string strModel;         // 模型名称
		2 optional string strReferPic;      // 参考图
		3 optional string strTips;          // 说明
		4 optional eVideoModelType eType;   // 模型类型
		5 optional int iNeedTips;			// 是否需要图片提示词
	};


	// 主流程信息
    struct ProcessDo{
        0 optional emFlow eFlow;                 // 流程类型
        1 optional vector<SubProcessDo> vecSubProc;     // 子流程
    };

    // 流程信息配置 同一个类型不同的品质都有一套配置
    struct strProcessConf{
        0 optional vector<ProcessDo> vecConf;            // 流程配置
        1 optional string strConfId;                     // MV模板ID
        2 optional string strConfName;                   // MV模板名称
        3 optional string strShowPicUrl;                 // 流程对应的缩略图
        4 optional vector<string> showUsers;             // 可以显示该流程的用户，为空时表示所有人都可以看到
		5 optional MVQuality stQualityInfo;				 // MV品质
		6 optional string strDefaultPicModel;	 	 	// 默认图片模型
		7 optional string strDefaultVideoModel;	 		// 默视频片模型
		8 optional int iOnceMaxUseCnt;					 // 单人使用的次数
		9 optional string strDefaultPicStyle;			 // 默认画面风格
    };

    struct strProcessConfList {
        0 optional vector<strProcessConf> vecProcessList;  // 可选的流程列表
    };

    // MV模板
    struct MVTemplate {
        0 optional string strId;        // 模板 ID
        1 optional string strName;      // 模板名称
		3 optional vector<PictureModel> vecPicModel;	// 2- 废弃 图片模型
    };

    // 歌词信息
    struct Lyric{
        0 optional int iStartMs;             // 开始时间戳
        1 optional int iEndMs;               // 结束时间戳
        2 optional string strContent;        // 歌词信息
        3 optional int iDurationMs;          // 间隔时长
    };

    // 歌曲信息
    struct SongInfo{
        0 optional string strMid;               // 歌曲 Mid
        1 optional string strSongId;            // 歌曲 ID
        2 optional string strSongName;          // 歌曲信息
        3 optional string strSinger;            // 歌手
        4 optional string strOriFile;           // 原始文件
        5 optional vector<Lyric> veclyric;      // 歌词信息
        6 optional string strSummary;           // 歌曲摘要
        7 optional int iAudioLength;            // 歌曲长度 - 毫秒
        8 optional int iStartMs;                // 开始时间
        9 optional int iEndMs;                  // 结束时间
		10 optional unsigned int uKid;			// K 歌歌曲 Kid
    };


    // 运镜类型
    struct Movement{
        0 optional string strId;            // 运镜 ID
        1 optional string strDesc;          // 运镜描述
		2 optional string strReferPic;      // 参考图
		3 optional string strTips;          // 说明
		4 optional string strReferVideo;    // 参考视频
    };

	// 脚本信息
	struct ReferInfo{
		0 optional vector<string> vecPicUrl;		// 参考图
		1 optional vector<string> vecTag;			// 标签
		2 optional vector<string> vecUseChoose;			// 用户选择的标签
	};

    // 主题信息
    struct TopicInfo{
        0 optional string strId;                        // 唯一 ID
        1 optional PictureModel stPicModel;             // 图片模型 - MV 风格
        2 optional VideoSize stSize;                    // 视频大小
        3 optional MVTemplate stMVTemplate;             // (废弃)使用的 MV 模板(页面可显的 MV 类型) + 这里要选择一个MV品质
        4 optional SongInfo stSongInfo;                 // 歌曲信息
        5 optional string strInspirationSummary;        // 歌曲灵感摘要
        6 optional VideoModel stVideoModel;             // 视频模型
        7 optional Movement stMovement;                 // 运镜模型
		8 optional emStatus eStatus;					// 主题生成状态
		9 optional ReferInfo stReferInfo;				// 参考信息
		10 optional PicStyle stStyle;					// 画面风格（这里包含了上面的图片模型）
		11 optional vector<string> vecStyleId;			// 适配的画面风格 id
	};


    // 图片信息
    struct PicInfo{
        0 optional string strId;                // 图片 ID
        1 optional string strPicUrl;            // 图片 URl
        2 optional string strPicTips;           // 图片生成提示词
        3 optional EmGenType eGenType;          // 生成类型
        4 optional string stUseModelId;         // 生成图片使用的模型
        5 optional emStatus eStatus;            // 图片状态
        6 optional string strStoryboardDesc;    // 分镜描述
		7 optional map<string, string> mapExt;	// 扩展字段
    };

    // 视频信息
    struct VideoInfo{
        0 optional string strId;                // 视频 ID
        1 optional string strPicUrl;            // 基于哪个图片生成的 URL
        2 optional string strPicTips;           // 图片提示词
        3 optional string strVideo;             // 视频 Url
        4 optional string strVideoTips;         // 视频提示词
        5 optional string strVideoModelId;      // 视频生成使用的模型
        6 optional string strMovementId;        // 视频运镜ID
        7 optional emStatus eStatus;            // 视频状态
        8 optional string strStoryboardDesc;    // 分镜描述
        9 optional int iStartTs;                // 开始时间
        10 optional int iEndTs;                 // 结束时间
        11 optional int iTimeRange;             // 视频时长
		12 optional map<string, string> mapExt;	// 扩展字段
    };

    // 分镜信息
    struct StoryboardInfo{
        0  optional string            strId;         // 分镜 ID
        1  optional string            strSceneId;    // 场景ID
        2  optional string            strStory;      // 分镜故事
        3  optional emStatus          eStatus;       // 分镜状态
        4  optional PicInfo           stCurPic;      // 当前使用的照片
        5  optional vector<PicInfo>   vecPic;        // 历史图片
        6  optional VideoInfo         stCurVideo;    // 当前使用的视频
        7  optional vector<VideoInfo> vecVideo;      // 历史生成的视频
        8  optional vector<string>    vecRoleId;     // 关联的角色 ID
        9  optional string            strEnvId;      // 环境 ID
        11 optional int               iTimeRange;    // 场景时长(ms)
        12 optional string           strShotType;   // 景别(中景、远景等)
        13 optional int               iIndex;        // 分镜序号
		// 注意 10、14是删除的 tag，后续字段不要使用
    };

    // 场景信息
    struct SceneceDescItem{
        0 optional string strKey;
        1 optional string strValue;
    };
    struct SceneInfo{
        0 optional string strId;                        // 场景 ID
        1 optional string strLyric;                     // 场景歌词
        2 optional emStatus eStatus;                    // 场景状态
        3 optional string strSceneDesc;                 // 场景描述 tag-4 不要使用
        4 optional vector<SceneceDescItem>Desc;         // 场景描述
        5 optional vector<string> vecRoleId;            // 角色 ID
        6 optional vector<string> vecEnvId;             // 环境 ID
        7 optional vector<proto_lens_script_comm::StoryboardInfo> VecStoryboard; // 分镜信息
        8 optional int iStoryboardCnt;                  // 分镜数量
        9 optional int iIndex;                          // 场景序号
        10 optional string strTimeRange;                // 场景时长(ms) -- 因为存储原因这里先换成string
		11 optional int iStartTime;     				// 开始时间(ms)
		12 optional int iEndTime;		 				//  结束时间(ms)
		13 optional string strSceneTitle;				// 场景标题
    };

    // 字幕参数
	struct SubtitleParam
	{
		0 optional string             					font;           // 字体 ZiHunBianTaoTi-2.ttf
		1 optional string             					color;          // 颜色 "red" 十六进制"#FF0000"  RGB格式 "255, 0, 0"
		2 optional int             						fontsize;       // 字体大小 45
		3 optional string             					strokeColor;   	// 轮廓线颜色，留空代表没有轮廓线
		4 optional int             						strokeWidth;   	// 轮廓线宽度 默认为1
		5 optional string             					widthSize;     	// 字幕横向的尺寸（超过这个值，则自动换行），比如1280*720时，设置为1000
		6 optional float             					kerning;        // 字间距，可留空
		7 optional string             					align;          // 对齐方式 center | East | West | South | North
		8 optional int             						interline;      // 行间距
		9 optional string             					position;       // 字幕位置 支持的格式："45,150" "center,top"  "0.4,0.7"
		10 optional float             					crossfadeDuration;        // 渐入渐出的时长，单位秒 比如 0.2
	};

	struct ComVideoParam
	{
		0 optional proto_lens_script_comm::SubtitleParam            stSongName;       // 歌名字幕参数
		1 optional proto_lens_script_comm::SubtitleParam            stSinger;     	// 歌手名字幕参数
		2 optional proto_lens_script_comm::SubtitleParam           	stLyric;          // 歌词字幕参数
	};


	struct ComposeModel
	{
		0 optional string             					strModelId;      	// 模型/模板id，比如 compose_template_1
		1 optional string             					strName;    // 模型/模板外显名称
		2 optional string             					strRemark;         // 备注
		3 optional string             					strShowUrl;        // 模型/模板的demoUrl地址
		4 optional map<emResolution, ComVideoParam>		stParam;    // 不同视频分辨率，需要的视频参数
	};

    struct SubtitleItem{
        0 optional string strId;        // 字幕 ID
        1 optional int    iStartTimeMs; // 开始时间 ms
        2 optional int    iStopTimeMs;  // 结束时间 ms
        3 optional string strContent;   // 字幕内容 -- 4\5\6tag不要使用
        7 optional SubtitleParam stParam; // 字幕参数
    };

	enum emSubTitleType{
		EM_SUBTITLE_TYPE_SONGNAME  = 1,
		EM_SUBTITLE_TYPE_SINGER  = 2,
		EM_SUBTITLE_TYPE_LYRIC  = 3,
	};

    struct SubtitleTrack{
        0 optional string strId;                        // 轨道 ID
        1 optional vector<SubtitleItem> vecContent;     // 歌词/旁白内容
		2 optional emSubTitleType eType;				// 文本轨道类型
    };

    // 字幕信息
    struct SubtitleInfo{
        0 optional vector<SubtitleTrack> vecContent; // 歌词/旁白内容
    };

    // 音频信息
    struct AudioItem{
        0 optional string strId;             // 音频 ID
        1 optional int    iStartTimeMs;      // 轨道上的开始时间 ms
        2 optional int    iStopTimeMs;       // 轨道上的结束时间 ms
        3 optional int    iAudioStartTimeMs; // 音频本身的开始时间 ms
        4 optional int    iAudioStopTimeMs;  // 音频本身的结束时间 ms
        5 optional string strAudioUrl;       // 音频Url
        6 optional int    iAudioDuration;    // 音频时长
        7 optional float  fVolume;           // 音频音量，一般范围是0~100，75表示75%音量，210表示210%音量
    };
    struct AudioTrack{
        0 optional string strId;                    // 轨道 ID
        1 optional vector<AudioItem> vecContent;    // 音频信息
    };
    struct Audio{
        0 optional vector<AudioTrack> vecAudio;     // 音频信息
    };

	enum emFromSource{
		EM_FROM_SOURCE_USER = 0,				// 用户生产的
		EM_FROM_SOURCE_BATH_PRODUCTION = 1,		// 批量生成的
	};


    struct ScriptInfo{
        0 optional string strId;                        // 剧本 ID
        1 optional string strName;                      // 剧本名称
        2 optional strProcessConf stProcConf;           // 流程配置
        3 optional proto_lens_script_comm::emFlow eUserExecFlow; // 用户正在操作哪个流程
        4 optional TopicInfo stTopic;                   // MV主题信息
        5 optional proto_lens_script_comm::emFlow eFinishFlow; // 已经执行完的流程
        6 optional vector<SceneInfo> vecScene;          // 场景信息
        7 optional SubtitleInfo stSubtitle;             // 字幕信息
        8 optional Audio stAudio;                       // 音频信息
        9 optional long lModifyTime;                    // 修改时间
        10 optional string strVideoUrl;                  // 视频 Url
        11 optional string strCoverImg;                 // 封面图片
        12 optional int iIsAuto;                        // 是否自行执行
		13 optional emStatus eStatus;					// 脚本合成的状态
		14 optional int iUid;							// 用户 ID
		15 optional emFromSource eSource;				// 作品来源
		16 optional map<string, string> mapExt;			// 扩展信息
		17 optional vector<RoleInfo> vecRoleInfo;			// 当前脚本的角色 ID
		18 optional vector<EnvInfo> vecEnvInfo;			// 当前脚本的环境
    };

    // 执行器执行过程数据存储
    struct ActItem{
        0 optional string strSubProcessName;            // 子流程名称
        1 optional string strTaskId;                    // 任务 ID
        2 optional string strContent;                   // 任务内容
    };
    struct ActuatorInfo{
        0 optional vector<ActItem> vecAct;              // 执行器信息
    };

    // 回收站信息
    enum emRecycleType{
        EM_RECYCLE_TYPE_SCENE = 1,                  // 删除了场景
        EM_RECYCLE_TYPE_STORYBOARD = 2,             // 删除了分镜
    };
    struct RecycleScene{
        0 optional SceneInfo            stScene;
        1 optional string strPrevSceneId;   // 上一个场景Id
    };
    struct RecycleStoryboard{
        0 optional StoryboardInfo       stStoryBoard;
        1 optional string strSceneId;   // 场景ID
        2 optional string strPrevStoryboardId;  // 上一个分镜ID
    };
    struct RecycleItem{
        0 optional emRecycleType        eType;
        1 optional RecycleScene            stScene;
        2 optional RecycleStoryboard       stStoryBoard;
		3 optional string strId;			// 唯一 ID
    };
    struct RecycleInfo{
        0 optional vector<RecycleItem> vecInfo;
    };

    // prompt配置
    struct PromptItem{
        0 optional string strId;
        1 optional string strPromptName;    // prompt名称
        2 optional string strPromptText;
		3 optional string strLLMModel;		// 模型 ID
    };

    struct PromptConfList{
        0 optional vector<PromptItem> vecPrompt;
    };

	// 耗时&成本统计
	struct ModelCostMoney{
		0 optional string strExecFunc;					// 执行函数
		1 optional string strModelId;					// 模型 ID
		2 optional int	iCostMoney;						// 预留，第一期可不做。本次调用的花费，单位人民币（分）
	};
	struct SubProcCost{
		0 optional emSubProcessType eSubType;           	// 子流程类型
		1 optional int iCostTime;                       	// 耗费时间(秒)
		2 optional vector<ModelCostMoney> stCostMoney;  	// 耗费多少钱
	};

	struct GenCost{
		0 optional emFlow eType;             		// 流程类型
		1 optional vector<SubProcCost> vecSubProc;  // 子流程信息
	};

	// 脚本执行耗时统计
	struct ScriptGenCost{
		0 optional vector<GenCost> vecCost;             // 耗时&成本统计
	};

	struct MvTypeItem{
		0 optional string strID;				// 类型ID
		1 optional string strName;				// 类型名称
		2 optional string strKey;				// 唯一 Key
		3 optional string strAdapt;				// 适用范围
		4 optional string strNotAdapt;			// 不适用范围
		5 optional vector<string> vecCanUsePicModel;		// 可选的图片模型
		7 optional string strBasicPrcId;		// 基础流程 ID
		8 optional vector<MVQuality> vecQuality;	// 使用的品质
	};

	// MV题材列表
	struct MvType{
		0 optional vector<MvTypeItem> vecInfo;
	};

	enum emIDType{
		EM_ID_TYPE_KID                  = 1,        // K歌歌曲 ID
		EM_ID_TYPE_SONGID               = 2,        // 歌曲 SongId
		EM_ID_TYPE_SONG_DETAIL          = 3,        // 歌曲详情
		EM_ID_TYPE_SONG_DETAIL_FOR_POKE = 4,        // poke数据
	};

	struct QukuMsg{
		0 optional string strKid;				// K歌歌曲-kid
		1 optional string strMvUrl;				// 歌曲 MVurl
		2 optional emIDType eType;				// idType
		3 optional string strTargetId;          // 目标 ID
		4 optional map<string, string> mapExt;	// 扩展信息
	};

	enum BatchStatus{
		EM_BATCH_STATUS_INIT				= 0,	// 未开始
		EM_BATCH_STATUS_CAN_GEN				= 1,	// 可以开始生成
		EM_BATCH_STATUS_RUNNING				= 2,	// 生成中
		EM_BATCH_STATUS_VIDEO_AUDIT			= 3,	// 片段审核中
		EM_BATCH_STATUS_ALL_DONE			= 4,	// 全部生成结束
		EM_BATCH_STATUS_GEN_FAIL 			= 5,	// 生成失败
		EM_BATCH_STATUS_PUBLISH_QUKU		= 6,	// 已经回调到曲库
		EM_BATCH_STATUS_MV_AUDIT			= 7,	// 最终 MV 看一下再入曲库
		EM_BATCH_STATUS_RE_GEN				= 8,	// 重新生成
		EM_BATCH_STATUS_REFUSE				= 9,	// 敏感歌曲拒绝生产
	};

};
