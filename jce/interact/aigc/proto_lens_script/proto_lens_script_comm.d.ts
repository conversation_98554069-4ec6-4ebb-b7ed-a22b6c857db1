/* eslint-disable */
/* tslint:disable */

import * as $proto_lens from '../proto_lens/proto_lens';

export declare namespace proto_lens_script_comm {
  export const strKeyUserInfoPrefix = "LENS_USER_INFO";

  export const strKeyScriptPrefix = "SCRIPT_INFO";

  export const strKeyRolePrefix = "ROLE_INFO";

  export const strKeyProcessList = "LENS_MV_PROCESS_LIST";

  export const strKeyProcessListV2 = "LENS_MV_PROCESS_LIST_V2";

  export const strKeyProcessSort = "LENS_MV_PROCESS_LIST_SORT";

  export const strPicStyleList = "LENS_MV_PIC_STYLE_LIST";

  export const strMvTypeList = "LENS_MV_TYPE_LIST_KEY";

  export const SmartPicMode = "SmartPicMode";

  export const SmartVideoMode = "SmartVideoMode";

  export const strKeyPictureStyleList = "PICTURE_STYLE_LIST";

  export const strKeyQualityList = "QUALITY_LIST_KEY";

  export enum EmGender {
    "EM_GENDER_FEMALE"= 1,
    "EM_GENDER_MALE"= 2,
  }

  export enum EmRoleType {
    "EM_ROLE_TYPE_HUMAN"= 1,
    "EM_ROLE_TYPE_NON_HUMAN"= 2,
  }

  export enum EmGenType {
    "EM_GEN_TYPE_BY_AI"= 1,
    "EM_GEN_TYPE_BY_SELF"= 2,
    "EM_GEN_TYPE_BY_AI_REGEN"= 3,
  }

  export enum emStatus {
    "EM_STATUS_INIT"= 0,
    "EM_STATUS_RUNNING"= 1,
    "EM_STATUS_SUCC"= 2,
    "EM_STATUS_FAIL"= 3,
    "EM_STATUS_NEED_AUDIT"= 4,
  }

  export enum EmRegion {
    "EM_REGION_EAST_ASIA"= 0,
    "EM_REGION_EUROPE_AMERICA"= 1,
  }

  export interface $Region {
    eRegion: proto_lens_script_comm.EmRegion;
    strRegionName: string;
  }
  export const Region: $Region;

  export interface $RoleInfo {
    strId: string;
    strName: string;
    strUrl: string;
    strGen: string;
    strAge: string;
    eRoleType: proto_lens_script_comm.EmRoleType;
    strHair: string;
    strCloth: string;
    strDesc: string;
    strFaceReferPic: string;
    strDressRefrePic: string;
    eGenType: proto_lens_script_comm.EmGenType;
    vecUseScript: Array<string>;
    eStatus: proto_lens_script_comm.emStatus;
    strRoleTips: string;
    iIndex: number;
    iUseStoryboardCnt: number;
    iUse: number;
    eRegion: proto_lens_script_comm.EmRegion;
  }
  export const RoleInfo: $RoleInfo;

  export interface $EnvInfo {
    strId: string;
    strName: string;
    strUrl: string;
    strDesc: string;
    eGenType: proto_lens_script_comm.EmGenType;
    vecUseScript: Array<string>;
    eStatus: proto_lens_script_comm.emStatus;
    strEnvTips: string;
    iIndex: number;
  }
  export const EnvInfo: $EnvInfo;

  export enum emResolution {
    "EM_VIDEO_720P_H"= 0,
    "EM_VIDEO_720P_V"= 1,
  }

  export interface $VideoSize {
    lWidth: number;
    lHeight: number;
    sizeName: string;
    stResolution: proto_lens_script_comm.emResolution;
  }
  export const VideoSize: $VideoSize;

  export interface $WebVideoInfo {
    strScriptId: string;
    strVideoUrl: string;
    strCoverImg: string;
    strPublicTime: string;
    stUserInfo: $proto_lens.proto_lens.$UserInfo;
    lStar: number;
    strSongName: string;
    strTopic: string;
    stSize: proto_lens_script_comm.$VideoSize;
  }
  export const WebVideoInfo: $WebVideoInfo;

  export interface $UserScriptInfo {
    iUid: number;
    VecVideo: Array<proto_lens_script_comm.$WebVideoInfo>;
    vecRole: Array<proto_lens_script_comm.$RoleInfo>;
    vecEnv: Array<proto_lens_script_comm.$EnvInfo>;
    mapMvTypeUseCnt: {[key: string]: number};
  }
  export const UserScriptInfo: $UserScriptInfo;

  export enum emDataType {
    "EM_DATA_TYPE_INT"= 1,
    "EM_DATA_TYPE_STRING"= 2,
  }

  export interface $OPDataItem {
    iPos: number;
    strValueField: string;
    eType: proto_lens_script_comm.emDataType;
  }
  export const OPDataItem: $OPDataItem;

  export interface $OutPutData {
    vecItem: Array<proto_lens_script_comm.$OPDataItem>;
  }
  export const OutPutData: $OutPutData;

  export interface $ExecFunc {
    strExecFunc: string;
    mapArgs: {[key: string]: string};
    strDesc: string;
  }
  export const ExecFunc: $ExecFunc;

  export enum emSubProcessType {
    "EM_SUB_PROCESS_TYPE_TOPIC_DESC"= 1,
    "EM_SUB_PROCESS_TYPE_SCENE_ROLE_ENV_DESC"= 2,
    "EM_SUB_PROCESS_TYPE_SCENE_ROLE_PIC"= 3,
    "EM_SUB_PROCESS_TYPE_SCENE_ENV_PIC"= 4,
    "EM_SUB_PROCESS_TYPE_STORYBOARD_DESC"= 5,
    "EM_SUB_PROCESS_TYPE_STORYBOARD_IMAGE"= 6,
    "EM_SUB_PROCESS_TYPE_VIDEO"= 7,
    "EM_SUB_PROCESS_TYPE_MERGE"= 8,
    "EM_SUB_PROCESS_TYPE_CHOOSE_MVTYPE"= 9,
  }

  export interface $SubProcessDo {
    strSubProcessName: string;
    eSubType: proto_lens_script_comm.emSubProcessType;
    vecMainExecFunc: Array<proto_lens_script_comm.$ExecFunc>;
    vecSingleExecFunc: Array<proto_lens_script_comm.$ExecFunc>;
    eStatus: proto_lens_script_comm.emStatus;
  }
  export const SubProcessDo: $SubProcessDo;

  export enum emFlow {
    "EM_FLOW_DETERMIN_TOPIC"= 1,
    "EM_FLOW_MV_SCRIPT"= 2,
    "EM_FLOW_STORYBOARD"= 3,
    "EM_FLOW_VIDEO"= 4,
    "EM_FLOW_PREVIEW"= 5,
  }

  export interface $MVQuality {
    strId: string;
    strName: string;
    strDesc: string;
    iCanUse: number;
    strVideoModelId: string;
    strMaterialUseRate: string;
    iMaxUseCnt: number;
    vecWhitelist: Array<string>;
  }
  export const MVQuality: $MVQuality;

  export interface $QualityList {
    vecInfo: Array<proto_lens_script_comm.$MVQuality>;
  }
  export const QualityList: $QualityList;

  export interface $PictureModel {
    strId: string;
    strModel: string;
    strReferPic: string;
    strTips: string;
    vecInfo: Array<proto_lens_script_comm.$MVQuality>;
  }
  export const PictureModel: $PictureModel;

  export enum eVideoModelType {
    "EM_VIDEO_MODEL_TYPE_MOVEMENT"= 1,
    "EM_VIDEO_MODEL_TYPE_AI_GEN"= 2,
  }

  export enum emStyleChooseType {
    "EM_STYLE_CHOOSE_TYPE_DEFAULE"= 0,
    "EM_STYLE_CHOOSE_TYPE_HUMAN"= 1,
  }

  export interface $ChooseItem {
    strKey: string;
    strModelId: string;
  }
  export const ChooseItem: $ChooseItem;

  export interface $StyleModelItem {
    eType: proto_lens_script_comm.emStyleChooseType;
    vecChoose: Array<proto_lens_script_comm.$ChooseItem>;
  }
  export const StyleModelItem: $StyleModelItem;

  export interface $PicStyle {
    strConfId: string;
    strName: string;
    strShowPic: string;
    strGuideTips: string;
    stModel: proto_lens_script_comm.$StyleModelItem;
    strAdaptDesc: string;
    strRoleGuide: string;
    strImgGuide: string;
    strVideoGuide: string;
    strNotAdaptDesc: string;
    strMaterialStyleTag: string;
    strThemeKey: string;
    iUseLips: number;
  }
  export const PicStyle: $PicStyle;

  export interface $PicStyleList {
    vecInfo: Array<proto_lens_script_comm.$PicStyle>;
  }
  export const PicStyleList: $PicStyleList;

  export interface $VideoModel {
    strId: string;
    strModel: string;
    strReferPic: string;
    strTips: string;
    eType: proto_lens_script_comm.eVideoModelType;
    iNeedTips: number;
  }
  export const VideoModel: $VideoModel;

  export interface $ProcessDo {
    eFlow: proto_lens_script_comm.emFlow;
    vecSubProc: Array<proto_lens_script_comm.$SubProcessDo>;
  }
  export const ProcessDo: $ProcessDo;

  export interface $strProcessConf {
    vecConf: Array<proto_lens_script_comm.$ProcessDo>;
    strConfId: string;
    strConfName: string;
    strShowPicUrl: string;
    showUsers: Array<string>;
    stQualityInfo: proto_lens_script_comm.$MVQuality;
    strDefaultPicModel: string;
    strDefaultVideoModel: string;
    iOnceMaxUseCnt: number;
    strDefaultPicStyle: string;
  }
  export const strProcessConf: $strProcessConf;

  export interface $strProcessConfList {
    vecProcessList: Array<proto_lens_script_comm.$strProcessConf>;
  }
  export const strProcessConfList: $strProcessConfList;

  export interface $MVTemplate {
    strId: string;
    strName: string;
    vecPicModel: Array<proto_lens_script_comm.$PictureModel>;
  }
  export const MVTemplate: $MVTemplate;

  export interface $Lyric {
    iStartMs: number;
    iEndMs: number;
    strContent: string;
    iDurationMs: number;
  }
  export const Lyric: $Lyric;

  export interface $SongInfo {
    strMid: string;
    strSongId: string;
    strSongName: string;
    strSinger: string;
    strOriFile: string;
    veclyric: Array<proto_lens_script_comm.$Lyric>;
    strSummary: string;
    iAudioLength: number;
    iStartMs: number;
    iEndMs: number;
    uKid: number;
  }
  export const SongInfo: $SongInfo;

  export interface $Movement {
    strId: string;
    strDesc: string;
    strReferPic: string;
    strTips: string;
    strReferVideo: string;
  }
  export const Movement: $Movement;

  export interface $ReferInfo {
    vecPicUrl: Array<string>;
    vecTag: Array<string>;
    vecUseChoose: Array<string>;
  }
  export const ReferInfo: $ReferInfo;

  export interface $TopicInfo {
    strId: string;
    stPicModel: proto_lens_script_comm.$PictureModel;
    stSize: proto_lens_script_comm.$VideoSize;
    stMVTemplate: proto_lens_script_comm.$MVTemplate;
    stSongInfo: proto_lens_script_comm.$SongInfo;
    strInspirationSummary: string;
    stVideoModel: proto_lens_script_comm.$VideoModel;
    stMovement: proto_lens_script_comm.$Movement;
    eStatus: proto_lens_script_comm.emStatus;
    stReferInfo: proto_lens_script_comm.$ReferInfo;
    stStyle: proto_lens_script_comm.$PicStyle;
    vecStyleId: Array<string>;
  }
  export const TopicInfo: $TopicInfo;

  export interface $PicInfo {
    strId: string;
    strPicUrl: string;
    strPicTips: string;
    eGenType: proto_lens_script_comm.EmGenType;
    stUseModelId: string;
    eStatus: proto_lens_script_comm.emStatus;
    strStoryboardDesc: string;
    mapExt: {[key: string]: string};
  }
  export const PicInfo: $PicInfo;

  export interface $VideoInfo {
    strId: string;
    strPicUrl: string;
    strPicTips: string;
    strVideo: string;
    strVideoTips: string;
    strVideoModelId: string;
    strMovementId: string;
    eStatus: proto_lens_script_comm.emStatus;
    strStoryboardDesc: string;
    iStartTs: number;
    iEndTs: number;
    iTimeRange: number;
    mapExt: {[key: string]: string};
  }
  export const VideoInfo: $VideoInfo;

  export interface $StoryboardInfo {
    strId: string;
    strSceneId: string;
    strStory: string;
    eStatus: proto_lens_script_comm.emStatus;
    stCurPic: proto_lens_script_comm.$PicInfo;
    vecPic: Array<proto_lens_script_comm.$PicInfo>;
    stCurVideo: proto_lens_script_comm.$VideoInfo;
    vecVideo: Array<proto_lens_script_comm.$VideoInfo>;
    vecRoleId: Array<string>;
    strEnvId: string;
    iTimeRange: number;
    strShotType: string;
    iIndex: number;
  }
  export const StoryboardInfo: $StoryboardInfo;

  export interface $SceneceDescItem {
    strKey: string;
    strValue: string;
  }
  export const SceneceDescItem: $SceneceDescItem;

  export interface $SceneInfo {
    strId: string;
    strLyric: string;
    eStatus: proto_lens_script_comm.emStatus;
    strSceneDesc: string;
    Desc: Array<proto_lens_script_comm.$SceneceDescItem>;
    vecRoleId: Array<string>;
    vecEnvId: Array<string>;
    VecStoryboard: Array<proto_lens_script_comm.$StoryboardInfo>;
    iStoryboardCnt: number;
    iIndex: number;
    strTimeRange: string;
    iStartTime: number;
    iEndTime: number;
    strSceneTitle: string;
  }
  export const SceneInfo: $SceneInfo;

  export interface $SubtitleParam {
    font: string;
    color: string;
    fontsize: number;
    strokeColor: string;
    strokeWidth: number;
    widthSize: string;
    kerning: number;
    align: string;
    interline: number;
    position: string;
    crossfadeDuration: number;
  }
  export const SubtitleParam: $SubtitleParam;

  export interface $ComVideoParam {
    stSongName: proto_lens_script_comm.$SubtitleParam;
    stSinger: proto_lens_script_comm.$SubtitleParam;
    stLyric: proto_lens_script_comm.$SubtitleParam;
  }
  export const ComVideoParam: $ComVideoParam;

  export interface $ComposeModel {
    strModelId: string;
    strName: string;
    strRemark: string;
    strShowUrl: string;
    stParam: {[key: proto_lens_script_comm.emResolution]: proto_lens_script_comm.$ComVideoParam};
  }
  export const ComposeModel: $ComposeModel;

  export interface $SubtitleItem {
    strId: string;
    iStartTimeMs: number;
    iStopTimeMs: number;
    strContent: string;
    stParam: proto_lens_script_comm.$SubtitleParam;
  }
  export const SubtitleItem: $SubtitleItem;

  export enum emSubTitleType {
    "EM_SUBTITLE_TYPE_SONGNAME"= 1,
    "EM_SUBTITLE_TYPE_SINGER"= 2,
    "EM_SUBTITLE_TYPE_LYRIC"= 3,
  }

  export interface $SubtitleTrack {
    strId: string;
    vecContent: Array<proto_lens_script_comm.$SubtitleItem>;
    eType: proto_lens_script_comm.emSubTitleType;
  }
  export const SubtitleTrack: $SubtitleTrack;

  export interface $SubtitleInfo {
    vecContent: Array<proto_lens_script_comm.$SubtitleTrack>;
  }
  export const SubtitleInfo: $SubtitleInfo;

  export interface $AudioItem {
    strId: string;
    iStartTimeMs: number;
    iStopTimeMs: number;
    iAudioStartTimeMs: number;
    iAudioStopTimeMs: number;
    strAudioUrl: string;
    iAudioDuration: number;
    fVolume: number;
  }
  export const AudioItem: $AudioItem;

  export interface $AudioTrack {
    strId: string;
    vecContent: Array<proto_lens_script_comm.$AudioItem>;
  }
  export const AudioTrack: $AudioTrack;

  export interface $Audio {
    vecAudio: Array<proto_lens_script_comm.$AudioTrack>;
  }
  export const Audio: $Audio;

  export enum emFromSource {
    "EM_FROM_SOURCE_USER"= 0,
    "EM_FROM_SOURCE_BATH_PRODUCTION"= 1,
  }

  export interface $ScriptInfo {
    strId: string;
    strName: string;
    stProcConf: proto_lens_script_comm.$strProcessConf;
    eUserExecFlow: proto_lens_script_comm.emFlow;
    stTopic: proto_lens_script_comm.$TopicInfo;
    eFinishFlow: proto_lens_script_comm.emFlow;
    vecScene: Array<proto_lens_script_comm.$SceneInfo>;
    stSubtitle: proto_lens_script_comm.$SubtitleInfo;
    stAudio: proto_lens_script_comm.$Audio;
    lModifyTime: number;
    strVideoUrl: string;
    strCoverImg: string;
    iIsAuto: number;
    eStatus: proto_lens_script_comm.emStatus;
    iUid: number;
    eSource: proto_lens_script_comm.emFromSource;
    mapExt: {[key: string]: string};
    vecRoleInfo: Array<proto_lens_script_comm.$RoleInfo>;
    vecEnvInfo: Array<proto_lens_script_comm.$EnvInfo>;
  }
  export const ScriptInfo: $ScriptInfo;

  export interface $ActItem {
    strSubProcessName: string;
    strTaskId: string;
    strContent: string;
  }
  export const ActItem: $ActItem;

  export interface $ActuatorInfo {
    vecAct: Array<proto_lens_script_comm.$ActItem>;
  }
  export const ActuatorInfo: $ActuatorInfo;

  export enum emRecycleType {
    "EM_RECYCLE_TYPE_SCENE"= 1,
    "EM_RECYCLE_TYPE_STORYBOARD"= 2,
  }

  export interface $RecycleScene {
    stScene: proto_lens_script_comm.$SceneInfo;
    strPrevSceneId: string;
  }
  export const RecycleScene: $RecycleScene;

  export interface $RecycleStoryboard {
    stStoryBoard: proto_lens_script_comm.$StoryboardInfo;
    strSceneId: string;
    strPrevStoryboardId: string;
  }
  export const RecycleStoryboard: $RecycleStoryboard;

  export interface $RecycleItem {
    eType: proto_lens_script_comm.emRecycleType;
    stScene: proto_lens_script_comm.$RecycleScene;
    stStoryBoard: proto_lens_script_comm.$RecycleStoryboard;
    strId: string;
  }
  export const RecycleItem: $RecycleItem;

  export interface $RecycleInfo {
    vecInfo: Array<proto_lens_script_comm.$RecycleItem>;
  }
  export const RecycleInfo: $RecycleInfo;

  export interface $PromptItem {
    strId: string;
    strPromptName: string;
    strPromptText: string;
    strLLMModel: string;
  }
  export const PromptItem: $PromptItem;

  export interface $PromptConfList {
    vecPrompt: Array<proto_lens_script_comm.$PromptItem>;
  }
  export const PromptConfList: $PromptConfList;

  export interface $ModelCostMoney {
    strExecFunc: string;
    strModelId: string;
    iCostMoney: number;
  }
  export const ModelCostMoney: $ModelCostMoney;

  export interface $SubProcCost {
    eSubType: proto_lens_script_comm.emSubProcessType;
    iCostTime: number;
    stCostMoney: Array<proto_lens_script_comm.$ModelCostMoney>;
  }
  export const SubProcCost: $SubProcCost;

  export interface $GenCost {
    eType: proto_lens_script_comm.emFlow;
    vecSubProc: Array<proto_lens_script_comm.$SubProcCost>;
  }
  export const GenCost: $GenCost;

  export interface $ScriptGenCost {
    vecCost: Array<proto_lens_script_comm.$GenCost>;
  }
  export const ScriptGenCost: $ScriptGenCost;

  export interface $MvTypeItem {
    strID: string;
    strName: string;
    strKey: string;
    strAdapt: string;
    strNotAdapt: string;
    vecCanUsePicModel: Array<string>;
    strBasicPrcId: string;
    vecQuality: Array<proto_lens_script_comm.$MVQuality>;
  }
  export const MvTypeItem: $MvTypeItem;

  export interface $MvType {
    vecInfo: Array<proto_lens_script_comm.$MvTypeItem>;
  }
  export const MvType: $MvType;

  export enum emIDType {
    "EM_ID_TYPE_KID"= 1,
    "EM_ID_TYPE_SONGID"= 2,
    "EM_ID_TYPE_SONG_DETAIL"= 3,
    "EM_ID_TYPE_SONG_DETAIL_FOR_POKE"= 4,
  }

  export interface $QukuMsg {
    strKid: string;
    strMvUrl: string;
    eType: proto_lens_script_comm.emIDType;
    strTargetId: string;
    mapExt: {[key: string]: string};
  }
  export const QukuMsg: $QukuMsg;

  export enum BatchStatus {
    "EM_BATCH_STATUS_INIT"= 0,
    "EM_BATCH_STATUS_CAN_GEN"= 1,
    "EM_BATCH_STATUS_RUNNING"= 2,
    "EM_BATCH_STATUS_VIDEO_AUDIT"= 3,
    "EM_BATCH_STATUS_ALL_DONE"= 4,
    "EM_BATCH_STATUS_GEN_FAIL"= 5,
    "EM_BATCH_STATUS_PUBLISH_QUKU"= 6,
    "EM_BATCH_STATUS_MV_AUDIT"= 7,
    "EM_BATCH_STATUS_RE_GEN"= 8,
    "EM_BATCH_STATUS_REFUSE"= 9,
  }

}

export const strKeyUserInfoPrefix = "LENS_USER_INFO";

export const strKeyScriptPrefix = "SCRIPT_INFO";

export const strKeyRolePrefix = "ROLE_INFO";

export const strKeyProcessList = "LENS_MV_PROCESS_LIST";

export const strKeyProcessListV2 = "LENS_MV_PROCESS_LIST_V2";

export const strKeyProcessSort = "LENS_MV_PROCESS_LIST_SORT";

export const strPicStyleList = "LENS_MV_PIC_STYLE_LIST";

export const strMvTypeList = "LENS_MV_TYPE_LIST_KEY";

export const SmartPicMode = "SmartPicMode";

export const SmartVideoMode = "SmartVideoMode";

export const strKeyPictureStyleList = "PICTURE_STYLE_LIST";

export const strKeyQualityList = "QUALITY_LIST_KEY";

export enum EmGender {
  "EM_GENDER_FEMALE"= 1,
  "EM_GENDER_MALE"= 2,
}

export enum EmRoleType {
  "EM_ROLE_TYPE_HUMAN"= 1,
  "EM_ROLE_TYPE_NON_HUMAN"= 2,
}

export enum EmGenType {
  "EM_GEN_TYPE_BY_AI"= 1,
  "EM_GEN_TYPE_BY_SELF"= 2,
  "EM_GEN_TYPE_BY_AI_REGEN"= 3,
}

export enum emStatus {
  "EM_STATUS_INIT"= 0,
  "EM_STATUS_RUNNING"= 1,
  "EM_STATUS_SUCC"= 2,
  "EM_STATUS_FAIL"= 3,
  "EM_STATUS_NEED_AUDIT"= 4,
}

export enum EmRegion {
  "EM_REGION_EAST_ASIA"= 0,
  "EM_REGION_EUROPE_AMERICA"= 1,
}

export interface $Region {
  eRegion: proto_lens_script_comm.EmRegion;
  strRegionName: string;
}
export const Region: $Region;

export interface $RoleInfo {
  strId: string;
  strName: string;
  strUrl: string;
  strGen: string;
  strAge: string;
  eRoleType: proto_lens_script_comm.EmRoleType;
  strHair: string;
  strCloth: string;
  strDesc: string;
  strFaceReferPic: string;
  strDressRefrePic: string;
  eGenType: proto_lens_script_comm.EmGenType;
  vecUseScript: Array<string>;
  eStatus: proto_lens_script_comm.emStatus;
  strRoleTips: string;
  iIndex: number;
  iUseStoryboardCnt: number;
  iUse: number;
  eRegion: proto_lens_script_comm.EmRegion;
}
export const RoleInfo: $RoleInfo;

export interface $EnvInfo {
  strId: string;
  strName: string;
  strUrl: string;
  strDesc: string;
  eGenType: proto_lens_script_comm.EmGenType;
  vecUseScript: Array<string>;
  eStatus: proto_lens_script_comm.emStatus;
  strEnvTips: string;
  iIndex: number;
}
export const EnvInfo: $EnvInfo;

export enum emResolution {
  "EM_VIDEO_720P_H"= 0,
  "EM_VIDEO_720P_V"= 1,
}

export interface $VideoSize {
  lWidth: number;
  lHeight: number;
  sizeName: string;
  stResolution: proto_lens_script_comm.emResolution;
}
export const VideoSize: $VideoSize;

export interface $WebVideoInfo {
  strScriptId: string;
  strVideoUrl: string;
  strCoverImg: string;
  strPublicTime: string;
  stUserInfo: $proto_lens.proto_lens.$UserInfo;
  lStar: number;
  strSongName: string;
  strTopic: string;
  stSize: proto_lens_script_comm.$VideoSize;
}
export const WebVideoInfo: $WebVideoInfo;

export interface $UserScriptInfo {
  iUid: number;
  VecVideo: Array<proto_lens_script_comm.$WebVideoInfo>;
  vecRole: Array<proto_lens_script_comm.$RoleInfo>;
  vecEnv: Array<proto_lens_script_comm.$EnvInfo>;
  mapMvTypeUseCnt: {[key: string]: number};
}
export const UserScriptInfo: $UserScriptInfo;

export enum emDataType {
  "EM_DATA_TYPE_INT"= 1,
  "EM_DATA_TYPE_STRING"= 2,
}

export interface $OPDataItem {
  iPos: number;
  strValueField: string;
  eType: proto_lens_script_comm.emDataType;
}
export const OPDataItem: $OPDataItem;

export interface $OutPutData {
  vecItem: Array<proto_lens_script_comm.$OPDataItem>;
}
export const OutPutData: $OutPutData;

export interface $ExecFunc {
  strExecFunc: string;
  mapArgs: {[key: string]: string};
  strDesc: string;
}
export const ExecFunc: $ExecFunc;

export enum emSubProcessType {
  "EM_SUB_PROCESS_TYPE_TOPIC_DESC"= 1,
  "EM_SUB_PROCESS_TYPE_SCENE_ROLE_ENV_DESC"= 2,
  "EM_SUB_PROCESS_TYPE_SCENE_ROLE_PIC"= 3,
  "EM_SUB_PROCESS_TYPE_SCENE_ENV_PIC"= 4,
  "EM_SUB_PROCESS_TYPE_STORYBOARD_DESC"= 5,
  "EM_SUB_PROCESS_TYPE_STORYBOARD_IMAGE"= 6,
  "EM_SUB_PROCESS_TYPE_VIDEO"= 7,
  "EM_SUB_PROCESS_TYPE_MERGE"= 8,
  "EM_SUB_PROCESS_TYPE_CHOOSE_MVTYPE"= 9,
}

export interface $SubProcessDo {
  strSubProcessName: string;
  eSubType: proto_lens_script_comm.emSubProcessType;
  vecMainExecFunc: Array<proto_lens_script_comm.$ExecFunc>;
  vecSingleExecFunc: Array<proto_lens_script_comm.$ExecFunc>;
  eStatus: proto_lens_script_comm.emStatus;
}
export const SubProcessDo: $SubProcessDo;

export enum emFlow {
  "EM_FLOW_DETERMIN_TOPIC"= 1,
  "EM_FLOW_MV_SCRIPT"= 2,
  "EM_FLOW_STORYBOARD"= 3,
  "EM_FLOW_VIDEO"= 4,
  "EM_FLOW_PREVIEW"= 5,
}

export interface $MVQuality {
  strId: string;
  strName: string;
  strDesc: string;
  iCanUse: number;
  strVideoModelId: string;
  strMaterialUseRate: string;
  iMaxUseCnt: number;
  vecWhitelist: Array<string>;
}
export const MVQuality: $MVQuality;

export interface $QualityList {
  vecInfo: Array<proto_lens_script_comm.$MVQuality>;
}
export const QualityList: $QualityList;

export interface $PictureModel {
  strId: string;
  strModel: string;
  strReferPic: string;
  strTips: string;
  vecInfo: Array<proto_lens_script_comm.$MVQuality>;
}
export const PictureModel: $PictureModel;

export enum eVideoModelType {
  "EM_VIDEO_MODEL_TYPE_MOVEMENT"= 1,
  "EM_VIDEO_MODEL_TYPE_AI_GEN"= 2,
}

export enum emStyleChooseType {
  "EM_STYLE_CHOOSE_TYPE_DEFAULE"= 0,
  "EM_STYLE_CHOOSE_TYPE_HUMAN"= 1,
}

export interface $ChooseItem {
  strKey: string;
  strModelId: string;
}
export const ChooseItem: $ChooseItem;

export interface $StyleModelItem {
  eType: proto_lens_script_comm.emStyleChooseType;
  vecChoose: Array<proto_lens_script_comm.$ChooseItem>;
}
export const StyleModelItem: $StyleModelItem;

export interface $PicStyle {
  strConfId: string;
  strName: string;
  strShowPic: string;
  strGuideTips: string;
  stModel: proto_lens_script_comm.$StyleModelItem;
  strAdaptDesc: string;
  strRoleGuide: string;
  strImgGuide: string;
  strVideoGuide: string;
  strNotAdaptDesc: string;
  strMaterialStyleTag: string;
  strThemeKey: string;
  iUseLips: number;
}
export const PicStyle: $PicStyle;

export interface $PicStyleList {
  vecInfo: Array<proto_lens_script_comm.$PicStyle>;
}
export const PicStyleList: $PicStyleList;

export interface $VideoModel {
  strId: string;
  strModel: string;
  strReferPic: string;
  strTips: string;
  eType: proto_lens_script_comm.eVideoModelType;
  iNeedTips: number;
}
export const VideoModel: $VideoModel;

export interface $ProcessDo {
  eFlow: proto_lens_script_comm.emFlow;
  vecSubProc: Array<proto_lens_script_comm.$SubProcessDo>;
}
export const ProcessDo: $ProcessDo;

export interface $strProcessConf {
  vecConf: Array<proto_lens_script_comm.$ProcessDo>;
  strConfId: string;
  strConfName: string;
  strShowPicUrl: string;
  showUsers: Array<string>;
  stQualityInfo: proto_lens_script_comm.$MVQuality;
  strDefaultPicModel: string;
  strDefaultVideoModel: string;
  iOnceMaxUseCnt: number;
  strDefaultPicStyle: string;
}
export const strProcessConf: $strProcessConf;

export interface $strProcessConfList {
  vecProcessList: Array<proto_lens_script_comm.$strProcessConf>;
}
export const strProcessConfList: $strProcessConfList;

export interface $MVTemplate {
  strId: string;
  strName: string;
  vecPicModel: Array<proto_lens_script_comm.$PictureModel>;
}
export const MVTemplate: $MVTemplate;

export interface $Lyric {
  iStartMs: number;
  iEndMs: number;
  strContent: string;
  iDurationMs: number;
}
export const Lyric: $Lyric;

export interface $SongInfo {
  strMid: string;
  strSongId: string;
  strSongName: string;
  strSinger: string;
  strOriFile: string;
  veclyric: Array<proto_lens_script_comm.$Lyric>;
  strSummary: string;
  iAudioLength: number;
  iStartMs: number;
  iEndMs: number;
  uKid: number;
}
export const SongInfo: $SongInfo;

export interface $Movement {
  strId: string;
  strDesc: string;
  strReferPic: string;
  strTips: string;
  strReferVideo: string;
}
export const Movement: $Movement;

export interface $ReferInfo {
  vecPicUrl: Array<string>;
  vecTag: Array<string>;
  vecUseChoose: Array<string>;
}
export const ReferInfo: $ReferInfo;

export interface $TopicInfo {
  strId: string;
  stPicModel: proto_lens_script_comm.$PictureModel;
  stSize: proto_lens_script_comm.$VideoSize;
  stMVTemplate: proto_lens_script_comm.$MVTemplate;
  stSongInfo: proto_lens_script_comm.$SongInfo;
  strInspirationSummary: string;
  stVideoModel: proto_lens_script_comm.$VideoModel;
  stMovement: proto_lens_script_comm.$Movement;
  eStatus: proto_lens_script_comm.emStatus;
  stReferInfo: proto_lens_script_comm.$ReferInfo;
  stStyle: proto_lens_script_comm.$PicStyle;
  vecStyleId: Array<string>;
}
export const TopicInfo: $TopicInfo;

export interface $PicInfo {
  strId: string;
  strPicUrl: string;
  strPicTips: string;
  eGenType: proto_lens_script_comm.EmGenType;
  stUseModelId: string;
  eStatus: proto_lens_script_comm.emStatus;
  strStoryboardDesc: string;
  mapExt: {[key: string]: string};
}
export const PicInfo: $PicInfo;

export interface $VideoInfo {
  strId: string;
  strPicUrl: string;
  strPicTips: string;
  strVideo: string;
  strVideoTips: string;
  strVideoModelId: string;
  strMovementId: string;
  eStatus: proto_lens_script_comm.emStatus;
  strStoryboardDesc: string;
  iStartTs: number;
  iEndTs: number;
  iTimeRange: number;
  mapExt: {[key: string]: string};
}
export const VideoInfo: $VideoInfo;

export interface $StoryboardInfo {
  strId: string;
  strSceneId: string;
  strStory: string;
  eStatus: proto_lens_script_comm.emStatus;
  stCurPic: proto_lens_script_comm.$PicInfo;
  vecPic: Array<proto_lens_script_comm.$PicInfo>;
  stCurVideo: proto_lens_script_comm.$VideoInfo;
  vecVideo: Array<proto_lens_script_comm.$VideoInfo>;
  vecRoleId: Array<string>;
  strEnvId: string;
  iTimeRange: number;
  strShotType: string;
  iIndex: number;
}
export const StoryboardInfo: $StoryboardInfo;

export interface $SceneceDescItem {
  strKey: string;
  strValue: string;
}
export const SceneceDescItem: $SceneceDescItem;

export interface $SceneInfo {
  strId: string;
  strLyric: string;
  eStatus: proto_lens_script_comm.emStatus;
  strSceneDesc: string;
  Desc: Array<proto_lens_script_comm.$SceneceDescItem>;
  vecRoleId: Array<string>;
  vecEnvId: Array<string>;
  VecStoryboard: Array<proto_lens_script_comm.$StoryboardInfo>;
  iStoryboardCnt: number;
  iIndex: number;
  strTimeRange: string;
  iStartTime: number;
  iEndTime: number;
  strSceneTitle: string;
}
export const SceneInfo: $SceneInfo;

export interface $SubtitleParam {
  font: string;
  color: string;
  fontsize: number;
  strokeColor: string;
  strokeWidth: number;
  widthSize: string;
  kerning: number;
  align: string;
  interline: number;
  position: string;
  crossfadeDuration: number;
}
export const SubtitleParam: $SubtitleParam;

export interface $ComVideoParam {
  stSongName: proto_lens_script_comm.$SubtitleParam;
  stSinger: proto_lens_script_comm.$SubtitleParam;
  stLyric: proto_lens_script_comm.$SubtitleParam;
}
export const ComVideoParam: $ComVideoParam;

export interface $ComposeModel {
  strModelId: string;
  strName: string;
  strRemark: string;
  strShowUrl: string;
  stParam: {[key: proto_lens_script_comm.emResolution]: proto_lens_script_comm.$ComVideoParam};
}
export const ComposeModel: $ComposeModel;

export interface $SubtitleItem {
  strId: string;
  iStartTimeMs: number;
  iStopTimeMs: number;
  strContent: string;
  stParam: proto_lens_script_comm.$SubtitleParam;
}
export const SubtitleItem: $SubtitleItem;

export enum emSubTitleType {
  "EM_SUBTITLE_TYPE_SONGNAME"= 1,
  "EM_SUBTITLE_TYPE_SINGER"= 2,
  "EM_SUBTITLE_TYPE_LYRIC"= 3,
}

export interface $SubtitleTrack {
  strId: string;
  vecContent: Array<proto_lens_script_comm.$SubtitleItem>;
  eType: proto_lens_script_comm.emSubTitleType;
}
export const SubtitleTrack: $SubtitleTrack;

export interface $SubtitleInfo {
  vecContent: Array<proto_lens_script_comm.$SubtitleTrack>;
}
export const SubtitleInfo: $SubtitleInfo;

export interface $AudioItem {
  strId: string;
  iStartTimeMs: number;
  iStopTimeMs: number;
  iAudioStartTimeMs: number;
  iAudioStopTimeMs: number;
  strAudioUrl: string;
  iAudioDuration: number;
  fVolume: number;
}
export const AudioItem: $AudioItem;

export interface $AudioTrack {
  strId: string;
  vecContent: Array<proto_lens_script_comm.$AudioItem>;
}
export const AudioTrack: $AudioTrack;

export interface $Audio {
  vecAudio: Array<proto_lens_script_comm.$AudioTrack>;
}
export const Audio: $Audio;

export enum emFromSource {
  "EM_FROM_SOURCE_USER"= 0,
  "EM_FROM_SOURCE_BATH_PRODUCTION"= 1,
}

export interface $ScriptInfo {
  strId: string;
  strName: string;
  stProcConf: proto_lens_script_comm.$strProcessConf;
  eUserExecFlow: proto_lens_script_comm.emFlow;
  stTopic: proto_lens_script_comm.$TopicInfo;
  eFinishFlow: proto_lens_script_comm.emFlow;
  vecScene: Array<proto_lens_script_comm.$SceneInfo>;
  stSubtitle: proto_lens_script_comm.$SubtitleInfo;
  stAudio: proto_lens_script_comm.$Audio;
  lModifyTime: number;
  strVideoUrl: string;
  strCoverImg: string;
  iIsAuto: number;
  eStatus: proto_lens_script_comm.emStatus;
  iUid: number;
  eSource: proto_lens_script_comm.emFromSource;
  mapExt: {[key: string]: string};
  vecRoleInfo: Array<proto_lens_script_comm.$RoleInfo>;
  vecEnvInfo: Array<proto_lens_script_comm.$EnvInfo>;
}
export const ScriptInfo: $ScriptInfo;

export interface $ActItem {
  strSubProcessName: string;
  strTaskId: string;
  strContent: string;
}
export const ActItem: $ActItem;

export interface $ActuatorInfo {
  vecAct: Array<proto_lens_script_comm.$ActItem>;
}
export const ActuatorInfo: $ActuatorInfo;

export enum emRecycleType {
  "EM_RECYCLE_TYPE_SCENE"= 1,
  "EM_RECYCLE_TYPE_STORYBOARD"= 2,
}

export interface $RecycleScene {
  stScene: proto_lens_script_comm.$SceneInfo;
  strPrevSceneId: string;
}
export const RecycleScene: $RecycleScene;

export interface $RecycleStoryboard {
  stStoryBoard: proto_lens_script_comm.$StoryboardInfo;
  strSceneId: string;
  strPrevStoryboardId: string;
}
export const RecycleStoryboard: $RecycleStoryboard;

export interface $RecycleItem {
  eType: proto_lens_script_comm.emRecycleType;
  stScene: proto_lens_script_comm.$RecycleScene;
  stStoryBoard: proto_lens_script_comm.$RecycleStoryboard;
  strId: string;
}
export const RecycleItem: $RecycleItem;

export interface $RecycleInfo {
  vecInfo: Array<proto_lens_script_comm.$RecycleItem>;
}
export const RecycleInfo: $RecycleInfo;

export interface $PromptItem {
  strId: string;
  strPromptName: string;
  strPromptText: string;
  strLLMModel: string;
}
export const PromptItem: $PromptItem;

export interface $PromptConfList {
  vecPrompt: Array<proto_lens_script_comm.$PromptItem>;
}
export const PromptConfList: $PromptConfList;

export interface $ModelCostMoney {
  strExecFunc: string;
  strModelId: string;
  iCostMoney: number;
}
export const ModelCostMoney: $ModelCostMoney;

export interface $SubProcCost {
  eSubType: proto_lens_script_comm.emSubProcessType;
  iCostTime: number;
  stCostMoney: Array<proto_lens_script_comm.$ModelCostMoney>;
}
export const SubProcCost: $SubProcCost;

export interface $GenCost {
  eType: proto_lens_script_comm.emFlow;
  vecSubProc: Array<proto_lens_script_comm.$SubProcCost>;
}
export const GenCost: $GenCost;

export interface $ScriptGenCost {
  vecCost: Array<proto_lens_script_comm.$GenCost>;
}
export const ScriptGenCost: $ScriptGenCost;

export interface $MvTypeItem {
  strID: string;
  strName: string;
  strKey: string;
  strAdapt: string;
  strNotAdapt: string;
  vecCanUsePicModel: Array<string>;
  strBasicPrcId: string;
  vecQuality: Array<proto_lens_script_comm.$MVQuality>;
}
export const MvTypeItem: $MvTypeItem;

export interface $MvType {
  vecInfo: Array<proto_lens_script_comm.$MvTypeItem>;
}
export const MvType: $MvType;

export enum emIDType {
  "EM_ID_TYPE_KID"= 1,
  "EM_ID_TYPE_SONGID"= 2,
  "EM_ID_TYPE_SONG_DETAIL"= 3,
  "EM_ID_TYPE_SONG_DETAIL_FOR_POKE"= 4,
}

export interface $QukuMsg {
  strKid: string;
  strMvUrl: string;
  eType: proto_lens_script_comm.emIDType;
  strTargetId: string;
  mapExt: {[key: string]: string};
}
export const QukuMsg: $QukuMsg;

export enum BatchStatus {
  "EM_BATCH_STATUS_INIT"= 0,
  "EM_BATCH_STATUS_CAN_GEN"= 1,
  "EM_BATCH_STATUS_RUNNING"= 2,
  "EM_BATCH_STATUS_VIDEO_AUDIT"= 3,
  "EM_BATCH_STATUS_ALL_DONE"= 4,
  "EM_BATCH_STATUS_GEN_FAIL"= 5,
  "EM_BATCH_STATUS_PUBLISH_QUKU"= 6,
  "EM_BATCH_STATUS_MV_AUDIT"= 7,
  "EM_BATCH_STATUS_RE_GEN"= 8,
  "EM_BATCH_STATUS_REFUSE"= 9,
}