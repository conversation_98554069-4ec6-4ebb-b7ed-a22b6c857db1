/* eslint-disable */
/* tslint:disable */

import * as $proto_lens_script_comm from './proto_lens_script_comm';
import * as $proto_lens from '../proto_lens/proto_lens';

export declare namespace proto_lens_script_svr {
  export const LENS_SCRIPT_SVR_MOD_ID = 0;

  export const CMD_GET_FRONT_PAGE = "interact.lens_script.svr.get_front_page";

  export const CMD_GET_PERSON_INFO = "interact.lens_script.svr.get_person_info";

  export const CMD_GET_SCRIPT_INFO = "interact.lens_script.svr.get_script_info";

  export const CMD_SEARCH_SONG_LIST = "interact.lens_script.svr.search_song_list";

  export const CMD_SONG_INFO = "interact.lens_script.svr.song_info";

  export const CMD_BASIC_INFO = "interact.lens_script.svr.basic_info";

  export const CMD_DETERMINE_TOPIC = "interact.lens_script.svr.determine_topic";

  export const CMD_AUTO_GEN_ALL = "interact.lens_script.svr.auto_gen_all";

  export const CMD_DO_MAIN_PROCESS = "interact.lens_script.svr.do_main_process";

  export const CMD_EDIT_SCENE = "interact.lens_script.svr.edit_scene";

  export const CMD_EDIT_ROLE = "interact.lens_script.svr.edit_role";

  export const CMD_EDIT_ENV = "interact.lens_script.svr.edit_env";

  export const CMD_EDIT_STORYBOARD = "interact.lens_script.svr.edit_storyboard";

  export const CMD_EDIT_TEXT = "interact.lens_script.svr.edit_text";

  export const CMD_EDIT_AUDIO = "interact.lens_script.svr.edit_audio";

  export const CMD_MODITY_PERSON_INFO = "interact.lens_script.svr.modify_person_info";

  export const CMD_MODITY_VIDEO_COVER = "interact.lens_script.svr.modify_video_cover";

  export const CMD_GET_RECYCLE_INFO = "interact.lens_script.svr.get_recycle_info";

  export const CMD_REDUCTION_ITEM = "interact.lens_script.svr.reduction_item";

  export const CMD_DELETE_SCRIPT = "interact.lens_script.svr.delete_script";

  export const CMD_SW_CALLBACK = "interact.lens_script.svr.sw_call_back";

  export const CMD_BATCH_PRODUCTION = "interact.lens_script.svr.batch_production";

  export const CMD_AUDIT_RESULT = "interact.lens_script.svr.audit_result";

  export const CMD_BACKEND_GEN = "interact.lens_script.svr.backend_gen";

  export const CMD_PUSH_MV_URL = "interact.lens_script.svr.push_mv_url";

  export const CMD_PICTURE_ANALYZE = "interact.lens_script.svr.picture_analyze";

  export const CMD_GET_MATERIAL_LIST = "interact.lens_script.svr.get_material_list";

  export const CMD_GET_ROLE_INFO = "interact.lens_script.svr.get_role_info";

  export const CMD_GET_AUDIO_URL = "interact.lens_script.svr.get_audio_url";

  export interface $GetFrontPageReq {
    iPageIndex: number;
    iPageCnt: number;
  }
  export const GetFrontPageReq: $GetFrontPageReq;

  export interface $GetFrontPageRsp {
    vecList: Array<$proto_lens_script_comm.proto_lens_script_comm.$WebVideoInfo>;
    iTotal: number;
    strTitleDetail: string;
    strDocsUrl: string;
    iCanAccess: number;
    strRefuseTips: string;
  }
  export const GetFrontPageRsp: $GetFrontPageRsp;

  export enum EmGetPersonInfoMask {
    "EM_GET_PERSON_INFO_TYPE_ALL"= 8191,
    "EM_GET_PERSON_INFO_TYPE_ROLE"= 1,
    "EM_GET_PERSON_INFO_TYPE_VIDEO_LIST"= 2,
    "EM_GET_PERSON_INFO_TYPE_ENV"= 4,
  }

  export interface $GetPersonInfoReq {
    IGetMask: proto_lens_script_svr.EmGetPersonInfoMask;
  }
  export const GetPersonInfoReq: $GetPersonInfoReq;

  export interface $GetPersonInfoRsp {
    vecRole: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
    vecVideo: Array<$proto_lens_script_comm.proto_lens_script_comm.$WebVideoInfo>;
    VecEnv: Array<$proto_lens_script_comm.proto_lens_script_comm.$EnvInfo>;
  }
  export const GetPersonInfoRsp: $GetPersonInfoRsp;

  export enum emScriptGetMask {
    "EM_SCRIPT_GET_TYPE_ALL"= 8191,
    "EM_SCRIPT_GET_TYPE_TOPIC"= 1,
    "EM_SCRIPT_GET_TYPE_SCENE"= 2,
    "EM_SCRIPT_GET_TYPE_STORYBOARD"= 4,
    "EM_SCRIPT_GET_TYPE_MERGE"= 8,
    "EM_SCRIPT_GET_TYPE_ROLE"= 16,
    "EM_SCRIPT_GET_TYPE_ENV"= 32,
  }

  export interface $GetScriptInfoReq {
    strScriptId: string;
    eGetType: proto_lens_script_svr.emScriptGetMask;
  }
  export const GetScriptInfoReq: $GetScriptInfoReq;

  export interface $WebSceneInfo {
    strId: string;
    strLyric: string;
    eStatus: $proto_lens_script_comm.proto_lens_script_comm.emStatus;
    Desc: Array<$proto_lens_script_comm.proto_lens_script_comm.$SceneceDescItem>;
    vecRoleInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
    vecEnvInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$EnvInfo>;
    iStoryboardCnt: number;
    iIndex: number;
    iTimeRange: number;
  }
  export const WebSceneInfo: $WebSceneInfo;

  export interface $WebStoryboardInfo {
    strId: string;
    strSceneId: string;
    strStory: string;
    eStatus: $proto_lens_script_comm.proto_lens_script_comm.emStatus;
    stCurPic: $proto_lens_script_comm.proto_lens_script_comm.$PicInfo;
    vecPic: Array<$proto_lens_script_comm.proto_lens_script_comm.$PicInfo>;
    stCurVideo: $proto_lens_script_comm.proto_lens_script_comm.$VideoInfo;
    vecVideo: Array<$proto_lens_script_comm.proto_lens_script_comm.$VideoInfo>;
    vecRoleInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
    stEnvInfo: $proto_lens_script_comm.proto_lens_script_comm.$EnvInfo;
    iStartTime: number;
    iTimeRange: number;
    strShotType: string;
    iIndex: number;
  }
  export const WebStoryboardInfo: $WebStoryboardInfo;

  export interface $WebScriptInfo {
    strName: string;
    eCurFlow: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
    stTopic: $proto_lens_script_comm.proto_lens_script_comm.$TopicInfo;
    vecScene: Array<proto_lens_script_svr.$WebSceneInfo>;
    VecStoryboard: Array<proto_lens_script_svr.$WebStoryboardInfo>;
    stSubtitle: $proto_lens_script_comm.proto_lens_script_comm.$SubtitleInfo;
    stAudio: $proto_lens_script_comm.proto_lens_script_comm.$Audio;
    lPublichTime: number;
    strVideoUrl: string;
    strCoverImg: string;
    iTimeRange: number;
    mapErrInfo: {[key: string]: string};
    vecRoleInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
    vecEnvInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$EnvInfo>;
    strScriptId: string;
    mapFlowStatus: {[key: $proto_lens_script_comm.proto_lens_script_comm.emFlow]: $proto_lens_script_comm.proto_lens_script_comm.emStatus};
    iIsAuto: number;
    vecNewScene: Array<$proto_lens_script_comm.proto_lens_script_comm.$SceneInfo>;
    VecNewStoryboard: Array<$proto_lens_script_comm.proto_lens_script_comm.$StoryboardInfo>;
  }
  export const WebScriptInfo: $WebScriptInfo;

  export interface $GetScriptInfoRsp {
    stData: proto_lens_script_svr.$WebScriptInfo;
    strTips: string;
  }
  export const GetScriptInfoRsp: $GetScriptInfoRsp;

  export interface $SearchSongListReq {
    strSongName: string;
  }
  export const SearchSongListReq: $SearchSongListReq;

  export interface $SongList {
    strSongId: string;
    strSongName: string;
    strSinger: string;
    strMid: string;
  }
  export const SongList: $SongList;

  export interface $SearchSongListRsp {
    vecList: Array<proto_lens_script_svr.$SongList>;
  }
  export const SearchSongListRsp: $SearchSongListRsp;

  export interface $SongInfoReq {
    strMid: string;
    strSongId: string;
    uKid: number;
  }
  export const SongInfoReq: $SongInfoReq;

  export interface $SongInfoRsp {
    stSong: $proto_lens_script_comm.proto_lens_script_comm.$SongInfo;
  }
  export const SongInfoRsp: $SongInfoRsp;

  export enum emBasicMask {
    "EM_BASIC_MASK_ALL"= 131071,
    "EM_BASIC_MASK_STYLE"= 1,
    "EM_BASIC_MASK_PIC_MOD"= 2,
    "EM_BASIC_MASK_VIDEO_MOD"= 4,
    "EM_BASIC_MASK_VIDEO_SIZE"= 8,
    "EM_BASIC_MASK_MOVEMENT"= 16,
    "EM_BASIC_MASK_MVTEMPLATE"= 32,
    "EM_BASIC_MASK_COMPOSE_PARAM"= 64,
    "EM_BASIC_MASK_REGION_LIST"= 128,
  }

  export interface $BasicInfoReq {
    eMask: proto_lens_script_svr.emBasicMask;
    strScriptId: string;
  }
  export const BasicInfoReq: $BasicInfoReq;

  export interface $BasicInfoRsp {
    vecPicMod: Array<$proto_lens_script_comm.proto_lens_script_comm.$PictureModel>;
    vecViMod: Array<$proto_lens_script_comm.proto_lens_script_comm.$VideoModel>;
    vecVideoSize: Array<$proto_lens_script_comm.proto_lens_script_comm.$VideoSize>;
    vecMovement: Array<$proto_lens_script_comm.proto_lens_script_comm.$Movement>;
    vecCompose: Array<$proto_lens_script_comm.proto_lens_script_comm.$ComposeModel>;
    vecRegion: Array<$proto_lens_script_comm.proto_lens_script_comm.$Region>;
  }
  export const BasicInfoRsp: $BasicInfoRsp;

  export enum emEditType {
    "EM_EDIT_TOPIC_TYPE_INSPRATION"= 1,
    "EM_EDIT_TOPIC_TYPE_PICMODEL"= 2,
    "EM_EDIT_TOPIC_TYPE_VIDMODEL"= 3,
    "EM_EDIT_TOPIC_TYPE_MOVEMENT"= 4,
    "EM_EDIT_TOPIC_TYPE_MVSIZE"= 5,
    "EM_EDIT_TOPIC_TYPE_MVTEMPLATE"= 6,
    "EM_EDIT_TOPIC_TYPE_SONGINFO"= 7,
    "EM_EDIT_TOPIC_TYPE_REGEN"= 8,
    "EM_EDIT_TOPIC_MODIFY_TAG"= 9,
    "EM_EDIT_TOPIC_MODIFY_QUALITY"= 10,
    "EM_EDIT_SCEN_TYPE_SCENEDESC"= 20,
    "EM_EDIT_SCEN_TYPE_ROLEID"= 21,
    "EM_EDIT_SCEN_TYPE_ENVID"= 22,
    "EM_EDIT_SCEN_TYPE_STORYBOARDCNT"= 23,
    "EM_EDIT_SCEN_TYPE_DELETE"= 24,
    "EM_EDIT_SCEN_TYPE_ADD"= 25,
    "EM_EDIT_SCEN_TYPE_REGEN_SINGLE"= 26,
    "EM_EDIT_SCEN_TYPE_REGEN_ALL"= 27,
    "EM_EDIT_SCEN_TYPE_CHANGE_ORDER"= 28,
    "EM_EDIT_SCEN_TYPE_REGEN_STORYBOARD"= 29,
    "EM_EDIT_SCEN_TYPE_GEN_TITLE"= 30,
    "EM_EDIT_STORYBOARD_TYPE_STORY"= 40,
    "EM_EDIT_STORYBOARD_TYPE_ROLEID"= 41,
    "EM_EDIT_STORYBOARD_TYPE_ENVID"= 42,
    "EM_EDIT_STORYBOARD_TYPE_CHANGE_ORDER"= 43,
    "EM_EDIT_STORYBOARD_TYPE_DELETE"= 44,
    "EM_EDIT_STORYBOARD_TYPE_ADD"= 45,
    "EM_EDIT_STORYBOARD_TYPE_REGEN_SINGLE"= 46,
    "EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_PIC"= 47,
    "EM_EDIT_STORYBOARD_TYPE_REGEN_PIC"= 48,
    "EM_EDIT_STORYBOARD_TYPE_REGEN_VIDEO"= 49,
    "EM_EDIT_STORYBOARD_TYPE_MODIFY_VIDEO_MODEL"= 50,
    "EM_EDIT_STORYBOARD_TYPE_MODIFY_PIC_MODEL"= 51,
    "EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_PIC"= 52,
    "EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_VIDEO"= 53,
    "EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_VIDEO"= 54,
    "EM_EDIT_STORYBOARD_TYPE_EDIT_VIDEO_LENGTH"= 55,
    "EM_EDIT_STORYBOARD_TYPE_GEN_STORY"= 57,
    "EM_EDIT_STORYBOARD_TYPE_USE_MATERIAL"= 58,
    "EM_EDIT_ROLE_TYPE_REGEN"= 60,
    "EM_EDIT_ROLE_TYPE_UPLOAD"= 61,
    "EM_EDIT_ROLE_TYPE_AI_GEN"= 62,
    "EM_EDIT_ROLE_TYPE_GEN_ROLE_TIPS"= 63,
    "EM_EDIT_ROLE_TYPE_CHANGE_ROLE"= 64,
    "EM_EDIT_ROLE_TYPE_CONFIRM"= 65,
    "EM_EDIT_ROLE_TYPE_CHOOSE_ROLE_LIST"= 66,
    "EM_EDIT_ROLE_TYPE_DELETE_ROLE"= 67,
    "EM_EDIT_ENV_TYPE_REGEN"= 80,
    "EM_EDIT_ENV_TYPE_UPLOAD"= 81,
    "EM_EDIT_ENV_TYPE_AI_GEN"= 82,
  }

  export interface $DetermineTopicReq {
    strScriptId: string;
    vectorEditField: Array<proto_lens_script_svr.emEditType>;
    stTopic: $proto_lens_script_comm.proto_lens_script_comm.$TopicInfo;
  }
  export const DetermineTopicReq: $DetermineTopicReq;

  export interface $DetermineTopicRsp {
    strScriptId: string;
    stTopic: $proto_lens_script_comm.proto_lens_script_comm.$TopicInfo;
  }
  export const DetermineTopicRsp: $DetermineTopicRsp;

  export interface $AutoGenAllReq {
    strScriptId: string;
    eType: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
    eSource: $proto_lens_script_comm.proto_lens_script_comm.emFromSource;
  }
  export const AutoGenAllReq: $AutoGenAllReq;

  export interface $AutoGenAllRsp {
    iRes: number;
    strTips: string;
  }
  export const AutoGenAllRsp: $AutoGenAllRsp;

  export interface $DoMainProcessReq {
    strScriptId: string;
    eType: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
  }
  export const DoMainProcessReq: $DoMainProcessReq;

  export interface $DoMainProcessRsp {
    iRes: number;
    strTips: string;
  }
  export const DoMainProcessRsp: $DoMainProcessRsp;

  export interface $EditSceneReq {
    strScriptId: string;
    vectorEditField: Array<proto_lens_script_svr.emEditType>;
    vecScene: Array<$proto_lens_script_comm.proto_lens_script_comm.$SceneInfo>;
  }
  export const EditSceneReq: $EditSceneReq;

  export interface $EditSceneRsp {
    iRes: number;
    strTips: string;
    scene: $proto_lens_script_comm.proto_lens_script_comm.$SceneInfo;
  }
  export const EditSceneRsp: $EditSceneRsp;

  export interface $EditRoleReq {
    strScriptId: string;
    vectorEditField: Array<proto_lens_script_svr.emEditType>;
    vecRole: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
  }
  export const EditRoleReq: $EditRoleReq;

  export interface $EditRoleRsp {
    iRes: number;
    strTips: string;
    stRole: $proto_lens_script_comm.proto_lens_script_comm.$RoleInfo;
  }
  export const EditRoleRsp: $EditRoleRsp;

  export interface $EditEnvReq {
    strScriptId: string;
    vectorEditField: Array<proto_lens_script_svr.emEditType>;
    stEnv: $proto_lens_script_comm.proto_lens_script_comm.$EnvInfo;
  }
  export const EditEnvReq: $EditEnvReq;

  export interface $EditEnvRsp {
    iRes: number;
    strTips: string;
  }
  export const EditEnvRsp: $EditEnvRsp;

  export interface $MaterialItem {
    stVideoInfo: $proto_lens_script_comm.proto_lens_script_comm.$VideoInfo;
    stPicInfo: $proto_lens_script_comm.proto_lens_script_comm.$PicInfo;
    materialID: string;
  }
  export const MaterialItem: $MaterialItem;

  export interface $EditStoryboardReq {
    strScriptId: string;
    vectorEditField: Array<proto_lens_script_svr.emEditType>;
    stBoardInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$StoryboardInfo>;
    stMaterial: proto_lens_script_svr.$MaterialItem;
  }
  export const EditStoryboardReq: $EditStoryboardReq;

  export interface $EditStoryboardRsp {
    iRes: number;
    strTips: string;
    storyboard: $proto_lens_script_comm.proto_lens_script_comm.$StoryboardInfo;
  }
  export const EditStoryboardRsp: $EditStoryboardRsp;

  export interface $EditTextReq {
    strScriptId: string;
    strTrackId: string;
    stInfo: $proto_lens_script_comm.proto_lens_script_comm.$SubtitleItem;
  }
  export const EditTextReq: $EditTextReq;

  export interface $EditTextRsp {
    iRes: number;
    strTips: string;
  }
  export const EditTextRsp: $EditTextRsp;

  export interface $EditAudioReq {
    strScriptId: string;
    strTrackId: string;
    stInfo: $proto_lens_script_comm.proto_lens_script_comm.$AudioItem;
  }
  export const EditAudioReq: $EditAudioReq;

  export interface $EditAudioRsp {
    iRes: number;
    strTips: string;
  }
  export const EditAudioRsp: $EditAudioRsp;

  export enum emModifyType {
    "EM_MODIFY_TYPE_NICK"= 1,
    "EM_MODIFY_TYPE_AVATAR"= 2,
  }

  export interface $ModifyPersonInfoReq {
    stUserInfo: $proto_lens.proto_lens.$UserInfo;
    eType: proto_lens_script_svr.emModifyType;
  }
  export const ModifyPersonInfoReq: $ModifyPersonInfoReq;

  export interface $ModifyPersonInfoRsp {
    iRes: number;
    strTips: string;
  }
  export const ModifyPersonInfoRsp: $ModifyPersonInfoRsp;

  export interface $ModifyVideoCoverReq {
    strScriptId: string;
    strCoverUrl: string;
  }
  export const ModifyVideoCoverReq: $ModifyVideoCoverReq;

  export interface $ModifyVideoCoverRsp {
    iRes: number;
    strTips: string;
  }
  export const ModifyVideoCoverRsp: $ModifyVideoCoverRsp;

  export interface $GetRecycleInfoReq {
    strScriptId: string;
  }
  export const GetRecycleInfoReq: $GetRecycleInfoReq;

  export interface $GetRecycleInfoRsp {
    stInfo: $proto_lens_script_comm.proto_lens_script_comm.$RecycleInfo;
  }
  export const GetRecycleInfoRsp: $GetRecycleInfoRsp;

  export interface $ReductionItemReq {
    strScriptId: string;
    stItem: $proto_lens_script_comm.proto_lens_script_comm.$RecycleItem;
  }
  export const ReductionItemReq: $ReductionItemReq;

  export interface $ReductionItemRsp {
    iRes: number;
    strTips: string;
  }
  export const ReductionItemRsp: $ReductionItemRsp;

  export interface $SwCallbackReq {
    iUid: number;
    strScriptId: string;
    strUniqueId: string;
    eType: $proto_lens_script_comm.proto_lens_script_comm.emSubProcessType;
    mapData: {[key: string]: string};
    eFlow: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
    iErrCode: number;
    strErrMsg: string;
  }
  export const SwCallbackReq: $SwCallbackReq;

  export interface $SwCallbackRsp {
    iRes: number;
  }
  export const SwCallbackRsp: $SwCallbackRsp;

  export interface $DeleteScriptReq {
    strScriptId: string;
  }
  export const DeleteScriptReq: $DeleteScriptReq;

  export interface $DeleteScriptRsp {
    iRes: number;
    strTips: string;
  }
  export const DeleteScriptRsp: $DeleteScriptRsp;

  export interface $BatchSongItem {
    strAudioUrl: string;
    iAudioLength: number;
    strLyric: string;
    strSongName: string;
    strSingerName: string;
    strTargetId: string;
  }
  export const BatchSongItem: $BatchSongItem;

  export interface $BatchProductionReq {
    strAppId: string;
    eType: $proto_lens_script_comm.proto_lens_script_comm.emIDType;
    vecId: Array<string>;
    vecSong: Array<proto_lens_script_svr.$BatchSongItem>;
  }
  export const BatchProductionReq: $BatchProductionReq;

  export interface $BatchProductionRsp {
    iRes: number;
  }
  export const BatchProductionRsp: $BatchProductionRsp;

  export enum emAuditStatus {
    "EM_AUDIT_STATUS_SUCC"= 1,
    "EM_AUDIT_STATUS_PIC_FAILED"= 2,
    "EM_AUDIT_STATUS_VIO_FAILED"= 3,
  }

  export interface $AuditResultReq {
    eStatus: proto_lens_script_svr.emAuditStatus;
    strRemark: string;
  }
  export const AuditResultReq: $AuditResultReq;

  export interface $AuditResultRsp {
    iRes: number;
  }
  export const AuditResultRsp: $AuditResultRsp;

  export interface $BackendGenReq {
    strScriptId: string;
    eIdType: $proto_lens_script_comm.proto_lens_script_comm.emIDType;
    strId: string;
    strDBPrimaryId: string;
    strBatchId: string;
    strExtInspiration: string;
    strProcFlowId: string;
    strPicModel: string;
    strAppId: string;
    stSongInfo: proto_lens_script_svr.$BatchSongItem;
  }
  export const BackendGenReq: $BackendGenReq;

  export interface $BackendGenRsp {
    iRes: number;
  }
  export const BackendGenRsp: $BackendGenRsp;

  export interface $MvUrlItem {
    eIdType: $proto_lens_script_comm.proto_lens_script_comm.emIDType;
    strId: string;
    strMvUrl: string;
    strAppId: string;
  }
  export const MvUrlItem: $MvUrlItem;

  export interface $PushMvUrlReq {
    vecInfo: Array<proto_lens_script_svr.$MvUrlItem>;
  }
  export const PushMvUrlReq: $PushMvUrlReq;

  export interface $PushMvUrlRsp {
    iRes: number;
  }
  export const PushMvUrlRsp: $PushMvUrlRsp;

  export interface $PicAnalyzeReq {
    strScriptId: string;
    vecPic: Array<string>;
  }
  export const PicAnalyzeReq: $PicAnalyzeReq;

  export interface $PicAnalyzeRsp {
    vecTag: Array<string>;
  }
  export const PicAnalyzeRsp: $PicAnalyzeRsp;

  export interface $GetMaterialListReq {
    strScriptId: string;
    strPicTips: string;
    strPicModelId: string;
  }
  export const GetMaterialListReq: $GetMaterialListReq;

  export interface $GetMaterialListRsp {
    vecInfo: Array<proto_lens_script_svr.$MaterialItem>;
  }
  export const GetMaterialListRsp: $GetMaterialListRsp;

  export interface $GetRoleInfoReq {
    strScriptId: string;
    strRoleId: string;
  }
  export const GetRoleInfoReq: $GetRoleInfoReq;

  export interface $GetRoleInfoRsp {
    stRoleInfo: $proto_lens_script_comm.proto_lens_script_comm.$RoleInfo;
  }
  export const GetRoleInfoRsp: $GetRoleInfoRsp;

  export interface $AudioUrlReq {
    strSongId: string;
  }
  export const AudioUrlReq: $AudioUrlReq;

  export interface $AudioUrlRsp {
    strAudioUrl: string;
  }
  export const AudioUrlRsp: $AudioUrlRsp;

}

export const LENS_SCRIPT_SVR_MOD_ID = 0;

export const CMD_GET_FRONT_PAGE = "interact.lens_script.svr.get_front_page";

export const CMD_GET_PERSON_INFO = "interact.lens_script.svr.get_person_info";

export const CMD_GET_SCRIPT_INFO = "interact.lens_script.svr.get_script_info";

export const CMD_SEARCH_SONG_LIST = "interact.lens_script.svr.search_song_list";

export const CMD_SONG_INFO = "interact.lens_script.svr.song_info";

export const CMD_BASIC_INFO = "interact.lens_script.svr.basic_info";

export const CMD_DETERMINE_TOPIC = "interact.lens_script.svr.determine_topic";

export const CMD_AUTO_GEN_ALL = "interact.lens_script.svr.auto_gen_all";

export const CMD_DO_MAIN_PROCESS = "interact.lens_script.svr.do_main_process";

export const CMD_EDIT_SCENE = "interact.lens_script.svr.edit_scene";

export const CMD_EDIT_ROLE = "interact.lens_script.svr.edit_role";

export const CMD_EDIT_ENV = "interact.lens_script.svr.edit_env";

export const CMD_EDIT_STORYBOARD = "interact.lens_script.svr.edit_storyboard";

export const CMD_EDIT_TEXT = "interact.lens_script.svr.edit_text";

export const CMD_EDIT_AUDIO = "interact.lens_script.svr.edit_audio";

export const CMD_MODITY_PERSON_INFO = "interact.lens_script.svr.modify_person_info";

export const CMD_MODITY_VIDEO_COVER = "interact.lens_script.svr.modify_video_cover";

export const CMD_GET_RECYCLE_INFO = "interact.lens_script.svr.get_recycle_info";

export const CMD_REDUCTION_ITEM = "interact.lens_script.svr.reduction_item";

export const CMD_DELETE_SCRIPT = "interact.lens_script.svr.delete_script";

export const CMD_SW_CALLBACK = "interact.lens_script.svr.sw_call_back";

export const CMD_BATCH_PRODUCTION = "interact.lens_script.svr.batch_production";

export const CMD_AUDIT_RESULT = "interact.lens_script.svr.audit_result";

export const CMD_BACKEND_GEN = "interact.lens_script.svr.backend_gen";

export const CMD_PUSH_MV_URL = "interact.lens_script.svr.push_mv_url";

export const CMD_PICTURE_ANALYZE = "interact.lens_script.svr.picture_analyze";

export const CMD_GET_MATERIAL_LIST = "interact.lens_script.svr.get_material_list";

export const CMD_GET_ROLE_INFO = "interact.lens_script.svr.get_role_info";

export const CMD_GET_AUDIO_URL = "interact.lens_script.svr.get_audio_url";

export interface $GetFrontPageReq {
  iPageIndex: number;
  iPageCnt: number;
}
export const GetFrontPageReq: $GetFrontPageReq;

export interface $GetFrontPageRsp {
  vecList: Array<$proto_lens_script_comm.proto_lens_script_comm.$WebVideoInfo>;
  iTotal: number;
  strTitleDetail: string;
  strDocsUrl: string;
  iCanAccess: number;
  strRefuseTips: string;
}
export const GetFrontPageRsp: $GetFrontPageRsp;

export enum EmGetPersonInfoMask {
  "EM_GET_PERSON_INFO_TYPE_ALL"= 8191,
  "EM_GET_PERSON_INFO_TYPE_ROLE"= 1,
  "EM_GET_PERSON_INFO_TYPE_VIDEO_LIST"= 2,
  "EM_GET_PERSON_INFO_TYPE_ENV"= 4,
}

export interface $GetPersonInfoReq {
  IGetMask: proto_lens_script_svr.EmGetPersonInfoMask;
}
export const GetPersonInfoReq: $GetPersonInfoReq;

export interface $GetPersonInfoRsp {
  vecRole: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
  vecVideo: Array<$proto_lens_script_comm.proto_lens_script_comm.$WebVideoInfo>;
  VecEnv: Array<$proto_lens_script_comm.proto_lens_script_comm.$EnvInfo>;
}
export const GetPersonInfoRsp: $GetPersonInfoRsp;

export enum emScriptGetMask {
  "EM_SCRIPT_GET_TYPE_ALL"= 8191,
  "EM_SCRIPT_GET_TYPE_TOPIC"= 1,
  "EM_SCRIPT_GET_TYPE_SCENE"= 2,
  "EM_SCRIPT_GET_TYPE_STORYBOARD"= 4,
  "EM_SCRIPT_GET_TYPE_MERGE"= 8,
  "EM_SCRIPT_GET_TYPE_ROLE"= 16,
  "EM_SCRIPT_GET_TYPE_ENV"= 32,
}

export interface $GetScriptInfoReq {
  strScriptId: string;
  eGetType: proto_lens_script_svr.emScriptGetMask;
}
export const GetScriptInfoReq: $GetScriptInfoReq;

export interface $WebSceneInfo {
  strId: string;
  strLyric: string;
  eStatus: $proto_lens_script_comm.proto_lens_script_comm.emStatus;
  Desc: Array<$proto_lens_script_comm.proto_lens_script_comm.$SceneceDescItem>;
  vecRoleInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
  vecEnvInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$EnvInfo>;
  iStoryboardCnt: number;
  iIndex: number;
  iTimeRange: number;
}
export const WebSceneInfo: $WebSceneInfo;

export interface $WebStoryboardInfo {
  strId: string;
  strSceneId: string;
  strStory: string;
  eStatus: $proto_lens_script_comm.proto_lens_script_comm.emStatus;
  stCurPic: $proto_lens_script_comm.proto_lens_script_comm.$PicInfo;
  vecPic: Array<$proto_lens_script_comm.proto_lens_script_comm.$PicInfo>;
  stCurVideo: $proto_lens_script_comm.proto_lens_script_comm.$VideoInfo;
  vecVideo: Array<$proto_lens_script_comm.proto_lens_script_comm.$VideoInfo>;
  vecRoleInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
  stEnvInfo: $proto_lens_script_comm.proto_lens_script_comm.$EnvInfo;
  iStartTime: number;
  iTimeRange: number;
  strShotType: string;
  iIndex: number;
}
export const WebStoryboardInfo: $WebStoryboardInfo;

export interface $WebScriptInfo {
  strName: string;
  eCurFlow: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
  stTopic: $proto_lens_script_comm.proto_lens_script_comm.$TopicInfo;
  vecScene: Array<proto_lens_script_svr.$WebSceneInfo>;
  VecStoryboard: Array<proto_lens_script_svr.$WebStoryboardInfo>;
  stSubtitle: $proto_lens_script_comm.proto_lens_script_comm.$SubtitleInfo;
  stAudio: $proto_lens_script_comm.proto_lens_script_comm.$Audio;
  lPublichTime: number;
  strVideoUrl: string;
  strCoverImg: string;
  iTimeRange: number;
  mapErrInfo: {[key: string]: string};
  vecRoleInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
  vecEnvInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$EnvInfo>;
  strScriptId: string;
  mapFlowStatus: {[key: $proto_lens_script_comm.proto_lens_script_comm.emFlow]: $proto_lens_script_comm.proto_lens_script_comm.emStatus};
  iIsAuto: number;
  vecNewScene: Array<$proto_lens_script_comm.proto_lens_script_comm.$SceneInfo>;
  VecNewStoryboard: Array<$proto_lens_script_comm.proto_lens_script_comm.$StoryboardInfo>;
}
export const WebScriptInfo: $WebScriptInfo;

export interface $GetScriptInfoRsp {
  stData: proto_lens_script_svr.$WebScriptInfo;
  strTips: string;
}
export const GetScriptInfoRsp: $GetScriptInfoRsp;

export interface $SearchSongListReq {
  strSongName: string;
}
export const SearchSongListReq: $SearchSongListReq;

export interface $SongList {
  strSongId: string;
  strSongName: string;
  strSinger: string;
  strMid: string;
}
export const SongList: $SongList;

export interface $SearchSongListRsp {
  vecList: Array<proto_lens_script_svr.$SongList>;
}
export const SearchSongListRsp: $SearchSongListRsp;

export interface $SongInfoReq {
  strMid: string;
  strSongId: string;
  uKid: number;
}
export const SongInfoReq: $SongInfoReq;

export interface $SongInfoRsp {
  stSong: $proto_lens_script_comm.proto_lens_script_comm.$SongInfo;
}
export const SongInfoRsp: $SongInfoRsp;

export enum emBasicMask {
  "EM_BASIC_MASK_ALL"= 131071,
  "EM_BASIC_MASK_STYLE"= 1,
  "EM_BASIC_MASK_PIC_MOD"= 2,
  "EM_BASIC_MASK_VIDEO_MOD"= 4,
  "EM_BASIC_MASK_VIDEO_SIZE"= 8,
  "EM_BASIC_MASK_MOVEMENT"= 16,
  "EM_BASIC_MASK_MVTEMPLATE"= 32,
  "EM_BASIC_MASK_COMPOSE_PARAM"= 64,
  "EM_BASIC_MASK_REGION_LIST"= 128,
}

export interface $BasicInfoReq {
  eMask: proto_lens_script_svr.emBasicMask;
  strScriptId: string;
}
export const BasicInfoReq: $BasicInfoReq;

export interface $BasicInfoRsp {
  vecPicMod: Array<$proto_lens_script_comm.proto_lens_script_comm.$PictureModel>;
  vecViMod: Array<$proto_lens_script_comm.proto_lens_script_comm.$VideoModel>;
  vecVideoSize: Array<$proto_lens_script_comm.proto_lens_script_comm.$VideoSize>;
  vecMovement: Array<$proto_lens_script_comm.proto_lens_script_comm.$Movement>;
  vecCompose: Array<$proto_lens_script_comm.proto_lens_script_comm.$ComposeModel>;
  vecRegion: Array<$proto_lens_script_comm.proto_lens_script_comm.$Region>;
}
export const BasicInfoRsp: $BasicInfoRsp;

export enum emEditType {
  "EM_EDIT_TOPIC_TYPE_INSPRATION"= 1,
  "EM_EDIT_TOPIC_TYPE_PICMODEL"= 2,
  "EM_EDIT_TOPIC_TYPE_VIDMODEL"= 3,
  "EM_EDIT_TOPIC_TYPE_MOVEMENT"= 4,
  "EM_EDIT_TOPIC_TYPE_MVSIZE"= 5,
  "EM_EDIT_TOPIC_TYPE_MVTEMPLATE"= 6,
  "EM_EDIT_TOPIC_TYPE_SONGINFO"= 7,
  "EM_EDIT_TOPIC_TYPE_REGEN"= 8,
  "EM_EDIT_TOPIC_MODIFY_TAG"= 9,
  "EM_EDIT_TOPIC_MODIFY_QUALITY"= 10,
  "EM_EDIT_SCEN_TYPE_SCENEDESC"= 20,
  "EM_EDIT_SCEN_TYPE_ROLEID"= 21,
  "EM_EDIT_SCEN_TYPE_ENVID"= 22,
  "EM_EDIT_SCEN_TYPE_STORYBOARDCNT"= 23,
  "EM_EDIT_SCEN_TYPE_DELETE"= 24,
  "EM_EDIT_SCEN_TYPE_ADD"= 25,
  "EM_EDIT_SCEN_TYPE_REGEN_SINGLE"= 26,
  "EM_EDIT_SCEN_TYPE_REGEN_ALL"= 27,
  "EM_EDIT_SCEN_TYPE_CHANGE_ORDER"= 28,
  "EM_EDIT_SCEN_TYPE_REGEN_STORYBOARD"= 29,
  "EM_EDIT_SCEN_TYPE_GEN_TITLE"= 30,
  "EM_EDIT_STORYBOARD_TYPE_STORY"= 40,
  "EM_EDIT_STORYBOARD_TYPE_ROLEID"= 41,
  "EM_EDIT_STORYBOARD_TYPE_ENVID"= 42,
  "EM_EDIT_STORYBOARD_TYPE_CHANGE_ORDER"= 43,
  "EM_EDIT_STORYBOARD_TYPE_DELETE"= 44,
  "EM_EDIT_STORYBOARD_TYPE_ADD"= 45,
  "EM_EDIT_STORYBOARD_TYPE_REGEN_SINGLE"= 46,
  "EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_PIC"= 47,
  "EM_EDIT_STORYBOARD_TYPE_REGEN_PIC"= 48,
  "EM_EDIT_STORYBOARD_TYPE_REGEN_VIDEO"= 49,
  "EM_EDIT_STORYBOARD_TYPE_MODIFY_VIDEO_MODEL"= 50,
  "EM_EDIT_STORYBOARD_TYPE_MODIFY_PIC_MODEL"= 51,
  "EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_PIC"= 52,
  "EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_VIDEO"= 53,
  "EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_VIDEO"= 54,
  "EM_EDIT_STORYBOARD_TYPE_EDIT_VIDEO_LENGTH"= 55,
  "EM_EDIT_STORYBOARD_TYPE_GEN_STORY"= 57,
  "EM_EDIT_STORYBOARD_TYPE_USE_MATERIAL"= 58,
  "EM_EDIT_ROLE_TYPE_REGEN"= 60,
  "EM_EDIT_ROLE_TYPE_UPLOAD"= 61,
  "EM_EDIT_ROLE_TYPE_AI_GEN"= 62,
  "EM_EDIT_ROLE_TYPE_GEN_ROLE_TIPS"= 63,
  "EM_EDIT_ROLE_TYPE_CHANGE_ROLE"= 64,
  "EM_EDIT_ROLE_TYPE_CONFIRM"= 65,
  "EM_EDIT_ROLE_TYPE_CHOOSE_ROLE_LIST"= 66,
  "EM_EDIT_ROLE_TYPE_DELETE_ROLE"= 67,
  "EM_EDIT_ENV_TYPE_REGEN"= 80,
  "EM_EDIT_ENV_TYPE_UPLOAD"= 81,
  "EM_EDIT_ENV_TYPE_AI_GEN"= 82,
}

export interface $DetermineTopicReq {
  strScriptId: string;
  vectorEditField: Array<proto_lens_script_svr.emEditType>;
  stTopic: $proto_lens_script_comm.proto_lens_script_comm.$TopicInfo;
}
export const DetermineTopicReq: $DetermineTopicReq;

export interface $DetermineTopicRsp {
  strScriptId: string;
  stTopic: $proto_lens_script_comm.proto_lens_script_comm.$TopicInfo;
}
export const DetermineTopicRsp: $DetermineTopicRsp;

export interface $AutoGenAllReq {
  strScriptId: string;
  eType: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
  eSource: $proto_lens_script_comm.proto_lens_script_comm.emFromSource;
}
export const AutoGenAllReq: $AutoGenAllReq;

export interface $AutoGenAllRsp {
  iRes: number;
  strTips: string;
}
export const AutoGenAllRsp: $AutoGenAllRsp;

export interface $DoMainProcessReq {
  strScriptId: string;
  eType: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
}
export const DoMainProcessReq: $DoMainProcessReq;

export interface $DoMainProcessRsp {
  iRes: number;
  strTips: string;
}
export const DoMainProcessRsp: $DoMainProcessRsp;

export interface $EditSceneReq {
  strScriptId: string;
  vectorEditField: Array<proto_lens_script_svr.emEditType>;
  vecScene: Array<$proto_lens_script_comm.proto_lens_script_comm.$SceneInfo>;
}
export const EditSceneReq: $EditSceneReq;

export interface $EditSceneRsp {
  iRes: number;
  strTips: string;
  scene: $proto_lens_script_comm.proto_lens_script_comm.$SceneInfo;
}
export const EditSceneRsp: $EditSceneRsp;

export interface $EditRoleReq {
  strScriptId: string;
  vectorEditField: Array<proto_lens_script_svr.emEditType>;
  vecRole: Array<$proto_lens_script_comm.proto_lens_script_comm.$RoleInfo>;
}
export const EditRoleReq: $EditRoleReq;

export interface $EditRoleRsp {
  iRes: number;
  strTips: string;
  stRole: $proto_lens_script_comm.proto_lens_script_comm.$RoleInfo;
}
export const EditRoleRsp: $EditRoleRsp;

export interface $EditEnvReq {
  strScriptId: string;
  vectorEditField: Array<proto_lens_script_svr.emEditType>;
  stEnv: $proto_lens_script_comm.proto_lens_script_comm.$EnvInfo;
}
export const EditEnvReq: $EditEnvReq;

export interface $EditEnvRsp {
  iRes: number;
  strTips: string;
}
export const EditEnvRsp: $EditEnvRsp;

export interface $MaterialItem {
  stVideoInfo: $proto_lens_script_comm.proto_lens_script_comm.$VideoInfo;
  stPicInfo: $proto_lens_script_comm.proto_lens_script_comm.$PicInfo;
  materialID: string;
}
export const MaterialItem: $MaterialItem;

export interface $EditStoryboardReq {
  strScriptId: string;
  vectorEditField: Array<proto_lens_script_svr.emEditType>;
  stBoardInfo: Array<$proto_lens_script_comm.proto_lens_script_comm.$StoryboardInfo>;
  stMaterial: proto_lens_script_svr.$MaterialItem;
}
export const EditStoryboardReq: $EditStoryboardReq;

export interface $EditStoryboardRsp {
  iRes: number;
  strTips: string;
  storyboard: $proto_lens_script_comm.proto_lens_script_comm.$StoryboardInfo;
}
export const EditStoryboardRsp: $EditStoryboardRsp;

export interface $EditTextReq {
  strScriptId: string;
  strTrackId: string;
  stInfo: $proto_lens_script_comm.proto_lens_script_comm.$SubtitleItem;
}
export const EditTextReq: $EditTextReq;

export interface $EditTextRsp {
  iRes: number;
  strTips: string;
}
export const EditTextRsp: $EditTextRsp;

export interface $EditAudioReq {
  strScriptId: string;
  strTrackId: string;
  stInfo: $proto_lens_script_comm.proto_lens_script_comm.$AudioItem;
}
export const EditAudioReq: $EditAudioReq;

export interface $EditAudioRsp {
  iRes: number;
  strTips: string;
}
export const EditAudioRsp: $EditAudioRsp;

export enum emModifyType {
  "EM_MODIFY_TYPE_NICK"= 1,
  "EM_MODIFY_TYPE_AVATAR"= 2,
}

export interface $ModifyPersonInfoReq {
  stUserInfo: $proto_lens.proto_lens.$UserInfo;
  eType: proto_lens_script_svr.emModifyType;
}
export const ModifyPersonInfoReq: $ModifyPersonInfoReq;

export interface $ModifyPersonInfoRsp {
  iRes: number;
  strTips: string;
}
export const ModifyPersonInfoRsp: $ModifyPersonInfoRsp;

export interface $ModifyVideoCoverReq {
  strScriptId: string;
  strCoverUrl: string;
}
export const ModifyVideoCoverReq: $ModifyVideoCoverReq;

export interface $ModifyVideoCoverRsp {
  iRes: number;
  strTips: string;
}
export const ModifyVideoCoverRsp: $ModifyVideoCoverRsp;

export interface $GetRecycleInfoReq {
  strScriptId: string;
}
export const GetRecycleInfoReq: $GetRecycleInfoReq;

export interface $GetRecycleInfoRsp {
  stInfo: $proto_lens_script_comm.proto_lens_script_comm.$RecycleInfo;
}
export const GetRecycleInfoRsp: $GetRecycleInfoRsp;

export interface $ReductionItemReq {
  strScriptId: string;
  stItem: $proto_lens_script_comm.proto_lens_script_comm.$RecycleItem;
}
export const ReductionItemReq: $ReductionItemReq;

export interface $ReductionItemRsp {
  iRes: number;
  strTips: string;
}
export const ReductionItemRsp: $ReductionItemRsp;

export interface $SwCallbackReq {
  iUid: number;
  strScriptId: string;
  strUniqueId: string;
  eType: $proto_lens_script_comm.proto_lens_script_comm.emSubProcessType;
  mapData: {[key: string]: string};
  eFlow: $proto_lens_script_comm.proto_lens_script_comm.emFlow;
  iErrCode: number;
  strErrMsg: string;
}
export const SwCallbackReq: $SwCallbackReq;

export interface $SwCallbackRsp {
  iRes: number;
}
export const SwCallbackRsp: $SwCallbackRsp;

export interface $DeleteScriptReq {
  strScriptId: string;
}
export const DeleteScriptReq: $DeleteScriptReq;

export interface $DeleteScriptRsp {
  iRes: number;
  strTips: string;
}
export const DeleteScriptRsp: $DeleteScriptRsp;

export interface $BatchSongItem {
  strAudioUrl: string;
  iAudioLength: number;
  strLyric: string;
  strSongName: string;
  strSingerName: string;
  strTargetId: string;
}
export const BatchSongItem: $BatchSongItem;

export interface $BatchProductionReq {
  strAppId: string;
  eType: $proto_lens_script_comm.proto_lens_script_comm.emIDType;
  vecId: Array<string>;
  vecSong: Array<proto_lens_script_svr.$BatchSongItem>;
}
export const BatchProductionReq: $BatchProductionReq;

export interface $BatchProductionRsp {
  iRes: number;
}
export const BatchProductionRsp: $BatchProductionRsp;

export enum emAuditStatus {
  "EM_AUDIT_STATUS_SUCC"= 1,
  "EM_AUDIT_STATUS_PIC_FAILED"= 2,
  "EM_AUDIT_STATUS_VIO_FAILED"= 3,
}

export interface $AuditResultReq {
  eStatus: proto_lens_script_svr.emAuditStatus;
  strRemark: string;
}
export const AuditResultReq: $AuditResultReq;

export interface $AuditResultRsp {
  iRes: number;
}
export const AuditResultRsp: $AuditResultRsp;

export interface $BackendGenReq {
  strScriptId: string;
  eIdType: $proto_lens_script_comm.proto_lens_script_comm.emIDType;
  strId: string;
  strDBPrimaryId: string;
  strBatchId: string;
  strExtInspiration: string;
  strProcFlowId: string;
  strPicModel: string;
  strAppId: string;
  stSongInfo: proto_lens_script_svr.$BatchSongItem;
}
export const BackendGenReq: $BackendGenReq;

export interface $BackendGenRsp {
  iRes: number;
}
export const BackendGenRsp: $BackendGenRsp;

export interface $MvUrlItem {
  eIdType: $proto_lens_script_comm.proto_lens_script_comm.emIDType;
  strId: string;
  strMvUrl: string;
  strAppId: string;
}
export const MvUrlItem: $MvUrlItem;

export interface $PushMvUrlReq {
  vecInfo: Array<proto_lens_script_svr.$MvUrlItem>;
}
export const PushMvUrlReq: $PushMvUrlReq;

export interface $PushMvUrlRsp {
  iRes: number;
}
export const PushMvUrlRsp: $PushMvUrlRsp;

export interface $PicAnalyzeReq {
  strScriptId: string;
  vecPic: Array<string>;
}
export const PicAnalyzeReq: $PicAnalyzeReq;

export interface $PicAnalyzeRsp {
  vecTag: Array<string>;
}
export const PicAnalyzeRsp: $PicAnalyzeRsp;

export interface $GetMaterialListReq {
  strScriptId: string;
  strPicTips: string;
  strPicModelId: string;
}
export const GetMaterialListReq: $GetMaterialListReq;

export interface $GetMaterialListRsp {
  vecInfo: Array<proto_lens_script_svr.$MaterialItem>;
}
export const GetMaterialListRsp: $GetMaterialListRsp;

export interface $GetRoleInfoReq {
  strScriptId: string;
  strRoleId: string;
}
export const GetRoleInfoReq: $GetRoleInfoReq;

export interface $GetRoleInfoRsp {
  stRoleInfo: $proto_lens_script_comm.proto_lens_script_comm.$RoleInfo;
}
export const GetRoleInfoRsp: $GetRoleInfoRsp;

export interface $AudioUrlReq {
  strSongId: string;
}
export const AudioUrlReq: $AudioUrlReq;

export interface $AudioUrlRsp {
  strAudioUrl: string;
}
export const AudioUrlRsp: $AudioUrlRsp;