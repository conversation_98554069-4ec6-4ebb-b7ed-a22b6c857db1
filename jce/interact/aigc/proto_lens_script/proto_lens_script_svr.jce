#include "proto_lens_script_comm.jce"
#include "../proto_lens/proto_lens.jce"

module proto_lens_script_svr
{
    // 模调 ID
    const int LENS_SCRIPT_SVR_MOD_ID = 0;       // 棱镜主服务

    // 获取首页信息
    const string CMD_GET_FRONT_PAGE = "interact.lens_script.svr.get_front_page";
    // 获取个人中心(可以获取所有、也可以单个获取角色/作品/环境)
    const string CMD_GET_PERSON_INFO = "interact.lens_script.svr.get_person_info";
    // 获取脚本信息
    const string CMD_GET_SCRIPT_INFO = "interact.lens_script.svr.get_script_info";
    // 搜索歌曲
    const string CMD_SEARCH_SONG_LIST = "interact.lens_script.svr.search_song_list";
    // 获取歌曲信息(单个歌曲信息)
    const string CMD_SONG_INFO = "interact.lens_script.svr.song_info";
    // 获取基础信息（画面风格、MV 类型、MV 尺寸、图片模型、视频模型）
    const string CMD_BASIC_INFO = "interact.lens_script.svr.basic_info";
    // 确定主题 选定歌曲立即创作+修改主题
    const string CMD_DETERMINE_TOPIC = "interact.lens_script.svr.determine_topic";
    // 一键生成 MV
    const string CMD_AUTO_GEN_ALL = "interact.lens_script.svr.auto_gen_all";
    // 主流程生成（脚本生成、分镜画面、视频生成、合成视频）
    const string CMD_DO_MAIN_PROCESS = "interact.lens_script.svr.do_main_process";
   // 场景编辑（新增/修改/重新生成/删除/分镜数量/调整角色/环境绑定)
    const string CMD_EDIT_SCENE = "interact.lens_script.svr.edit_scene";
    // 角色编辑（新增/修改/删除）
    const string CMD_EDIT_ROLE = "interact.lens_script.svr.edit_role";
    // 环境编辑（新增/修改/删除）
    const string CMD_EDIT_ENV = "interact.lens_script.svr.edit_env";
    // 分镜图片编辑（新增/修改/重新生成/删除/调整角色/环境绑定)
    const string CMD_EDIT_STORYBOARD = "interact.lens_script.svr.edit_storyboard";
    // 文本编辑（新增/修改/删除）
    const string CMD_EDIT_TEXT = "interact.lens_script.svr.edit_text";
    // 音频编辑（新增/修改/删除）
    const string CMD_EDIT_AUDIO= "interact.lens_script.svr.edit_audio";
    // 修改个人信息(头像、昵称、绑定关系等)
    const string CMD_MODITY_PERSON_INFO = "interact.lens_script.svr.modify_person_info";
    // 修改视频封面
    const string CMD_MODITY_VIDEO_COVER = "interact.lens_script.svr.modify_video_cover";
    // 获取回收站信息
    const string CMD_GET_RECYCLE_INFO = "interact.lens_script.svr.get_recycle_info";
    // 还原回收站信息
    const string CMD_REDUCTION_ITEM = "interact.lens_script.svr.reduction_item";
    // 删除作品
    const string CMD_DELETE_SCRIPT = "interact.lens_script.svr.delete_script";
    // SW回调更新scriptInfo -- 后台专用命令字
    const string CMD_SW_CALLBACK = "interact.lens_script.svr.sw_call_back";
    // 批量入库生产 MV
    const string CMD_BATCH_PRODUCTION = "interact.lens_script.svr.batch_production";
    // 审核结果回调
    const string CMD_AUDIT_RESULT = "interact.lens_script.svr.audit_result";
    // 后台生成 MV
    const string CMD_BACKEND_GEN = "interact.lens_script.svr.backend_gen";
    // PushMV
    const string CMD_PUSH_MV_URL = "interact.lens_script.svr.push_mv_url";
    // 图片解析
    const string CMD_PICTURE_ANALYZE = "interact.lens_script.svr.picture_analyze";
    // 获取素材库列表
    const string CMD_GET_MATERIAL_LIST = "interact.lens_script.svr.get_material_list";
    // 获取角色信息
    const string CMD_GET_ROLE_INFO = "interact.lens_script.svr.get_role_info";
    // 获取音频文件
    const string CMD_GET_AUDIO_URL = "interact.lens_script.svr.get_audio_url";

    // CMD_GET_FRONT_PAGE 获取首页信息
    struct GetFrontPageReq{
        0 optional int iPageIndex;          // 页面 Index
        1 optional int iPageCnt;            // 单次获取数量
    };

    struct GetFrontPageRsp{
        0 optional vector<proto_lens_script_comm::WebVideoInfo>   vecList;    // 精彩推荐列表
        1 optional int iTotal;                     // 总数
        2 optional string strTitleDetail;                   // 标题下面的描述
        3 optional string strDocsUrl;                       // 帮助文档链接
        4 optional int iCanAccess;                          // 是否能够访问
        5 optional string strRefuseTips;                    // 拒绝文案
    };

    // CMD_GET_PERSON_INFO 获取个人中心(可以获取所有、也可以单个获取角色/作品/环境)
    enum EmGetPersonInfoMask{
        EM_GET_PERSON_INFO_TYPE_ALL         = 0x1fff,        // 获取所有
        EM_GET_PERSON_INFO_TYPE_ROLE        = 0x1,       // 获取角色列表
        EM_GET_PERSON_INFO_TYPE_VIDEO_LIST  = 0x2, // 获取作品列表
        EM_GET_PERSON_INFO_TYPE_ENV         = 0x4,      // 获取环境列表
    };
    struct GetPersonInfoReq{
        0 optional EmGetPersonInfoMask IGetMask;   // 获取类型
    };

    struct GetPersonInfoRsp{
        0 optional vector<proto_lens_script_comm::RoleInfo> vecRole;
        1 optional vector<proto_lens_script_comm::WebVideoInfo> vecVideo;
        2 optional vector<proto_lens_script_comm::EnvInfo> VecEnv;
    };

    // CMD_GET_SCRIPT_INFO 获取脚本信息
    enum emScriptGetMask{
        EM_SCRIPT_GET_TYPE_ALL          = 0x1fff,                 // 所有信息
        EM_SCRIPT_GET_TYPE_TOPIC        = 0x1,                   // 主题信息
        EM_SCRIPT_GET_TYPE_SCENE        = 0x2,                   // 场景脚本
        EM_SCRIPT_GET_TYPE_STORYBOARD   = 0x4,                   // 分镜信息（分镜文案、图片、视频）
        EM_SCRIPT_GET_TYPE_MERGE        = 0x8,                   // 视频合成
        EM_SCRIPT_GET_TYPE_ROLE         = 0x10,                  // 获取当前脚本的角色列表
        EM_SCRIPT_GET_TYPE_ENV          = 0x20,                  // 获取当前脚本的环境列表
    };
    struct GetScriptInfoReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional emScriptGetMask eGetType;
    };
    struct WebSceneInfo{
        0 optional string strId;                        // 场景 ID
        1 optional string strLyric;                     // 场景歌词
        2 optional proto_lens_script_comm::emStatus eStatus;                    // 场景状态
        3 optional vector<proto_lens_script_comm::SceneceDescItem>Desc;         // 场景描述
        4 optional vector<proto_lens_script_comm::RoleInfo> vecRoleInfo;            // 角色 ID
        5 optional vector<proto_lens_script_comm::EnvInfo> vecEnvInfo;             // 环境 ID
        6 optional int iStoryboardCnt;                  // 分镜数量
        7 optional int iIndex;                          // 场景序号
        8 optional int iTimeRange;                     // 场景时长(ms)
    };

    struct WebStoryboardInfo{
        0  optional string            strId;         // 分镜 ID
        1  optional string            strSceneId;    // 属于哪个场景ID
        2  optional string            strStory;      // 分镜故事
        3  optional proto_lens_script_comm::emStatus          eStatus;       // 分镜状态
        4  optional proto_lens_script_comm::PicInfo           stCurPic;      // 当前使用的照片
        5  optional vector<proto_lens_script_comm::PicInfo>   vecPic;        // 历史图片
        6  optional proto_lens_script_comm::VideoInfo         stCurVideo;    // 当前使用的视频
        7  optional vector<proto_lens_script_comm::VideoInfo> vecVideo;      // 历史生成的视频
        8  optional vector<proto_lens_script_comm::RoleInfo>    vecRoleInfo;     // 关联的角色ID
        9  optional proto_lens_script_comm::EnvInfo            stEnvInfo;      // 环境 ID -- 二期废弃
        10 optional int               iStartTime;     // 开始时间
        11 optional int iTimeRange;                     // 分镜时长(ms)
        12  optional string           strShotType;   // 景别(中景、远景等)
        13 optional int               iIndex;        // 分镜序号
    };

    struct WebScriptInfo{
        0 optional string strName;                                              // 剧本名称
        1 optional proto_lens_script_comm::emFlow eCurFlow;                     // 当前流程
        2 optional proto_lens_script_comm::TopicInfo stTopic;                   // MV主题信息
        3 optional vector<WebSceneInfo> vecScene;                               // 场景信息 -- 二期废弃的字段
        4 optional vector<WebStoryboardInfo> VecStoryboard;                     // 分镜信息 -- 二期废弃的字段
        5 optional proto_lens_script_comm::SubtitleInfo stSubtitle;             // 字幕信息
        6 optional proto_lens_script_comm::Audio stAudio;                       // 音频信息
        7 optional long lPublichTime;                                           // 合成时间
        8 optional string strVideoUrl;                                          // 视频 Url
        9 optional string strCoverImg;                                          // 封面图片
        10 optional int iTimeRange;                                             // 视频片段时长
        11 optional map<string, string> mapErrInfo;                             // 如果某个流程失败，可以从这里获取流程失败的原因
        12 optional vector<proto_lens_script_comm::RoleInfo> vecRoleInfo;       // 角色列表
        13 optional vector<proto_lens_script_comm::EnvInfo> vecEnvInfo;         // 环境列表
        14 optional string strScriptId;                                         // 脚本 ID
        15 optional map<proto_lens_script_comm::emFlow, proto_lens_script_comm::emStatus> mapFlowStatus; // 每个大流程的状态
        16 optional int iIsAuto;                                                // 是否是一键成片
        17 optional vector<proto_lens_script_comm::SceneInfo> vecNewScene;        // 场景信息
        18 optional vector<proto_lens_script_comm::StoryboardInfo> VecNewStoryboard;  // 分镜信息
    };

    struct GetScriptInfoRsp{
        0 optional WebScriptInfo stData;     // 脚本信息
        1 optional string strTips;             // 提示语
    };

    // CMD_SEARCH_SONG_LIST 搜索歌曲
    struct SearchSongListReq{
        0 optional string strSongName;          // 歌曲名称
    };
    struct SongList{
        0 optional string strSongId;        // 歌曲Id
        1 optional string strSongName;      // 歌曲名称
        2 optional string strSinger;        // 歌手
        3 optional string strMid;           // 歌曲 Mid
    };
    struct SearchSongListRsp{
        0 optional vector<SongList> vecList;        // 搜歌列表
    };

    // CMD_SONG_INFO 获取歌曲信息(单个歌曲信息)
    struct SongInfoReq{
        0 optional string strMid;           // mid
        1 optional string strSongId;        // 歌曲 ID
        2 optional unsigned int uKid;       // K歌 歌曲 id
    };

    struct SongInfoRsp{
        0 optional proto_lens_script_comm::SongInfo stSong;
    };

    // CMD_BASIC_INFO 获取基础信息（画面风格、MV 类型、MV 尺寸、图片模型、视频模型）
    enum emBasicMask{
        EM_BASIC_MASK_ALL           = 0x1ffff,      // 所有
        EM_BASIC_MASK_STYLE         = 0x1,          // 画面风格
        EM_BASIC_MASK_PIC_MOD       = 0x2,          // 图片模型
        EM_BASIC_MASK_VIDEO_MOD     = 0x4,          // 视频模型
        EM_BASIC_MASK_VIDEO_SIZE    = 0x8,          // 视频尺寸
        EM_BASIC_MASK_MOVEMENT      = 0x10,         // 运镜信息
        EM_BASIC_MASK_MVTEMPLATE    = 0x20,         // MV类型
        EM_BASIC_MASK_COMPOSE_PARAM    = 0x40,      // 合成视频的参数
        EM_BASIC_MASK_REGION_LIST    = 0x80,        // 角色地区
    };
    struct BasicInfoReq{
        0 optional emBasicMask eMask;           // 获取信息
        1 optional string strScriptId;          // 脚本信息
    };
    struct BasicInfoRsp{
        0 optional vector<proto_lens_script_comm::PictureModel> vecPicMod;  // 画面风格
        1 optional vector<proto_lens_script_comm::VideoModel> vecViMod;     // 视频模型
        2 optional vector<proto_lens_script_comm::VideoSize> vecVideoSize;  // 视频尺寸
        3 optional vector<proto_lens_script_comm::Movement> vecMovement;    // 运镜
        5 optional vector<proto_lens_script_comm::ComposeModel> vecCompose;     // 合成视频使用的参数
        6 optional vector<proto_lens_script_comm::Region> vecRegion;     // 角色支持的地区
    };

    enum emEditType{
        // 修改主题
        EM_EDIT_TOPIC_TYPE_INSPRATION       = 1,    // 修改灵感
        EM_EDIT_TOPIC_TYPE_PICMODEL         = 2,    // 画面风格 - 图片模型
        EM_EDIT_TOPIC_TYPE_VIDMODEL         = 3,    // 视频模型
        EM_EDIT_TOPIC_TYPE_MOVEMENT         = 4,    // 视频运镜
        EM_EDIT_TOPIC_TYPE_MVSIZE           = 5,     // MV尺寸
        EM_EDIT_TOPIC_TYPE_MVTEMPLATE       = 6,    // MV模版
        EM_EDIT_TOPIC_TYPE_SONGINFO         = 7,    // 歌曲类型
        EM_EDIT_TOPIC_TYPE_REGEN            = 8,    // 重新生成灵感
        EM_EDIT_TOPIC_MODIFY_TAG            = 9,    // 修改引用图片tag
        EM_EDIT_TOPIC_MODIFY_QUALITY            = 10,    // 修改 MV 品质


        // 修改场景
        EM_EDIT_SCEN_TYPE_SCENEDESC         = 20,   // 修改场景描述
        EM_EDIT_SCEN_TYPE_ROLEID            = 21,   // 修改绑定角色
        EM_EDIT_SCEN_TYPE_ENVID             = 22,   // 修改绑定环境
        EM_EDIT_SCEN_TYPE_STORYBOARDCNT     = 23,   // 修改分镜数量
        EM_EDIT_SCEN_TYPE_DELETE            = 24,   // 删除场景
        EM_EDIT_SCEN_TYPE_ADD               = 25,   // 新增场景
        EM_EDIT_SCEN_TYPE_REGEN_SINGLE      = 26,   // 重新生成单个场景
        EM_EDIT_SCEN_TYPE_REGEN_ALL         = 27,   // 重新生成所有场景
        EM_EDIT_SCEN_TYPE_CHANGE_ORDER      = 28,  // 调换顺序
        EM_EDIT_SCEN_TYPE_REGEN_STORYBOARD  = 29,  // 重新生成某个场景的所有分镜
        EM_EDIT_SCEN_TYPE_GEN_TITLE          = 30,  // 生成场景标题-同步接口

        // 修改分镜
        EM_EDIT_STORYBOARD_TYPE_STORY       = 40,   // 修改分镜故事
        EM_EDIT_STORYBOARD_TYPE_ROLEID      = 41,   // 修改绑定角色
        EM_EDIT_STORYBOARD_TYPE_ENVID       = 42,   // 修改绑定环境
        EM_EDIT_STORYBOARD_TYPE_CHANGE_ORDER = 43,  // 调换顺序
        EM_EDIT_STORYBOARD_TYPE_DELETE      = 44,   // 删除
        EM_EDIT_STORYBOARD_TYPE_ADD         = 45,   // 添加
        EM_EDIT_STORYBOARD_TYPE_REGEN_SINGLE = 46,  // 重新生成单个分镜 -- 二期是生成单个分镜故事
        EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_PIC = 47,   // 重新生成所有分镜图片
        EM_EDIT_STORYBOARD_TYPE_REGEN_PIC   = 48,   // 生成单个图片
        EM_EDIT_STORYBOARD_TYPE_REGEN_VIDEO = 49,   // 生成单个视频
        EM_EDIT_STORYBOARD_TYPE_MODIFY_VIDEO_MODEL = 50,   // 修改视频使用模型
        EM_EDIT_STORYBOARD_TYPE_MODIFY_PIC_MODEL = 51,   // 修改图片使用模型
        EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_PIC = 52,   // 选中历史生成的图片
        EM_EDIT_STORYBOARD_TYPE_CHANGE_HISTORY_VIDEO = 53,   // 选中历史生成的视频
        EM_EDIT_STORYBOARD_TYPE_REGEN_ALL_VIDEO = 54,   // 生成剩余分镜视频
        EM_EDIT_STORYBOARD_TYPE_EDIT_VIDEO_LENGTH = 55,   // 修改视频的长度
        EM_EDIT_STORYBOARD_TYPE_GEN_STORY          = 57,  // 生成分镜故事-同步接口
        EM_EDIT_STORYBOARD_TYPE_USE_MATERIAL         = 58,  // 使用素材库

        // 角色
        EM_EDIT_ROLE_TYPE_REGEN             = 60,   // 重新生成当前角色
        EM_EDIT_ROLE_TYPE_UPLOAD            = 61,   // 上传角色
        EM_EDIT_ROLE_TYPE_AI_GEN            = 62,   // AI生成新角色
        EM_EDIT_ROLE_TYPE_GEN_ROLE_TIPS     = 63,   // 生成角色描述
        EM_EDIT_ROLE_TYPE_CHANGE_ROLE       = 64,   // 使用其他角色替换当前角色
        EM_EDIT_ROLE_TYPE_CONFIRM           = 65,   // 确认使用当前角色
        EM_EDIT_ROLE_TYPE_CHOOSE_ROLE_LIST  = 66,   // 选取当然脚本使用哪些角色
        EM_EDIT_ROLE_TYPE_DELETE_ROLE  = 67,   // 删除角色

        // 环境
        EM_EDIT_ENV_TYPE_REGEN              = 80,   // 重新生成当前角色
        EM_EDIT_ENV_TYPE_UPLOAD             = 81,   // 上传环境
        EM_EDIT_ENV_TYPE_AI_GEN            = 82,    // AI生成新环境
    };


    // CMD_DETERMINE_TOPIC 选定歌曲立即创作+修改主题
    struct DetermineTopicReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional vector<emEditType> vectorEditField;         // 调整类型
        2 optional proto_lens_script_comm::TopicInfo stTopic;       // 主题信息 首次请求只需要填充里面的歌曲信息
    };
    struct DetermineTopicRsp{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional proto_lens_script_comm::TopicInfo stTopic;       // 主题信息
    };

    // CMD_AUTO_GEN_ALL 一键生成 MV
    struct AutoGenAllReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional proto_lens_script_comm::emFlow eType;            // 从哪个流程开始一键成片
        2 optional proto_lens_script_comm::emFromSource eSource;    // 用户发起还是后台生成
    };
    struct AutoGenAllRsp{
        0 optional int iRes;                    // 执行结果
        1 optional string strTips;              // 提示语
    };

    // CMD_DO_MAIN_PROCESS 主流程生成（脚本生成、分镜画面、视频生成、视频预览）
    struct DoMainProcessReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional proto_lens_script_comm::emFlow eType;            // 流程类型
    };
    struct DoMainProcessRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    // CMD_EDIT_SCENE 场景编辑（新增/修改/重新生成/删除/分镜数量/调整角色/环境绑定)
    struct EditSceneReq{
        0 optional string strScriptId;                              // 脚本 ID
        2 optional vector<emEditType> vectorEditField;                  // 调整类型（这里到时候和 web 一起定一个 key）
        3 optional vector<proto_lens_script_comm::SceneInfo> vecScene;   // 场景信息
    };
    struct EditSceneRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
        2 optional proto_lens_script_comm::SceneInfo scene; // 场景信息
    };

    // CMD_EDIT_ROLE 角色编辑（新增/修改/删除）
    struct EditRoleReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional vector<emEditType> vectorEditField;                  // 调整类型（这里到时候和 web 一起定一个 key）
        2 optional vector<proto_lens_script_comm::RoleInfo> vecRole;         // 角色信息
    };
    struct EditRoleRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
        2 optional proto_lens_script_comm::RoleInfo stRole;         // 角色信息
    };

    // CMD_EDIT_ENV 环境编辑（新增/修改/删除）
    struct EditEnvReq{
        0 optional string strScriptId;                      // 脚本 ID
        1 optional vector<emEditType> vectorEditField;          // 调整类型（这里到时候和 web 一起定一个 key）
        2 optional proto_lens_script_comm::EnvInfo stEnv;   // 环境
    };
    struct EditEnvRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    struct MaterialItem{
        0  optional proto_lens_script_comm::VideoInfo stVideoInfo;      // 历史生成的视频
        1  optional proto_lens_script_comm::PicInfo stPicInfo;          // 历史生成的视频
        2 optional string				materialID;		            	// 对应的素材id。需要在使用素材库图片生成对应视频时，传入
    };

    // CMD_EDIT_STORYBOARD 分镜编辑
    // （图片：新增/修改/重新生成/删除/调整角色/环境绑定)
    // （视频：新增/修改/重新生成/删除）
    struct EditStoryboardReq{
        0 optional string strScriptId;                                  // 脚本 ID
        1 optional vector<emEditType> vectorEditField;                      // 调整类型（这里到时候和 web 一起定一个 key）
        2 optional vector<proto_lens_script_comm::StoryboardInfo> stBoardInfo;  // 分镜信息
        3 optional MaterialItem stMaterial;                                 // 素材库 - 新增分镜使用
    };
    struct EditStoryboardRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
        2 optional proto_lens_script_comm::StoryboardInfo     storyboard; // 分镜信息
    };

    // CMD_EDIT_TEXT 文本编辑（新增/修改/删除）
    struct EditTextReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional string strTrackId;                               // 轨道 ID
        2 optional proto_lens_script_comm::SubtitleItem stInfo;     // 字幕信息
    };
    struct EditTextRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    // CMD_EDIT_AUDIO 音频编辑（新增/修改/删除）
    struct EditAudioReq{
        0 optional string strScriptId;                              // 脚本 ID
        1 optional string strTrackId;                               // 轨道 ID
        2 optional proto_lens_script_comm::AudioItem stInfo;        // 音频信息
    };
    struct EditAudioRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    // CMD_MODITY_PERSON_INFO 修改个人信息(头像、昵称、绑定关系等)
    enum emModifyType{
        EM_MODIFY_TYPE_NICK     = 1,        // 修改昵称
        EM_MODIFY_TYPE_AVATAR   = 2,        // 修改头像链接
    };
    struct ModifyPersonInfoReq{
        0 optional proto_lens::UserInfo stUserInfo;     // 用户信息
        1 optional emModifyType eType;                  // 修改类型
    };
    struct ModifyPersonInfoRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    // CMD_MODITY_VIDEO_COVER 修改视频封面
    struct ModifyVideoCoverReq{
        0 optional string strScriptId;      // 脚本 ID
        1 optional string strCoverUrl;      // 视频封面 Url
    };
    struct ModifyVideoCoverRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    // CMD_GET_RECYCLE_INFO 获取回收站信息
    struct GetRecycleInfoReq{
        0 optional string strScriptId;      // 脚本 ID
    };

    struct GetRecycleInfoRsp{
        0 optional proto_lens_script_comm::RecycleInfo stInfo;
    };

    // CMD_REDUCTION_ITEM 还原回收站信息
    struct ReductionItemReq{
        0 optional string strScriptId;
        1 optional proto_lens_script_comm::RecycleItem stItem;
    };
    struct ReductionItemRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };

    // CMD_SW_CALLBACK
    struct SwCallbackReq{
        0 optional int iUid;                                        // 生成用户的UID
        1 optional string strScriptId;                              // 脚本ID
        2 optional string strUniqueId;                              // 唯一ID
        3 optional proto_lens_script_comm::emSubProcessType eType;  // 子流程类型
        4 optional map<string, string> mapData;                     // 流程数据
        5 optional proto_lens_script_comm::emFlow eFlow;            // 执行器属于哪个流程
        6 optional int iErrCode;                                    // 失败错误码
        7 optional string strErrMsg;                                // 失败信息
    };
    struct SwCallbackRsp{
        0 optional int iRes;
    };

    // CMD_DELETE_SCRIPT
    struct DeleteScriptReq{
        0 optional string strScriptId;                              // 脚本ID
    };
    struct DeleteScriptRsp{
        0 optional int iRes;                // 执行结果
        1 optional string strTips;          // 提示信息
    };


    struct BatchSongItem{
        0 optional string strAudioUrl;          // 音频文件 URl
        1 optional int iAudioLength;            // 歌曲时长，单位毫秒
        2 optional string strLyric;             // QRC歌词
        3 optional string strSongName;          // 歌曲名称
        4 optional string strSingerName;        // 歌手名称
        5 optional string strTargetId;          // 目标 ID
    };
    // CMD_BATCH_PRODUCTION
    struct BatchProductionReq{
        0 optional string strAppId;
        1 optional proto_lens_script_comm::emIDType eType;
        2 optional vector<string> vecId;           // K歌歌曲 ID
        3 optional vector<BatchSongItem> vecSong;   // 歌曲信息
    };
    struct BatchProductionRsp{
        0 optional int iRes;                // 执行结果
    };

    // CMD_AUDIT_RESULT
    enum emAuditStatus{
        EM_AUDIT_STATUS_SUCC = 1,               // 审核通过
        EM_AUDIT_STATUS_PIC_FAILED = 2,         // 图片不通过
        EM_AUDIT_STATUS_VIO_FAILED = 3,         // 视频不通过
    };
    struct AuditResultReq{
        0 optional emAuditStatus eStatus;
        1 optional string strRemark;    // 透传字段
    };

    struct AuditResultRsp{
        0 optional int iRes;                // 执行结果
    };

    // CMD_BACKEND_GEN
    struct BackendGenReq{
        0 optional string strScriptId;          // 脚本 ID
        1 optional proto_lens_script_comm::emIDType eIdType;
        2 optional string strId;                // 歌曲 ID
        3 optional string strDBPrimaryId;       // DB主键
        4 optional string strBatchId;           // 批次 ID
        5 optional string strExtInspiration;           // 扩展灵感
        6 optional string strProcFlowId;           // 流程 ID
        7 optional string strPicModel;             // 指定图片模型
        8 optional string strAppId;                // 业务的 APPid
        9 optional BatchSongItem stSongInfo;        // 歌曲信息
    };
    struct BackendGenRsp{
        0 optional int iRes;                // 执行结果
    };

    // CMD_PUSH_MV_URL
    struct MvUrlItem{
        0 optional proto_lens_script_comm::emIDType eIdType;
        1 optional string strId;                // 歌曲 ID
        2 optional string strMvUrl;             // MvUrl
        3 optional string strAppId;             // AppId
    };
    struct PushMvUrlReq{
        0 optional vector<MvUrlItem> vecInfo;   // 可以批量推送
    };
    struct PushMvUrlRsp{
        0 optional int iRes;                // 执行结果
    };

    // CMD_PICTURE_ANALYZE
    struct PicAnalyzeReq{
        0 optional string strScriptId;                              // 脚本ID
        1 optional vector<string> vecPic;                      // 图片文件
    };
    struct PicAnalyzeRsp{
        0 optional vector<string> vecTag;                           // 图片标签
    };

    // CMD_GET_MATERIAL_LIST
    struct GetMaterialListReq{
        0 optional string strScriptId;                              // 脚本ID
        1 optional string strPicTips;                               // 图片提示词
    };
    struct GetMaterialListRsp{
        0 optional vector<MaterialItem> vecInfo;
    };

    // CMD_GET_ROLE_INFO
    struct GetRoleInfoReq{
        0 optional string strScriptId;                              // 脚本ID
        1 optional string strRoleId;                                    // 角色 ID
    };
    struct GetRoleInfoRsp{
        0 optional proto_lens_script_comm::RoleInfo stRoleInfo;         // 角色信息
    };

    // CMD_GET_AUDIO_URL
    struct AudioUrlReq{
        0 optional string strSongId;        // 歌曲 ID
    };

    struct AudioUrlRsp{
        0 optional string strAudioUrl;      // 音频链接
    };
};

