{"name": "lens", "version": "1.0.0", "description": "棱镜", "main": "universal.build.config.js", "scripts": {"format:less": "prettier './projects/**/*.less' --write", "preinstall": "node -e \"!process.env.DOCKER_CI_BUILD && require('./scripts/stopNpm.js')\"", "prepare": "husky install & node -e \"!process.env.DOCKER_CI_BUILD && require('./scripts/stopNpm.js')\"", "tscheck": "tsc   --noEmit", "lint": "eslint ./projects --ext .ts,.tsx  --quiet", "lint:fix": "eslint ./projects --ext .ts,.tsx --fix  --quiet", "build": "tme-build build", "build:analyze": "tme-build build --analyze", "build:us": "universal-build build", "build-info": "echo 'no need build info'", "build-info:us": "universal-build query-dll-deps", "dev": "cross-env NODE_ENV=development tme-build dev --react=18", "dev:proxy": "auto-proxy setup -e 'npm run dev'", "dev:us": "cross-env NODE_ENV=development universal-build dev --react=18", "update-style": "git submodule init && git submodule update --remote", "log": "useful rtl", "jce": "yarn interact-jce-cli update", "across-jce-cli": "node node_modules/@tencent/kg-jce-cli/bin/index.js --git-repository \"https://git.woa.com/tme/across_jce.git\" --jce-resource \"./jce/across\" --jce-output \"./output/jce/national/across\" --jce-tsd \"./jce-tsd/across\" --git-cache-dir \"./.cache/kg-jce-cli/across-jce-git\" --transform-require-to-plug", "interact-jce-cli": "node node_modules/@tencent/kg-jce-cli/bin/index.js --git-repository \"https://git.woa.com/tme/tme_interact/interact_jce.git\" --jce-resource \"./jce/interact\" --jce-output \"./output/jce/national/interact\" --jce-tsd \"./jce-tsd/interact\" --git-cache-dir \"./.cache/kg-jce-cli/interact-jce-git\" --transform-require-to-plug", "jce-cli": "node node_modules/@tencent/kg-jce-cli/bin/index.js --git-repository \"https://git.woa.com/tme/kg_jce.git\" --jce-resource \"./jce/kg\" --jce-output \"./output/jce/national\" --jce-tsd \"./jce-tsd/kg\" --transform-require-to-plug", "ark-jce-cli": "node node_modules/@tencent/kg-jce-cli/bin/index.js --git-repository \"https://git.woa.com/MusicCommercial/ark/arkjce.git\" --jce-resource \"./jce/ark\" --jce-output \"./output/jce/national/ark\" --jce-tsd \"./jce-tsd/ark\" --git-cache-dir \"./.cache/kg-jce-cli/ark-jce-git\" --transform-require-to-plug", "stylelint": "stylelint \"projects/**/*.{css,less}\" --fix", "imagemin": "node scripts/imagemin.js"}, "license": "ISC", "lint-staged": {"*.{js,jsx,ts,tsx}": ["yarn lint", "prettier --write", "git add"]}, "dependencies": {"@emotion/react": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@mantine/core": "^7.16.3", "@mantine/dropzone": "^7.16.3", "@mantine/form": "^7.16.3", "@mantine/hooks": "^7.16.3", "@mantine/modals": "^7.17.2", "@mantine/notifications": "^7.16.3", "@tabler/icons-react": "^3.30.0", "@tencent/fast-jce-plugin": "^0.3.5", "@tencent/jce-universal-build-plugin": "^1.13.0", "@tencent/kg-ajax": "^0.60.1", "@tencent/kg-base": "^0.44.9", "@tencent/kg-jce-cli": "^1.8.0", "@tencent/kg-report": "^0.87.20", "@tencent/kg-ui": "^2.41.0", "@tencent/kui-react": "^0.0.20", "@tencent/qmfe-sdk-cgi": "^1.1.3", "@tencent/qmkege-ajax": "^0.38.1", "@tencent/qnu-client": "^1.9.35", "@tencent/tme-build": "^0.7.0", "@tencent/tme-interact-common-report": "^1.0.2", "@tencent/tme-interaction-common": "^1.5.1", "@tencent/tme-interaction-component": "^1.5.12", "@tencent/tme-interaction-network": "^1.5.0", "@tencent/tme-interaction-ui": "^1.5.0", "@tencent/tme-interface-qmkege": "^1.0.421", "@tencent/tmeoa-auth-client": "^1.12.0", "@tencent/useful-plugins": "^0.5.3", "@webav/av-canvas": "^1.0.15", "@webav/av-cliper": "^1.0.15", "@xzdarcy/react-timeline-editor": "^0.1.9", "ahooks": "^3.7.8", "antd": "^5.8.4", "axios": "^1.7.9", "browser-md5-file": "^1.1.1", "classnames": "^2.5.1", "cos-js-sdk-v5": "^1.8.1", "dayjs": "^1.11.9", "fs-extra": "^11.1.1", "immer": "^10.1.1", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-countup": "^6.5.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-virtualized": "^9.22.5", "redux": "^4.2.1", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "17", "@commitlint/config-conventional": "17", "@tencent/autoproxy": "^1.1.3", "@types/lodash-es": "^4.17.8", "@types/react": "^18.2.20", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.7", "@types/react-redux": "^7.1.25", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.0", "imagemin": "^9.0.0", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^10.0.0", "imagemin-webp": "^8.0.0", "less": "^4.1.3", "less-loader": "^11.1.0", "lint-staged": "14", "postcss": "^8.4.21", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.1.1", "style-loader": "^4.0.0", "stylelint": "^16.14.1", "stylelint-config-standard": "^37.0.0", "typescript": "^4.9.5", "webpack": "^5.97.1", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1"}}