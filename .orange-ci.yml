# === !!! DON'T EDIT THIS FILE !!! ===
# === GENERATED BY @tencent/ocg ===
# === (config from: node_modules/.bin/kg-ci-remote-cli) ===

get-git-token: &get-git-token
  - name: get git token
    type: git:set-token
    imports: https://git.woa.com/karaoke-web/ci-envs/blob/master/base.yml # 从私有仓库引入TGIT_PERSONAL_ACCESS_TOKEN
    options:
      token: ${TGIT_PERSONAL_ACCESS_TOKEN}
      name: karaoke_web_proj
      email: <EMAIL>

git-submodule-update: &git-submodule-update
  - name: git submodule update --remote
    script:
      - git submodule update --init --recursive --remote -f

master|release_*:
  merge_request:
    - git:
        lfs: true
        submodules: true
        dotGit: true
      runner:
        network: devnet
      docker:
        image: node
      services:
        - docker
      stages:
        - name: get cli version
          jobs:
            - name: check version
              image: csighub.tencentyun.com/kgdocker/plugin-check-image-version
              exports:
                result: CLI_VERSION_RESULT
              settings:
                image_name: plugin-kg-ci-remote-cli
                image_namespace: kgdocker
                output_path: __cliversion__
        - name: install
          jobs:
            - name: docker cache
              type: 'docker:cache'
              exports:
                name: DOCKER_CACHE_IMAGE_NAME
              options:
                dockerfile: Dockerfile.cache
                by:
                  - yarn.lock
                  - .npmrc
                  - package.json
                  - __cliversion__
                buildArgs:
                  CLI_VERSION: $CLI_VERSION_RESULT
        - name: docker cache done
          jobs:
            - name: resolve docker cache
              type: 'orange-ci:resolve'
              options:
                key: dockerCacheReady
        - name: diff
          jobs:
            - name: diff
              type: 'git:changeList'
              options:
                changed: changed.txt
                deleted: deleted.txt
                ignoreSubmodule: true
            - name: get commit list
              type: 'git:commit-list'
              options:
                toFile: commitList.txt
        - name: prepare
          jobs:
            - name: 初始化构建环境(check changed projects)
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli prepare --output=build-prepare.json
            - name: 初始化构建环境(export env)
              type: 'orange-ci:readFile'
              exports:
                projects: CHECK_PROJECTS
                workspace: WORKSPACE
                shouldBuild: SHOULD_BUILD
                sourceBranch: SOURCE_BRANCH
                currentBranch: CURRENT_BRANCH
                tapdIds: TAPD_IDS
              options:
                filePath: build-prepare.json
        - name: 创建任务
          if:
            - 'if [ "$SHOULD_BUILD" = "true" ]; then exit 0; else exit 1; fi'
          jobs:
            - name: create job
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli create --specify-job=$SPECIFY_JOB
                  --projects=$CHECK_PROJECTS --source-branch=$SOURCE_BRANCH
                  --output=build-info.json --changed-record=changed.txt
                  --deleted-record=deleted.txt
                  --ignore-projects-from=.projectsignore
        - name: 初始化构建环境(export env)
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: read env from build-info.json
              type: 'orange-ci:readFile'
              exports:
                ticket: TICKET
                repo_id: REPO_ID
                build_version: VERSION
                build_count: BUILD_COUNT
                build_projects: PROJECTS
                cos_upload_key: COS_UPLOAD_NAME
                cos_options.appid: COS_APPID
                cos_options.secret_id: COS_ID
                cos_options.secret_key: COS_KEY
                cos_options.base_host: COS_HOST
                receive_cos: COS_UPLOAD_BUCKET
                backup_cos: COS_DOWNLOAD_BUCKET
              options:
                filePath: build-info.json
            - name: send message
              type: 'wework:message'
              options:
                message: '执行构建任务:${VERSION}. 当前分支:${CURRENT_BRANCH}'
        - name: 构建前检查
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: build precheck
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - kg-ci-remote-cli exec --cmd=precheck --input=build-info.json
            - name: begin build
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - >-
                  kg-ci-remote-cli begin --ticket=$TICKET
                  --precheck=precheck-result.json --output=deps-info.json
            - name: begin build (only get files)
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  !require(b).build_projects.length &&
                  require(b).unbuild_files.length ? 0 : 1)"
              commands:
                - >-
                  kg-ci-remote-cli begin --ticket=$TICKET
                  --output=deps-info.json
            - name: 确认构建 projects
              type: 'orange-ci:readFile'
              exports:
                build_projects: PROJECTS
              options:
                filePath: deps-info.json
            - name: download dll to workspace
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli batchdownload --input=deps-info.json
                  --repoid=$REPO_ID --downloadpath=$WORKSPACE
                  --cosappid=$COS_APPID --cossecretid=$COS_ID
                  --cossecretkey=$COS_KEY --cosbasehost=$COS_HOST
                  --cosbucket=$COS_DOWNLOAD_BUCKET
            - name: send message
              type: 'wework:message'
              options:
                message: >-
                  开始自动构建:${VERSION}(第${BUILD_COUNT}次). 当前分支:${CURRENT_BRANCH} 
                  业务:${PROJECTS}
        - name: 开始构建
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: start build step
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - kg-ci-remote-cli exec --cmd=script --input=build-info.json
            - name: fetch unbuild files
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli get-files --input=build-info.json
        - name: 上传产物
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: send message
              type: 'wework:message'
              options:
                message: '构建完成, 上传产物'
            - name: tarball
              script:
                - tar -czvf output.tar.gz -C $WORKSPACE .
            - name: transfer to cos
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli upload --filename=output.tar.gz
                  --cosfilename=$COS_UPLOAD_NAME --cosappid=$COS_APPID
                  --cossecretid=$COS_ID --cossecretkey=$COS_KEY
                  --cosbasehost=$COS_HOST --cosbucket=$COS_UPLOAD_BUCKET
        - name: 结束构建
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: end build status
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli end --ticket=$TICKET
      failStages:
        - name: 构建失败
          jobs:
            - name: end with error
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli error --specify-job=$SPECIFY_JOB
                  --ticket=$TICKET
  push:
    - git:
        lfs: true
        submodules: true
        dotGit: true
      runner:
        network: devnet
      docker:
        image: node
      env:
        IS_MR_COMPLETED: 1
      services:
        - docker
      stages:
        - *get-git-token
        - *git-submodule-update
        - name: get cli version
          jobs:
            - name: check version
              image: csighub.tencentyun.com/kgdocker/plugin-check-image-version
              exports:
                result: CLI_VERSION_RESULT
              settings:
                image_name: plugin-kg-ci-remote-cli
                image_namespace: kgdocker
                output_path: __cliversion__
        - name: install
          jobs:
            - name: docker cache
              type: 'docker:cache'
              exports:
                name: DOCKER_CACHE_IMAGE_NAME
              options:
                dockerfile: Dockerfile.cache
                by:
                  - yarn.lock
                  - .npmrc
                  - package.json
                  - __cliversion__
                buildArgs:
                  CLI_VERSION: $CLI_VERSION_RESULT
        - name: docker cache done
          jobs:
            - name: resolve docker cache
              type: 'orange-ci:resolve'
              options:
                key: dockerCacheReady
        - name: prepare
          jobs:
            - name: 初始化构建环境(check changed projects)
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli prepare --output=build-prepare.json
            - name: 初始化构建环境(export env)
              type: 'orange-ci:readFile'
              exports:
                projects: CHECK_PROJECTS
                workspace: WORKSPACE
                shouldBuild: SHOULD_BUILD
                sourceBranch: SOURCE_BRANCH
                currentBranch: CURRENT_BRANCH
                tapdIds: TAPD_IDS
              options:
                filePath: build-prepare.json
        - name: 创建任务
          if:
            - 'if [ "$SHOULD_BUILD" = "true" ]; then exit 0; else exit 1; fi'
          jobs:
            - name: create job
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli create --specify-job=$SPECIFY_JOB
                  --projects=$CHECK_PROJECTS --source-branch=$SOURCE_BRANCH
                  --output=build-info.json --changed-record=changed.txt
                  --deleted-record=deleted.txt
                  --ignore-projects-from=.projectsignore
        - name: 初始化构建环境(export env)
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: read env from build-info.json
              type: 'orange-ci:readFile'
              exports:
                ticket: TICKET
                repo_id: REPO_ID
                build_version: VERSION
                build_count: BUILD_COUNT
                build_projects: PROJECTS
                cos_upload_key: COS_UPLOAD_NAME
                cos_options.appid: COS_APPID
                cos_options.secret_id: COS_ID
                cos_options.secret_key: COS_KEY
                cos_options.base_host: COS_HOST
                receive_cos: COS_UPLOAD_BUCKET
                backup_cos: COS_DOWNLOAD_BUCKET
              options:
                filePath: build-info.json
            - name: send message
              type: 'wework:message'
              options:
                message: '执行构建任务:${VERSION}. 当前分支:${CURRENT_BRANCH}'
        - name: 构建前检查
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: build precheck
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - kg-ci-remote-cli exec --cmd=precheck --input=build-info.json
            - name: begin build
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - >-
                  kg-ci-remote-cli begin --ticket=$TICKET
                  --precheck=precheck-result.json --output=deps-info.json
            - name: begin build (only get files)
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  !require(b).build_projects.length &&
                  require(b).unbuild_files.length ? 0 : 1)"
              commands:
                - >-
                  kg-ci-remote-cli begin --ticket=$TICKET
                  --output=deps-info.json
            - name: 确认构建 projects
              type: 'orange-ci:readFile'
              exports:
                build_projects: PROJECTS
              options:
                filePath: deps-info.json
            - name: download dll to workspace
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli batchdownload --input=deps-info.json
                  --repoid=$REPO_ID --downloadpath=$WORKSPACE
                  --cosappid=$COS_APPID --cossecretid=$COS_ID
                  --cossecretkey=$COS_KEY --cosbasehost=$COS_HOST
                  --cosbucket=$COS_DOWNLOAD_BUCKET
            - name: send message
              type: 'wework:message'
              options:
                message: >-
                  开始自动构建:${VERSION}(第${BUILD_COUNT}次). 当前分支:${CURRENT_BRANCH} 
                  业务:${PROJECTS}
        - name: 开始构建
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: start build step
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - kg-ci-remote-cli exec --cmd=script --input=build-info.json
            - name: fetch unbuild files
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli get-files --input=build-info.json
        - name: 上传产物
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: send message
              type: 'wework:message'
              options:
                message: '构建完成, 上传产物'
            - name: tarball
              script:
                - tar -czvf output.tar.gz -C $WORKSPACE .
            - name: transfer to cos
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli upload --filename=output.tar.gz
                  --cosfilename=$COS_UPLOAD_NAME --cosappid=$COS_APPID
                  --cossecretid=$COS_ID --cossecretkey=$COS_KEY
                  --cosbasehost=$COS_HOST --cosbucket=$COS_UPLOAD_BUCKET
        - name: 结束构建
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: end build status
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli end --ticket=$TICKET
      failStages:
        - name: 构建失败
          jobs:
            - name: end with error
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli error --specify-job=$SPECIFY_JOB
                  --ticket=$TICKET
$:
  api_trigger_build:
    - git:
        lfs: true
        submodules: true
        dotGit: true
      wework:
        title: >-
          自动构建$SPECIFY_JOB($CURRENT_BRANCH) $JOB_COMMENTS 第$JOB_BUILD_COUNT次
          $USER_NAME
      runner:
        network: devnet
      docker:
        image: node
      services:
        - docker
      stages:
        - *get-git-token
        - *git-submodule-update
        - name: get cli version
          jobs:
            - name: check version
              image: csighub.tencentyun.com/kgdocker/plugin-check-image-version
              exports:
                result: CLI_VERSION_RESULT
              settings:
                image_name: plugin-kg-ci-remote-cli
                image_namespace: kgdocker
                output_path: __cliversion__
        - name: install
          jobs:
            - name: docker cache
              type: 'docker:cache'
              exports:
                name: DOCKER_CACHE_IMAGE_NAME
              options:
                dockerfile: Dockerfile.cache
                by:
                  - yarn.lock
                  - .npmrc
                  - package.json
                  - __cliversion__
                buildArgs:
                  CLI_VERSION: $CLI_VERSION_RESULT
        - name: prepare
          jobs:
            - name: 初始化构建环境(check changed projects)
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli prepare --output=build-prepare.json
            - name: 初始化构建环境(export env)
              type: 'orange-ci:readFile'
              exports:
                projects: CHECK_PROJECTS
                workspace: WORKSPACE
                shouldBuild: SHOULD_BUILD
                sourceBranch: SOURCE_BRANCH
                currentBranch: CURRENT_BRANCH
                tapdIds: TAPD_IDS
              options:
                filePath: build-prepare.json
        - name: 创建任务
          if:
            - 'if [ "$SHOULD_BUILD" = "true" ]; then exit 0; else exit 1; fi'
          jobs:
            - name: create job
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli create --specify-job=$SPECIFY_JOB
                  --projects=$CHECK_PROJECTS --source-branch=$SOURCE_BRANCH
                  --output=build-info.json --changed-record=changed.txt
                  --deleted-record=deleted.txt
                  --ignore-projects-from=.projectsignore
        - name: 初始化构建环境(export env)
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: read env from build-info.json
              type: 'orange-ci:readFile'
              exports:
                ticket: TICKET
                repo_id: REPO_ID
                build_version: VERSION
                build_count: BUILD_COUNT
                build_projects: PROJECTS
                cos_upload_key: COS_UPLOAD_NAME
                cos_options.appid: COS_APPID
                cos_options.secret_id: COS_ID
                cos_options.secret_key: COS_KEY
                cos_options.base_host: COS_HOST
                receive_cos: COS_UPLOAD_BUCKET
                backup_cos: COS_DOWNLOAD_BUCKET
              options:
                filePath: build-info.json
            - name: send message
              type: 'wework:message'
              options:
                message: '执行构建任务:${VERSION}. 当前分支:${CURRENT_BRANCH}'
        - name: 构建前检查
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: build precheck
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - kg-ci-remote-cli exec --cmd=precheck --input=build-info.json
            - name: begin build
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - >-
                  kg-ci-remote-cli begin --ticket=$TICKET
                  --precheck=precheck-result.json --output=deps-info.json
            - name: begin build (only get files)
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  !require(b).build_projects.length &&
                  require(b).unbuild_files.length ? 0 : 1)"
              commands:
                - >-
                  kg-ci-remote-cli begin --ticket=$TICKET
                  --output=deps-info.json
            - name: 确认构建 projects
              type: 'orange-ci:readFile'
              exports:
                build_projects: PROJECTS
              options:
                filePath: deps-info.json
            - name: download dll to workspace
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli batchdownload --input=deps-info.json
                  --repoid=$REPO_ID --downloadpath=$WORKSPACE
                  --cosappid=$COS_APPID --cossecretid=$COS_ID
                  --cossecretkey=$COS_KEY --cosbasehost=$COS_HOST
                  --cosbucket=$COS_DOWNLOAD_BUCKET
            - name: send message
              type: 'wework:message'
              options:
                message: >-
                  开始自动构建:${VERSION}(第${BUILD_COUNT}次). 当前分支:${CURRENT_BRANCH} 
                  业务:${PROJECTS}
        - name: 开始构建
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: start build step
              image: $DOCKER_CACHE_IMAGE_NAME
              if:
                - >-
                  node -pe "var b = './build-info.json';
                  process.exit(fs.existsSync(b) &&
                  require(b).build_projects.length ? 0 : 1)"
              commands:
                - kg-ci-remote-cli exec --cmd=script --input=build-info.json
            - name: fetch unbuild files
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli get-files --input=build-info.json
        - name: 上传产物
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: send message
              type: 'wework:message'
              options:
                message: '构建完成, 上传产物'
            - name: tarball
              script:
                - tar -czvf output.tar.gz -C $WORKSPACE .
            - name: transfer to cos
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli upload --filename=output.tar.gz
                  --cosfilename=$COS_UPLOAD_NAME --cosappid=$COS_APPID
                  --cossecretid=$COS_ID --cossecretkey=$COS_KEY
                  --cosbasehost=$COS_HOST --cosbucket=$COS_UPLOAD_BUCKET
        - name: 结束构建
          if:
            - >-
              node -pe "var b = './build-info.json';
              process.exit(fs.existsSync(b) && (require(b).build_projects.length
              || require(b).unbuild_files.length) ? 0 : 1)"
          jobs:
            - name: end build status
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - kg-ci-remote-cli end --ticket=$TICKET
      failStages:
        - name: 构建失败
          jobs:
            - name: end with error
              image: $DOCKER_CACHE_IMAGE_NAME
              commands:
                - >-
                  kg-ci-remote-cli error --specify-job=$SPECIFY_JOB
                  --ticket=$TICKET


# === !!! DON'T EDIT THIS FILE !!! ===
# === GENERATED BY @tencent/ocg ===
# === (config from: node_modules/.bin/kg-ci-remote-cli) ===
