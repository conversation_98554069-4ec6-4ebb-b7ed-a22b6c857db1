* [@webav/av-canvas](https://webav-tech.github.io/WebAV/_api/av-canvas/modules.html)
* [AVCanvas](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html)

# Class AVCanvas

一个可交互的画布，让用户添加各种素材，支持基础交互（拖拽、缩放、旋转、时间偏移）

用于在 Web 环境中实现视频剪辑、直播推流工作台功能

#### Description

* 添加/删除素材（视频、音频、图片、文字）
* 分割（裁剪）素材
* 控制素材在视频中的空间属性（坐标、旋转、缩放）
* 控制素材在视频中的时间属性（偏移、时长）
* 实时预览播放
* 纯浏览器环境生成视频

#### See

* [直播录制](https://webav-tech.github.io/WebAV/demo/4_2-recorder-avcanvas)
* [视频剪辑](https://webav-tech.github.io/WebAV/demo/6_4-video-editor)

#### Example

```ts
const avCvs = new AVCanvas(document.querySelector('#app'), {
  bgColor: '#333',
  width: 1920,
  height: 1080,
});
Copy
```

* Defined in [av-canvas/src/av-canvas.ts:68](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L68)

<details class="tsd-index-content tsd-index-accordion" open="" data-has-instance="true"><summary class="tsd-accordion-summary tsd-index-summary"><h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabindex="0"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><use href="../assets/icons.svg#icon-chevronSmall"></use></svg><span> </span>Index</h5></summary>

### Constructors

[constructor](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#constructor)

### Properties

[addSprite](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#addSprite)[on](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on)[removeSprite](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#removeSprite)

### Accessors

[activeSprite](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#activeSprite)

### Methods

[captureImage](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#captureImage)[captureStream](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#captureStream)[createCombinator](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#createCombinator)[destroy](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#destroy)[pause](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#pause)[play](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#play)[previewFrame](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#previewFrame)

</details>

## Constructors

### constructor

* **new AVCanvas**(**attchEl**, **opts**)**:** [AVCanvas](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html)[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#constructor.new_AVCanvas)
* 创建 `AVCanvas` 类的实例。
  
  #### Parameters
  
  * **attchEl**: **HTMLElement**
    要添加画布的元素。
  * **opts**: **{**
    **bgColor**: **string**;
    } **&** **IResolution**
    画布的选项
  
  #### Returns [AVCanvas](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html)
  
  * Defined in [av-canvas/src/av-canvas.ts:98](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L98)

## Properties

### addSprite

**add**Sprite**:** **(**(**vs**) **=>** **Promise**<**void**>**)** **= ...**

添加 VisibleSprite

#### Type declaration

* * **(**vs**)**: **Promise**<**void**>
  * #### Parameters
    
    * **vs**: **VisibleSprite**
    
    #### Returns **Promise**<**void**>

#### Example

```ts
const sprite = new VisibleSprite(
  new ImgClip({
    type: 'image/gif',
    stream: (await fetch('https://xx.gif')).body!,
  }),
);
Copy
```

* Defined in [av-canvas/src/av-canvas.ts:330](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L330)

### on

**on**: **(**[[Type](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on.__type-5.__type-6.Type)](%5BType%5D(https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on.__type-5.__type-6.Type))**(**type, **listener**) **=>** **(**(**)** **=>** **void**)**)** **= ...**

#### Type declaration

* * **[**[Type](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on.__type-5.__type-6.Type)](**%5BType%5D(https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on.__type-5.__type-6.Type))**(**type, **listener**)**:** **(**(**)** **=>** **void**)
  * 监听 EventType 中定义的事件
    
    #### Type Parameters
    
    * **Type** **extends** **"activeSpriteChange"** **|** **"playing"** **|** **"timeupdate"** **|** **"paused"**
    
    #### Parameters
    
    * **type**: [Type](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on.__type-5.__type-6.Type)
    * **listener**: **{**
      **activeSpriteChange**: **(**(**sprite**) **=>** **void**)**;**
      **paused**: **(**(**)** **=>** **void**)**;**
      **playing**: **(**(**)** **=>** **void**)**;**
      **timeupdate**: **(**(**time**) **=>** **void**)**;**
      }**[**[Type](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#on.__type-5.__type-6.Type)]
    
    #### Returns **(**(**)** **=>** **void**)
    
    * * **(**)**:** **void**
      * #### Returns **void**
* Defined in [av-canvas/src/av-canvas.ts:86](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L86)

### removeSprite

**remove**Sprite**:** **(**(**spr**) **=>** **void**) **= ...**

删除 VisibleSprite

#### Type declaration

* * **(**spr**)**: **void**
  * #### Parameters
    
    * **spr**: **VisibleSprite**
    
    #### Returns **void**

#### Returns

#### Example

```ts
const sprite = new VisibleSprite();
avCvs.removeSprite(sprite);
Copy
```

* Defined in [av-canvas/src/av-canvas.ts:353](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L353)

## Accessors

### activeSprite

* **get** activeSprite**(**)**:** **null** **|** **VisibleSprite**
* #### Returns **null** **|** **VisibleSprite**
  
  * Defined in [av-canvas/src/av-canvas.ts:311](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L311)
* **set** activeSprite**(**s**)**: **void**
* #### Parameters
  
  * **s**: **null** **|** **VisibleSprite**
  
  #### Returns **void**
  
  * Defined in [av-canvas/src/av-canvas.ts:314](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L314)

## Methods

### captureImage

* **capture**Image**(**)**:** **string**[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#captureImage.captureImage-1)
* 获取当前帧的截图图像 返回的是一个base64
  
  #### Returns **string**
  
  * Defined in [av-canvas/src/av-canvas.ts:307](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L307)

### captureStream

* **capture**Stream**(**)**:** **MediaStream**[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#captureStream.captureStream-1)
* 合成所有素材的图像与音频，返回实时媒体流 `MediaStream`
  
  可用于 WebRTC 推流，或由 [AVRecorder](https://webav-tech.github.io/WebAV/_api/av-recorder/classes/AVRecorder.html) 录制生成视频文件
  
  #### Returns **MediaStream**
  
  #### See
  
  [直播录制](https://webav-tech.github.io/WebAV/demo/4_2-recorder-avcanvas)
  
  * Defined in [av-canvas/src/av-canvas.ts:383](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L383)

### createCombinator

* **create**Combinator**(**opts**?**)**:** **Promise**<**Combinator**>[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#createCombinator.createCombinator-1)
* 创建一个视频合成器 [Combinator](https://webav-tech.github.io/WebAV/_api/av-cliper/classes/Combinator.html) 实例，用于将当前画布添加的 Sprite 导出为视频文件流
  
  #### Parameters
  
  * **opts**: **ICombinatorOpts** **= {}**
    创建 Combinator 的可选参数
  
  #### Returns **Promise**<**Combinator**>
  
  #### Throws
  
  如果没有添加素材，会抛出错误
  
  #### Example
  
  ```ts
  avCvs.createCombinator().output() // => ReadableStream
  Copy
  ```
  
  #### See
  
  [视频剪辑](https://webav-tech.github.io/WebAV/demo/6_4-video-editor)
  
  * Defined in [av-canvas/src/av-canvas.ts:412](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L412)

### destroy

* **destroy**(**)**: **void**[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#destroy.destroy-1)
* 销毁实例
  
  #### Returns **void**
  
  * Defined in [av-canvas/src/av-canvas.ts:361](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L361)

### pause

* **pause**(**)**: **void**[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#pause.pause-1)
* 暂停播放，画布内容不再更新
  
  #### Returns **void**
  
  * Defined in [av-canvas/src/av-canvas.ts:292](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L292)

### play

* **play**(**opts**)**:** **void**[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#play.play-1)
* 每 33ms 更新一次画布，绘制已添加的 Sprite
  
  #### Parameters
  
  * **opts**: **{**
    **end**?: **number**;
    **playbackRate**?: **number**;
    **start**: **number**;
    }
    播放选项
    
    * ##### `Optional` **end**?: **number**
      
      结束播放的时间（单位：微秒）。如果未指定，则播放到最后一个 Sprite 的结束时间
    * ##### `Optional` **playback**Rate**?:** **number**
      
      播放速率。1 表示正常速度，2 表示两倍速度，0.5 表示半速等。如果未指定，则默认为 1
    * ##### start**:** **number**
      
      开始播放的时间（单位：微秒）
  
  #### Returns **void**
  
  #### Throws
  
  如果开始时间大于等于结束时间或小于 0，则抛出错误
  
  * Defined in [av-canvas/src/av-canvas.ts:257](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L257)

### previewFrame

* **preview**Frame**(**time**)**: **void**[](https://webav-tech.github.io/WebAV/_api/av-canvas/classes/AVCanvas.html#previewFrame.previewFrame-1)
* 预览 `AVCanvas` 指定时间的图像帧
  
  #### Parameters
  
  * **time**: **number**
  
  #### Returns **void**
  
  * Defined in [av-canvas/src/av-canvas.ts:299](https://github.com/WebAV-Tech/WebAV/blob/9a7fd1aafdae51014441c0099dbe7b22e7efa135/packages/av-canvas/src/av-canvas.ts#L299)

