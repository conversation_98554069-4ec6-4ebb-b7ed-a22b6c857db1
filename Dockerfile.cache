ARG CLI_VERSION

FROM csighub.tencentyun.com/${CLI_VERSION}

WORKDIR /cache/

LABEL maintainer="rowanyang"
LABEL descrition="棱镜缓存"

# 完全移除旧版本的 Node.js 和相关组件
RUN yum remove -y nodejs npm \
    && rm -rf /usr/local/bin/node \
    && rm -rf /usr/local/bin/npm \
    && rm -rf /usr/local/lib/node_modules \
    && rm -rf ~/.npm \
    && rm -rf /opt/nodejs \
    && rm -rf /usr/local/include/node

# 清理 NVM 安装
RUN rm -rf ~/.nvm \
    && sed -i '/NVM_DIR/d' ~/.bashrc \
    && sed -i '/nvm.sh/d' ~/.bashrc

# 禁用默认的 nodejs 模块
RUN yum module disable -y nodejs

# 安装必要工具
RUN yum install -y curl

# 使用官方脚本安装 Node.js 18
RUN curl -fsSL https://rpm.nodesource.com/setup_18.x | bash - \
    && yum install -y nodejs

# 设置 Node.js 使用旧版加密算法
ENV NODE_OPTIONS=--openssl-legacy-provider

# 验证 Node.js 版本和安装路径
RUN which node && node -v && which npm && npm -v

# 配置 npm 使用腾讯镜像源并安装 yarn
RUN npm config set registry https://mirrors.tencent.com/npm/ \
    && npm install -g yarn \
    && yarn config set registry https://mirrors.tencent.com/npm/

COPY ./package.json .
COPY ./yarn.lock .
COPY ./.npmrc .
ENV DOCKER_CI_BUILD=true
ENV SASS_BINARY_SITE=http://mirrors.tencent.com/npm_mirrors/node-sass
RUN yarn
